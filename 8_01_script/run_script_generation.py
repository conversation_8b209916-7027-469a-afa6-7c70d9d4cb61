#!/usr/bin/env python3
"""
一键运行剧本生成系统
"""

import logging
import sys
from pathlib import Path
from script_generator import ScriptGenerator
from batch_script_generator import BatchScriptGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_files():
    """检查必要文件是否存在"""
    logger.info("🔍 检查必要文件...")
    
    required_files = [
        "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json",
        "/data2/jevon/8_01_script/data/mash_up_script.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error("❌ 缺少必要文件:")
        for file_path in missing_files:
            logger.error(f"  - {file_path}")
        return False
    
    logger.info("✅ 所有必要文件检查通过")
    return True

def run_single_script_generation():
    """运行单个综合剧本生成"""
    logger.info("📝 开始生成综合剧本...")
    
    classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
    prompt_file = "/data2/jevon/8_01_script/data/mash_up_script.md"
    output_file = "/data2/jevon/8_01_script/output/comprehensive_script.md"
    
    generator = ScriptGenerator()
    success = generator.generate_script(
        classification_file=classification_file,
        prompt_file=prompt_file,
        output_file=output_file,
        max_videos=15
    )
    
    return success

def run_batch_script_generation():
    """运行批量类别剧本生成"""
    logger.info("📚 开始批量生成类别专门剧本...")
    
    classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
    prompt_file = "/data2/jevon/8_01_script/data/mash_up_script.md"
    output_dir = "/data2/jevon/8_01_script/output"
    
    batch_generator = BatchScriptGenerator()
    success = batch_generator.generate_category_scripts(
        classification_file=classification_file,
        prompt_file=prompt_file,
        output_dir=output_dir
    )
    
    return success

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("🎬 视频剧本生成系统")
    print("="*60)
    print("1. 生成综合剧本 (基于最佳15个视频)")
    print("2. 批量生成类别专门剧本 (按6个类别分别生成)")
    print("3. 全部生成 (综合剧本 + 类别剧本)")
    print("4. 退出")
    print("="*60)

def main():
    """主函数"""
    logger.info("🚀 启动视频剧本生成系统...")
    
    # 检查必要文件
    if not check_files():
        logger.error("❌ 文件检查失败，程序退出")
        sys.exit(1)
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (1-4): ").strip()
            
            if choice == "1":
                logger.info("🎯 选择: 生成综合剧本")
                success = run_single_script_generation()
                if success:
                    logger.info("✅ 综合剧本生成完成！")
                    logger.info("📄 查看文件: /data2/jevon/8_01_script/output/comprehensive_script.md")
                else:
                    logger.error("❌ 综合剧本生成失败")
            
            elif choice == "2":
                logger.info("🎯 选择: 批量生成类别专门剧本")
                success = run_batch_script_generation()
                if success:
                    logger.info("✅ 批量剧本生成完成！")
                    logger.info("📁 查看文件夹: /data2/jevon/8_01_script/output/")
                else:
                    logger.error("❌ 批量剧本生成失败")
            
            elif choice == "3":
                logger.info("🎯 选择: 全部生成")
                
                # 生成综合剧本
                logger.info("📝 第1步: 生成综合剧本...")
                success1 = run_single_script_generation()
                
                # 生成类别剧本
                logger.info("📚 第2步: 批量生成类别剧本...")
                success2 = run_batch_script_generation()
                
                if success1 and success2:
                    logger.info("🎉 全部剧本生成完成！")
                    logger.info("📁 查看所有文件: /data2/jevon/8_01_script/output/")
                elif success1 or success2:
                    logger.warning("⚠️ 部分剧本生成成功")
                else:
                    logger.error("❌ 全部剧本生成失败")
            
            elif choice == "4":
                logger.info("👋 退出程序")
                break
            
            else:
                print("❌ 无效选择，请输入 1-4")
                continue
            
            # 询问是否继续
            continue_choice = input("\n是否继续使用？(y/n): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                logger.info("👋 退出程序")
                break
                
        except KeyboardInterrupt:
            logger.info("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            logger.error(f"❌ 程序执行出错: {e}")
            continue

if __name__ == "__main__":
    main()
