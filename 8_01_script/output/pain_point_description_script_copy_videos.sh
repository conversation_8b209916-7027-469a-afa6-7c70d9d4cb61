#!/bin/bash
# 视频文件复制脚本
# 生成时间: 2025年08月01日 14:21:42

# 创建目标目录
mkdir -p "/data2/jevon/8_01_script/output/pain_point_description_script_videos"

echo "开始复制视频文件..."


# 复制视频 1: scene_008.mp4
echo "复制视频 1/5: scene_008.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_008.mp4" "/data2/jevon/8_01_script/output/pain_point_description_script_videos/01_scene_008.mp4"

# 复制视频 2: scene_005.mp4
echo "复制视频 2/5: scene_005.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_005.mp4" "/data2/jevon/8_01_script/output/pain_point_description_script_videos/02_scene_005.mp4"

# 复制视频 3: scene_004.mp4
echo "复制视频 3/5: scene_004.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_004.mp4" "/data2/jevon/8_01_script/output/pain_point_description_script_videos/03_scene_004.mp4"

# 复制视频 4: scene_003.mp4
echo "复制视频 4/5: scene_003.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_003.mp4" "/data2/jevon/8_01_script/output/pain_point_description_script_videos/04_scene_003.mp4"

# 复制视频 5: scene_004.mp4
echo "复制视频 5/5: scene_004.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_004.mp4" "/data2/jevon/8_01_script/output/pain_point_description_script_videos/05_scene_004.mp4"

echo "视频文件复制完成！"
echo "目标目录: /data2/jevon/8_01_script/output/pain_point_description_script_videos"
ls -la "/data2/jevon/8_01_script/output/pain_point_description_script_videos"
