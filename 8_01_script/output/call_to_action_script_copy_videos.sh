#!/bin/bash
# 视频文件复制脚本
# 生成时间: 2025年08月01日 14:21:01

# 创建目标目录
mkdir -p "/data2/jevon/8_01_script/output/call_to_action_script_videos"

echo "开始复制视频文件..."


# 复制视频 1: SBOX2326M_2023-07-20-Q3_scene_007.mp4
echo "复制视频 1/13: SBOX2326M_2023-07-20-Q3_scene_007.mp4"
cp "/data2/jevon/7_31_video_mashup/video/20241120-BM-Q4-BM_SBOX2326M_2023-07-20-Q3/scene_007.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/01_SBOX2326M_2023-07-20-Q3_scene_007.mp4"

# 复制视频 2: scene_008.mp4
echo "复制视频 2/13: scene_008.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_008.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/02_scene_008.mp4"

# 复制视频 3: scene_006.mp4
echo "复制视频 3/13: scene_006.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_006.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/03_scene_006.mp4"

# 复制视频 4: scene_003.mp4
echo "复制视频 4/13: scene_003.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_003.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/04_scene_003.mp4"

# 复制视频 5: scene_007.mp4
echo "复制视频 5/13: scene_007.mp4"
cp "/data2/jevon/7_31_video_mashup/video/20241120-BM-Q4-BM_SBOX2326M_2023-07-20-Q3/scene_007.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/05_scene_007.mp4"

# 复制视频 6: scene_002.mp4
echo "复制视频 6/13: scene_002.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_002.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/06_scene_002.mp4"

# 复制视频 7: scene_005.mp4
echo "复制视频 7/13: scene_005.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_005.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/07_scene_005.mp4"

# 复制视频 8: scene_004.mp4
echo "复制视频 8/13: scene_004.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_004.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/08_scene_004.mp4"

# 复制视频 9: scene_002.mp4
echo "复制视频 9/13: scene_002.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_002.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/09_scene_002.mp4"

# 复制视频 10: scene_003.mp4
echo "复制视频 10/13: scene_003.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_003.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/10_scene_003.mp4"

# 复制视频 11: scene_002.mp4
echo "复制视频 11/13: scene_002.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_002.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/11_scene_002.mp4"

# 复制视频 12: scene_003.mp4
echo "复制视频 12/13: scene_003.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_003.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/12_scene_003.mp4"

# 复制视频 13: scene_006.mp4
echo "复制视频 13/13: scene_006.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_006.mp4" "/data2/jevon/8_01_script/output/call_to_action_script_videos/13_scene_006.mp4"

echo "视频文件复制完成！"
echo "目标目录: /data2/jevon/8_01_script/output/call_to_action_script_videos"
ls -la "/data2/jevon/8_01_script/output/call_to_action_script_videos"
