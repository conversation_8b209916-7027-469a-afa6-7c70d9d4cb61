# 视频链接清单 - 用户评价剧本

**生成时间:** 2025年08月01日 14:20:20  
**剧本类型:** 用户评价剧本  
**推荐视频数量:** 11个  

---

## 📋 视频文件清单

### 📊 统计信息

- **总视频数:** 11个
- **文件存在:** 11个
- **文件缺失:** 0个
- **总文件大小:** 15.01 MB

---

## 🎬 推荐视频详情

### ✅ 视频 1: scene_001.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-0425-KOL/scene_001.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_001.mp4`
- **数据集:** SBOX2326M-0425-KOL
- **文件大小:** 1.57 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** User Reviews
- **次要类别:** Product Selling Points, Usage Scenario
- **置信度:** 0.95
- **标签:** unboxing, first-person perspective, dress sneakers, personal statement, workplace fashion

**内容描述:**
- **视觉描述:** A first-person perspective video of a person unboxing a pair of shoes. The video starts with a shot of a brown shoebox with the brand name 'BRUNO MARC' on it. The person opens the box to reveal a pair of black, perforated dress sneakers with a white sole and an orange accent. A hand with tattoos reaches into the box. Text overlay on the video says, 'if girls are allowed to wear makeup to work, I should be able to wear these'.
- **ASR内容:**  If girls are allowed to wear makeup to work, I should be able to wear these.
- **OCR内容:** if girls are allowed to  wear  makeup   to  work  BRUNO MARC, I should be ableto  wearthese

---

### ✅ 视频 2: scene_006.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-0425-KOL/scene_006.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_006.mp4`
- **数据集:** SBOX2326M-0425-KOL
- **文件大小:** 0.64 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** User Reviews
- **置信度:** 0.95
- **标签:** comfort, insole, shoe demonstration, positive feedback, visual close-up

**内容描述:**
- **视觉描述:** A man with a beard and glasses, wearing a green hoodie, holds up a dark blue shoe to the camera. He points inside the shoe with his index finger, showing the orange and blue insole, while speaking. An on-screen caption at the top reads, "but they're super comfortable".
- **ASR内容:**  but they're super comfortable.
- **OCR内容:** but they're super comfortable  I hope you can find the size.

---

### ✅ 视频 3: scene_001.mp4

**基本信息:**
- **视频ID:** `BM_Urgent-488_0418_V1/scene_001.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4`
- **数据集:** BM_Urgent-488_0418_V1
- **文件大小:** 0.59 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** User Reviews
- **次要类别:** Product Selling Points
- **置信度:** 0.90
- **标签:** unboxing, casual dress shoes, top-down view, white and brown shoes, shoebox opening

**内容描述:**
- **视觉描述:** A top-down view shows a person opening a large cardboard box. Inside, two smaller shoeboxes are stacked. The person opens each box to reveal a pair of modern, casual dress shoes—one pair is white, and the other is brown.
- **ASR内容:** 无语音内容
- **OCR内容:** IRUNO  MADC;-  -;2.

---

### ✅ 视频 4: Urgent-488_0418_V1_scene_001.mp4

**基本信息:**
- **视频ID:** `BM_Urgent-488_0418_V1/Urgent-488_0418_V1_scene_001.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4`
- **数据集:** BM_Urgent-488_0418_V1
- **文件大小:** 0.59 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** User Reviews
- **次要类别:** Product Selling Points
- **置信度:** 0.90
- **标签:** unboxing, casual dress shoes, top-down view, modern design, first-person perspective

**内容描述:**
- **视觉描述:** A top-down view shows a person opening a large cardboard box. Inside, two smaller shoeboxes are stacked. The person opens each box to reveal a pair of modern, casual dress shoes—one pair is white, and the other is brown.
- **ASR内容:** 无语音内容
- **OCR内容:** 无文字内容

---

### ✅ 视频 5: scene_008.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-KOL/scene_008.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_008.mp4`
- **数据集:** SBOX2326M-KOL
- **文件大小:** 1.38 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Pain Point Description
- **次要类别:** Product Selling Points, User Reviews
- **置信度:** 0.90
- **标签:** discomfort, insole, size, taller, uncomfortable

**内容描述:**
- **视觉描述:** A close-up shot of a man with a beard and glasses, wearing a green hoodie, holding a dark blue shoe. He is pointing inside the shoe with his finger, specifically at the insole where the size is located. There is a yellow arrow pointing to the area, with on-screen text that reads, "I hope you can find the size." The audio is a separate, likely dubbed, track of someone saying, "These make me look taller, but they're so uncomfortable."
- **ASR内容:**  do these make me look taller?, But they're super comfortable.
- **OCR内容:** I hope you can find the size.

---

### ✅ 视频 6: scene_004.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-0425-03/scene_004.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_004.mp4`
- **数据集:** SBOX2326M-0425-03
- **文件大小:** 2.50 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Pain Point Description
- **次要类别:** Product Selling Points, User Reviews
- **置信度:** 0.90
- **标签:** height increase, dress shoes, personal struggle, thick-soled, first-person narrative

**内容描述:**
- **视觉描述:** A high-angle shot shows a man with tattoos on his arm, sitting on a couch and putting on a pair of black, thick-soled dress shoes. Text on the screen reads, "I'VE BEEN SHORT MY WHOLE ENTIRE LIFE" and "I hope you can find the size," with a yellow arrow pointing to the shoe. The man is lacing up one shoe, suggesting these shoes will make him taller.
- **ASR内容:**  I've been short my whole entire life and now I finally get to feel like when it's
- **OCR内容:** LIKE AMI  WRONG  FOR THIS;IVE BEEN  SHORT  MY  WHOLE ENTIRE LIFE  I hope you can find the size.

---

### ✅ 视频 7: scene_006.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-0425-03/scene_006.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_006.mp4`
- **数据集:** SBOX2326M-0425-03
- **文件大小:** 0.73 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** User Reviews
- **置信度:** 0.90
- **标签:** comfortable insole, expressive presentation, shoe interior showcase

**内容描述:**
- **视觉描述:** A man with a beard and glasses, wearing a green hoodie, holds up a dark blue shoe. He points his finger inside the shoe, showing the camera the orange and blue patterned insole while talking with an expressive look on his face.
- **ASR内容:**  but they're super comfortable.
- **OCR内容:** 无文字内容

---

### ✅ 视频 8: scene_005.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-0425-KOL/scene_005.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_005.mp4`
- **数据集:** SBOX2326M-0425-KOL
- **文件大小:** 2.29 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** User Reviews, Usage Scenario
- **置信度:** 0.90
- **标签:** height enhancement, comfort claims, visual comparison, on-screen text, first-person narration

**内容描述:**
- **视觉描述:** A man wearing a green hoodie and black pants is in a room with wood-like flooring. He is putting on a pair of black shoes, and the on-screen text and narration state that these shoes make him feel 6 feet tall and look taller. At the bottom of the frame, there is a brown sandal on the floor, pointed out by a yellow arrow, accompanied by the text 'I hope you can find the size.'
- **ASR内容:**  Finally get to feel like what it's like to be six foot., Not only do these make me look taller.
- **OCR内容:** these make  look  notonly do  me  taller  I hope you can i  find the size.

---

### ✅ 视频 9: scene_003.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-0425-KOL/scene_003.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_003.mp4`
- **数据集:** SBOX2326M-0425-KOL
- **文件大小:** 2.83 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** Call to Action, User Reviews
- **置信度:** 0.90
- **标签:** height increase, breathability, size availability, text overlays, male voiceover

**内容描述:**
- **视觉描述:** A close-up shot of a person's feet wearing dark blue, sneaker-style shoes with thick white soles and an orange accent on the heel. The person is standing on a wet, paved surface with green grass in the background. The video features several text overlays. At the top, it says, "shoes that make me 3 inches taller". In the middle, the text reads, "Breathe Easy". At the bottom, a yellow arrow points down towards the shoes, next to the text, "I hope you can find the size." A snippet of text, "like am I wrong for this", also briefly appears. A male voice can be heard in the audio.
- **ASR内容:**  is taller., Am I wrong for this?
- **OCR内容:** shoes  that make me  3  inches  taller  Breathe  I hope you can find the size., like am I wrong for this  Breathe Easy;I've been.  short  my  whole entire  life  Breathe Easy

---

### ✅ 视频 10: scene_007.mp4

**基本信息:**
- **视频ID:** `SBOX2326M-KOL/scene_007.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_007.mp4`
- **数据集:** SBOX2326M-KOL
- **文件大小:** 1.11 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** Usage Scenario, User Reviews
- **置信度:** 0.85
- **标签:** Close-up shot, Comfortable Casual Oxford Shoes, Text overlays, Indoor setting, Trying on shoes

**内容描述:**
- **视觉描述:** A close-up shot of a man indoors on a wooden floor, trying on a black casual Oxford-style shoe with a white and red sole. He is wearing the shoe on his left foot and has a white sock on his right foot. A pair of brown sandals is on the floor in the foreground. Text overlays read, "Men's Comfortable Casual Oxford Shoes" at the top, and "I hope you can find the size" at the bottom, with a yellow arrow pointing to the sandals.
- **ASR内容:**  to be like what it's like to be six foot.
- **OCR内容:** Men's Comfortable.  Casual Oxford Shoes.  I hope you can find the size.

---

### ✅ 视频 11: scene_002.mp4

**基本信息:**
- **视频ID:** `SBOX2326M--KOL/scene_002.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_002.mp4`
- **数据集:** SBOX2326M--KOL
- **文件大小:** 0.78 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** Usage Scenario, User Reviews
- **置信度:** 0.85
- **标签:** close-up shot, size information, on-screen text, shoe detail, demonstration

**内容描述:**
- **视觉描述:** A close-up shot of a bearded man with glasses, wearing a green hoodie, holding a dark blue shoe. He is pointing inside the shoe with his finger, seemingly showing where to find the size information, which is reinforced by on-screen text and a yellow arrow.
- **ASR内容:**  They're also a super nice lesson.
- **OCR内容:** I hope you can find the size.

---

## 🔧 使用说明

### 视频合并命令参考

#### 使用FFmpeg合并视频（按推荐顺序）:

```bash
# 创建文件列表
cat > video_list.txt << EOF
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_001.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_006.mp4'
file '/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4'
file '/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_008.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_004.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_006.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_005.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_003.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_007.mp4'
file '/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_002.mp4'
EOF

# 合并视频
ffmpeg -f concat -safe 0 -i video_list.txt -c copy merged_video.mp4
```

#### 使用Python脚本合并:

```python
import subprocess

video_paths = [
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_001.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_006.mp4',
    '/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4',
    '/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_008.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_004.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_006.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_005.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_003.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_007.mp4',
    '/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_002.mp4',
]

# 使用moviepy合并
from moviepy.editor import VideoFileClip, concatenate_videoclips

clips = [VideoFileClip(path) for path in video_paths]
final_video = concatenate_videoclips(clips)
final_video.write_videofile('merged_video.mp4')
```

### 文件检查

#### 检查所有文件是否存在:

```bash
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_001.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_006.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_008.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_004.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_006.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_005.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_003.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_007.mp4'
ls -la '/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_002.mp4'
```

### 备注

- ✅ 表示文件存在，可以直接使用
- ❌ 表示文件缺失，需要检查路径或重新生成
- 建议按照推荐顺序进行视频合并
- 合并前请确保所有视频文件都存在

---

**生成时间:** 2025年08月01日 14:20:20  
**系统版本:** v1.0.0
