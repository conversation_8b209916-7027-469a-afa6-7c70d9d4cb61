#!/bin/bash
# 视频文件复制脚本
# 生成时间: 2025年08月01日 14:20:20

# 创建目标目录
mkdir -p "/data2/jevon/8_01_script/output/user_reviews_script_videos"

echo "开始复制视频文件..."


# 复制视频 1: scene_001.mp4
echo "复制视频 1/11: scene_001.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_001.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/01_scene_001.mp4"

# 复制视频 2: scene_006.mp4
echo "复制视频 2/11: scene_006.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_006.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/02_scene_006.mp4"

# 复制视频 3: scene_001.mp4
echo "复制视频 3/11: scene_001.mp4"
cp "/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/03_scene_001.mp4"

# 复制视频 4: Urgent-488_0418_V1_scene_001.mp4
echo "复制视频 4/11: Urgent-488_0418_V1_scene_001.mp4"
cp "/data2/jevon/7_31_video_mashup/video/BM_Urgent-488_0418_V1/scene_001.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/04_Urgent-488_0418_V1_scene_001.mp4"

# 复制视频 5: scene_008.mp4
echo "复制视频 5/11: scene_008.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_008.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/05_scene_008.mp4"

# 复制视频 6: scene_004.mp4
echo "复制视频 6/11: scene_004.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_004.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/06_scene_004.mp4"

# 复制视频 7: scene_006.mp4
echo "复制视频 7/11: scene_006.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-03/scene_006.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/07_scene_006.mp4"

# 复制视频 8: scene_005.mp4
echo "复制视频 8/11: scene_005.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_005.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/08_scene_005.mp4"

# 复制视频 9: scene_003.mp4
echo "复制视频 9/11: scene_003.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-0425-KOL/scene_003.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/09_scene_003.mp4"

# 复制视频 10: scene_007.mp4
echo "复制视频 10/11: scene_007.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M-KOL/scene_007.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/10_scene_007.mp4"

# 复制视频 11: scene_002.mp4
echo "复制视频 11/11: scene_002.mp4"
cp "/data2/jevon/7_31_video_mashup/video/SBOX2326M--KOL/scene_002.mp4" "/data2/jevon/8_01_script/output/user_reviews_script_videos/11_scene_002.mp4"

echo "视频文件复制完成！"
echo "目标目录: /data2/jevon/8_01_script/output/user_reviews_script_videos"
ls -la "/data2/jevon/8_01_script/output/user_reviews_script_videos"
