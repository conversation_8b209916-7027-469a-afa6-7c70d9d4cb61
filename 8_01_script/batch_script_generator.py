#!/usr/bin/env python3
"""
批量视频剧本生成器
根据不同类别生成专门的剧本
"""

import json
import logging
import time
from pathlib import Path
from datetime import datetime
from script_generator import ScriptGenerator
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchScriptGenerator:
    """批量剧本生成器"""
    
    def __init__(self):
        """初始化批量生成器"""
        self.generator = ScriptGenerator()
        
        # 类别专门的剧本类型 (排除Other类别)
        self.category_scripts = {
            "Product Selling Points": {
                "name": "产品卖点剧本",
                "description": "专注于产品特性和优势展示的剧本",
                "max_videos": 8
            },
            "Usage Scenario": {
                "name": "使用场景剧本",
                "description": "展示产品在实际使用场景中的剧本",
                "max_videos": 8
            },
            "User Reviews": {
                "name": "用户评价剧本",
                "description": "基于用户反馈和评价的剧本",
                "max_videos": 5
            },
            "Call to Action": {
                "name": "行动召唤剧本",
                "description": "引导用户购买行为的剧本",
                "max_videos": 5
            },
            "Pain Point Description": {
                "name": "痛点描述剧本",
                "description": "描述用户痛点和问题的剧本",
                "max_videos": 5
            }
        }
        
        logger.info("✅ 批量剧本生成器初始化完成")
    
    def filter_videos_by_category(self, results: List[Dict], target_category: str) -> List[Dict]:
        """按类别筛选视频（排除Other类别）"""
        filtered = []

        for video in results:
            primary_category = video.get('primary_category', '')
            secondary_categories = video.get('secondary_categories', [])

            # 排除Other类别的视频
            if primary_category == 'Other':
                continue

            # 检查主要类别或次要类别
            if primary_category == target_category or target_category in secondary_categories:
                filtered.append(video)

        # 按置信度排序
        filtered.sort(key=lambda x: x.get('confidence_score', 0), reverse=True)

        logger.info(f"📊 类别 '{target_category}' 筛选出 {len(filtered)} 个视频（已排除Other类别）")
        return filtered

    def _prepare_category_materials(self, category_videos: List[Dict], category: str) -> tuple[str, List[Dict]]:
        """为特定类别准备所有素材"""
        logger.info(f"🎬 为类别 '{category}' 准备所有素材...")

        if not category_videos:
            return f"### {category}\n\n暂无该类别的视频素材。\n\n", []

        # 按置信度排序
        category_videos.sort(key=lambda x: x.get('confidence_score', 0), reverse=True)

        # 类别映射到中文名称
        category_chinese_names = {
            "Pain Point Description": "① 痛点描述",
            "Usage Scenario": "② 使用场景",
            "Product Selling Points": "③ 产品卖点",
            "User Reviews": "④ 用户评价",
            "Call to Action": "⑤ Call to Action"
        }

        chinese_name = category_chinese_names.get(category, category)
        materials_text = f"### {chinese_name}\n\n"

        for video in category_videos:
            video_id = video.get('video_id', '')
            confidence = video.get('confidence_score', 0)
            reasoning = video.get('reasoning', '无描述')
            tags = video.get('specific_tags', [])

            # 获取ASR内容
            asr_content = self._get_asr_content(video_id)

            # 格式化为：视频号 + 置信度 + ASR内容 + 视觉描述
            materials_text += f"**{video_id}** (置信度: {confidence:.2f})\n"
            materials_text += f"- ASR内容: {asr_content}\n"
            materials_text += f"- 视觉描述: {reasoning}\n"
            materials_text += f"- 标签: {', '.join(tags)}\n\n"

        logger.info(f"✅ 为类别 '{category}' 准备了 {len(category_videos)} 个素材")
        return materials_text, category_videos

    def _get_asr_content(self, video_id: str) -> str:
        """获取视频的ASR内容"""
        try:
            video_info = self.generator.video_link_manager.get_video_info(video_id)
            if video_info and video_info.get('asr'):
                asr_list = video_info['asr']
                if asr_list:
                    return ' '.join(asr_list)
            return "无语音内容"
        except:
            return "无语音内容"
    
    def create_category_prompt(self, category: str, base_prompt: str) -> str:
        """为特定类别创建专门的提示词"""
        
        category_prompts = {
            "Product Selling Points": """
## 产品卖点剧本生成要求

请基于提供的产品卖点类视频素材，生成一个专业的产品展示剧本。重点关注：

1. **产品特性展示** - 突出产品的独特设计和功能特点
2. **视觉冲击力** - 利用特写镜头和细节展示
3. **专业解说** - 用专业术语介绍产品优势
4. **购买理由** - 给出明确的购买动机

剧本结构：
- 开场吸引（产品亮点预告）
- 特性详解（逐一展示卖点）
- 使用演示（简单使用场景）
- 用户好评（简短证言）
- 购买引导（明确行动指示）
""",
            
            "Usage Scenario": """
## 使用场景剧本生成要求

请基于提供的使用场景类视频素材，生成一个生活化的场景剧本。重点关注：

1. **场景代入** - 让观众感受到真实的使用环境
2. **自然过渡** - 场景间的自然衔接
3. **生活化语言** - 贴近用户日常表达
4. **问题解决** - 展示产品如何解决实际问题

剧本结构：
- 场景设定（日常生活场景）
- 需求出现（遇到的问题或需求）
- 产品登场（自然引入产品）
- 使用体验（展示使用过程）
- 满意结果（问题得到解决）
""",
            
            "User Reviews": """
## 用户评价剧本生成要求

请基于提供的用户评价类视频素材，生成一个真实可信的用户反馈剧本。重点关注：

1. **真实性** - 模拟真实用户的语言和表达
2. **多样性** - 展示不同用户的使用体验
3. **具体细节** - 包含具体的使用感受和细节
4. **情感共鸣** - 引起观众的情感共鸣

剧本结构：
- 用户介绍（简单背景介绍）
- 购买原因（为什么选择这个产品）
- 使用体验（详细使用感受）
- 意外惊喜（超出预期的地方）
- 推荐理由（为什么推荐给别人）
""",
            
            "Call to Action": """
## 行动召唤剧本生成要求

请基于提供的行动召唤类视频素材，生成一个有说服力的购买引导剧本。重点关注：

1. **紧迫感** - 创造购买的紧迫感
2. **优惠信息** - 突出价格优势和优惠
3. **限时限量** - 强调稀缺性
4. **简单行动** - 明确简单的购买步骤

剧本结构：
- 价值回顾（快速回顾产品价值）
- 优惠公布（限时优惠信息）
- 稀缺提醒（库存或时间限制）
- 行动指导（具体购买步骤）
- 保障承诺（售后服务保障）
""",
            
            "Pain Point Description": """
## 痛点描述剧本生成要求

请基于提供的痛点描述类视频素材，生成一个引起共鸣的痛点剧本。重点关注：

1. **痛点共鸣** - 准确描述用户的痛点
2. **情感连接** - 与观众建立情感连接
3. **问题放大** - 适度放大问题的严重性
4. **解决方案** - 自然引出产品解决方案

剧本结构：
- 痛点展现（描述常见问题）
- 影响分析（问题带来的困扰）
- 现状无奈（现有解决方案的不足）
- 解决方案（产品如何解决问题）
- 美好愿景（使用产品后的改变）
"""
        }
        
        category_prompt = category_prompts.get(category, "")
        return base_prompt + "\n\n" + category_prompt
    
    def generate_category_scripts(self, classification_file: str, prompt_file: str, output_dir: str):
        """为每个类别生成专门的剧本"""
        logger.info("🎬 开始批量生成类别专门剧本...")
        
        # 加载数据
        results = self.generator.load_classification_results(classification_file)
        base_prompt = self.generator.load_script_prompt(prompt_file)
        
        if not results or not base_prompt:
            logger.error("❌ 数据加载失败，终止批量生成")
            return False
        
        # 确保输出目录存在
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        success_count = 0
        total_count = len(self.category_scripts)
        
        # 为每个类别生成剧本
        for category, config in self.category_scripts.items():
            logger.info(f"📝 生成 {config['name']} ({category})")
            
            try:
                # 筛选该类别的视频
                category_videos = self.filter_videos_by_category(results, category)
                
                if not category_videos:
                    logger.warning(f"⚠️ 类别 '{category}' 没有找到相关视频，跳过")
                    continue
                
                # 准备该类别的所有素材
                materials, selected_videos = self._prepare_category_materials(category_videos, category)
                
                # 创建专门的提示词
                category_prompt = self.create_category_prompt(category, base_prompt)
                
                # 生成剧本（传递类别名称作为prompt_type）
                prompt_type = category.lower().replace(' ', '_')
                script_content = self.generator.call_gemini_api(category_prompt, materials, prompt_type)
                
                if script_content:
                    # 保存剧本
                    output_file = f"{output_dir}/{category.lower().replace(' ', '_')}_script.md"
                    
                    # 添加类别信息
                    header = f"""# {config['name']}

**类别:** {category}  
**描述:** {config['description']}  
**素材数量:** {len(category_videos[:config['max_videos']])}个  
**生成时间:** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

---

"""
                    
                    full_content = header + script_content
                    
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(full_content)

                    # 跳过辅助文件生成，只保留核心剧本

                    logger.info(f"✅ {config['name']} 生成成功: {output_file}")
                    success_count += 1
                else:
                    logger.error(f"❌ {config['name']} 生成失败")
                
                # 添加延迟避免API限制
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ 生成 {config['name']} 时出错: {e}")
        
        logger.info(f"🎉 批量生成完成！成功: {success_count}/{total_count}")
        return success_count > 0

def main():
    """主函数"""
    logger.info("🚀 启动批量视频剧本生成器...")
    
    # 文件路径配置
    classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
    prompt_file = "/data2/jevon/8_01_script/data/mash_up_script.md"
    output_dir = "/data2/jevon/8_01_script/output"
    
    try:
        # 初始化批量生成器
        batch_generator = BatchScriptGenerator()
        
        # 批量生成剧本
        success = batch_generator.generate_category_scripts(
            classification_file=classification_file,
            prompt_file=prompt_file,
            output_dir=output_dir
        )
        
        if success:
            logger.info("✅ 批量剧本生成任务完成！")
            logger.info(f"📁 查看生成的剧本: {output_dir}")
        else:
            logger.error("❌ 批量剧本生成任务失败")
            
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
