#!/usr/bin/env python3
"""
测试Prompt格式
只生成prompt，不调用API
"""

import logging
from datetime import datetime
from script_generator import ScriptGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """测试prompt格式"""
    logger.info("🧪 测试Prompt格式...")
    
    # 文件路径配置
    classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
    prompt_file = "/data2/jevon/8_01_script/data/mash_up_script.md"
    
    try:
        # 初始化生成器
        generator = ScriptGenerator()
        
        # 1. 加载分类结果
        results = generator.load_classification_results(classification_file)
        logger.info(f"✅ 加载了 {len(results)} 个视频分类结果")
        
        # 2. 加载提示词
        with open(prompt_file, 'r', encoding='utf-8') as f:
            prompt = f.read()
        logger.info("✅ 剧本提示词加载成功")
        
        # 3. 准备视频素材
        materials, selected_videos = generator.prepare_video_materials_by_category(results)
        logger.info(f"✅ 准备了 {len(selected_videos)} 个视频素材")
        
        # 4. 构建完整prompt（不调用API）
        full_prompt = prompt.replace("## 4️⃣ 原始素材\n\n---", f"## 4️⃣ 原始素材\n\n{materials}\n---")
        
        # 5. 保存测试prompt
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        test_prompt_file = f"/data2/jevon/8_01_script/prompts/test_prompt_{timestamp}.txt"
        
        with open(test_prompt_file, 'w', encoding='utf-8') as f:
            f.write(f"""# 测试Prompt格式

**生成时间:** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**测试目的:** 验证素材是否正确插入到"## 4️⃣ 原始素材"部分

---

{full_prompt}
""")
        
        logger.info(f"✅ 测试Prompt已保存: {test_prompt_file}")
        
        # 6. 验证格式
        logger.info("🔍 验证Prompt格式...")
        
        if "### ① 痛点描述" in full_prompt:
            logger.info("✅ 痛点描述类别存在")
        else:
            logger.error("❌ 痛点描述类别缺失")
            
        if "### ② 使用场景" in full_prompt:
            logger.info("✅ 使用场景类别存在")
        else:
            logger.error("❌ 使用场景类别缺失")
            
        if "### ③ 产品卖点" in full_prompt:
            logger.info("✅ 产品卖点类别存在")
        else:
            logger.error("❌ 产品卖点类别缺失")
            
        if "## 4️⃣ 原始素材\n\n---" in full_prompt:
            logger.error("❌ 素材未正确插入，仍然是空的")
        else:
            logger.info("✅ 素材已正确插入到原始素材部分")
        
        print(f"\n📁 测试完成！请查看文件: {test_prompt_file}")
        print("🔍 检查素材是否正确插入到'## 4️⃣ 原始素材'部分")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
