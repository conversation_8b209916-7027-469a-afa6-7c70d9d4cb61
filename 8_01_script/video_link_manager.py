#!/usr/bin/env python3
"""
视频链接管理器
为剧本中推荐的视频片段提供具体的视频文件路径，帮助后期视频合并
"""

import json
import logging
import os
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoLinkManager:
    """视频链接管理器"""
    
    def __init__(self, video_base_path: str = "/data2/jevon/test/8_01_video/video"):
        """初始化视频链接管理器"""
        self.video_base_path = video_base_path
        self.video_path_mapping = {}

        # 直接扫描视频目录构建映射
        self.build_video_mapping()

        logger.info("✅ 视频链接管理器初始化完成")
    
    def build_video_mapping(self):
        """直接扫描视频目录构建映射"""
        try:
            logger.info(f"📂 扫描视频目录: {self.video_base_path}")

            if not os.path.exists(self.video_base_path):
                logger.error(f"❌ 视频目录不存在: {self.video_base_path}")
                return

            # 扫描所有视频文件
            for dataset_name in os.listdir(self.video_base_path):
                dataset_path = os.path.join(self.video_base_path, dataset_name)

                # 跳过文件，只处理目录
                if not os.path.isdir(dataset_path):
                    continue

                # 扫描数据集目录中的视频文件
                for video_file in os.listdir(dataset_path):
                    if video_file.endswith('.mp4'):
                        video_path = os.path.join(dataset_path, video_file)

                        # 获取文件大小
                        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)

                        # 构建video_info
                        video_info = {
                            'video_path': video_path,
                            'dataset_name': dataset_name,
                            'video_name': video_file,
                            'file_size_mb': file_size_mb,
                            'description': f'Video from {dataset_name}',
                            'asr': [],  # 暂时为空，可以后续从其他数据源加载
                            'ocr': []   # 暂时为空，可以后续从其他数据源加载
                        }

                        # 构建多种可能的video_id格式
                        video_id_formats = [
                            f"{dataset_name}/{video_file}",  # 标准格式
                            video_file,  # 只有文件名
                        ]

                        # 添加到映射中
                        for video_id in video_id_formats:
                            self.video_path_mapping[video_id] = video_info

            logger.info(f"✅ 成功构建 {len(self.video_path_mapping)} 个视频路径映射")

        except Exception as e:
            logger.error(f"❌ 构建视频映射失败: {e}")
            self.video_path_mapping = {}
    
    def get_video_path(self, video_id: str) -> Optional[str]:
        """获取视频的完整路径"""
        video_info = self.video_path_mapping.get(video_id)
        return video_info['video_path'] if video_info else None
    
    def get_video_info(self, video_id: str) -> Optional[Dict]:
        """获取视频的详细信息"""
        return self.video_path_mapping.get(video_id)
    
    def check_video_exists(self, video_id: str) -> bool:
        """检查视频文件是否存在"""
        video_path = self.get_video_path(video_id)
        if not video_path:
            return False
        
        return os.path.exists(video_path)
    
    def get_video_links_for_script(self, selected_videos: List[Dict]) -> List[Dict]:
        """为剧本中的视频生成链接信息"""
        video_links = []
        
        for i, video in enumerate(selected_videos, 1):
            video_id = video.get('video_id', '')
            video_info = self.get_video_info(video_id)
            
            if video_info:
                link_info = {
                    'sequence': i,
                    'video_id': video_id,
                    'video_path': video_info['video_path'],
                    'dataset_name': video_info['dataset_name'],
                    'video_name': video_info['video_name'],
                    'file_size_mb': video_info['file_size_mb'],
                    'file_exists': self.check_video_exists(video_id),
                    'primary_category': video.get('primary_category', ''),
                    'secondary_categories': video.get('secondary_categories', []),
                    'confidence_score': video.get('confidence_score', 0),
                    'specific_tags': video.get('specific_tags', []),
                    'description': video_info['description'],
                    'asr_content': video_info['asr'],
                    'ocr_content': video_info['ocr']
                }
            else:
                # 如果找不到视频信息，创建基本信息
                link_info = {
                    'sequence': i,
                    'video_id': video_id,
                    'video_path': '❌ 路径未找到',
                    'dataset_name': video.get('dataset_name', ''),
                    'video_name': video_id.split('/')[-1] if '/' in video_id else video_id,
                    'file_size_mb': 0,
                    'file_exists': False,
                    'primary_category': video.get('primary_category', ''),
                    'secondary_categories': video.get('secondary_categories', []),
                    'confidence_score': video.get('confidence_score', 0),
                    'specific_tags': video.get('specific_tags', []),
                    'description': video.get('reasoning', ''),
                    'asr_content': [],
                    'ocr_content': []
                }
            
            video_links.append(link_info)
        
        return video_links
    
    def create_video_links_file(self, video_links: List[Dict], output_path: str, script_type: str = "综合剧本"):
        """创建视频链接文件"""
        try:
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 生成链接文件内容
            content = self._generate_links_content(video_links, script_type)
            
            # 保存到文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"📎 视频链接文件已保存: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ 创建视频链接文件失败: {e}")
            return None
    
    def _generate_links_content(self, video_links: List[Dict], script_type: str) -> str:
        """生成链接文件内容"""
        
        timestamp = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        
        content = f"""# 视频链接清单 - {script_type}

**生成时间:** {timestamp}  
**剧本类型:** {script_type}  
**推荐视频数量:** {len(video_links)}个  

---

## 📋 视频文件清单

"""
        
        # 统计信息
        existing_count = sum(1 for link in video_links if link['file_exists'])
        total_size = sum(link['file_size_mb'] for link in video_links)
        
        content += f"""### 📊 统计信息

- **总视频数:** {len(video_links)}个
- **文件存在:** {existing_count}个
- **文件缺失:** {len(video_links) - existing_count}个
- **总文件大小:** {total_size:.2f} MB

---

## 🎬 推荐视频详情

"""
        
        # 详细视频信息
        for link in video_links:
            status_icon = "✅" if link['file_exists'] else "❌"
            
            content += f"""### {status_icon} 视频 {link['sequence']}: {link['video_name']}

**基本信息:**
- **视频ID:** `{link['video_id']}`
- **文件路径:** `{link['video_path']}`
- **数据集:** {link['dataset_name']}
- **文件大小:** {link['file_size_mb']:.2f} MB
- **文件状态:** {'存在' if link['file_exists'] else '缺失'}

**分类信息:**
- **主要类别:** {link['primary_category']}
- **次要类别:** {', '.join(link['secondary_categories']) if link['secondary_categories'] else '无'}
- **置信度:** {link['confidence_score']:.2f}
- **标签:** {', '.join(link['specific_tags']) if link['specific_tags'] else '无'}

**内容描述:**
- **视觉描述:** {link['description']}
- **ASR内容:** {', '.join(link['asr_content']) if link['asr_content'] else '无语音内容'}
- **OCR内容:** {', '.join(link['ocr_content']) if link['ocr_content'] else '无文字内容'}

---

"""
        
        # 添加使用说明
        content += f"""## 🔧 使用说明

### 视频合并命令参考

#### 使用FFmpeg合并视频（按推荐顺序）:

```bash
# 创建文件列表
cat > video_list.txt << EOF
"""
        
        # 生成FFmpeg文件列表
        for link in video_links:
            if link['file_exists']:
                content += f"file '{link['video_path']}'\n"
        
        content += f"""EOF

# 合并视频
ffmpeg -f concat -safe 0 -i video_list.txt -c copy merged_video.mp4
```

#### 使用Python脚本合并:

```python
import subprocess

video_paths = [
"""
        
        # 生成Python路径列表
        for link in video_links:
            if link['file_exists']:
                content += f"    '{link['video_path']}',\n"
        
        content += f"""]

# 使用moviepy合并
from moviepy.editor import VideoFileClip, concatenate_videoclips

clips = [VideoFileClip(path) for path in video_paths]
final_video = concatenate_videoclips(clips)
final_video.write_videofile('merged_video.mp4')
```

### 文件检查

#### 检查所有文件是否存在:

```bash
"""
        
        # 生成文件检查命令
        for link in video_links:
            content += f"ls -la '{link['video_path']}'\n"
        
        content += f"""```

### 备注

- ✅ 表示文件存在，可以直接使用
- ❌ 表示文件缺失，需要检查路径或重新生成
- 建议按照推荐顺序进行视频合并
- 合并前请确保所有视频文件都存在

---

**生成时间:** {timestamp}  
**系统版本:** v1.0.0
"""
        
        return content
    
    def create_copy_script(self, video_links: List[Dict], target_dir: str, output_path: str):
        """创建视频文件复制脚本"""
        try:
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            script_content = f"""#!/bin/bash
# 视频文件复制脚本
# 生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

# 创建目标目录
mkdir -p "{target_dir}"

echo "开始复制视频文件..."

"""
            
            # 生成复制命令
            for i, link in enumerate(video_links, 1):
                if link['file_exists']:
                    new_name = f"{i:02d}_{link['video_name']}"
                    script_content += f"""
# 复制视频 {i}: {link['video_name']}
echo "复制视频 {i}/{len(video_links)}: {link['video_name']}"
cp "{link['video_path']}" "{target_dir}/{new_name}"
"""
                else:
                    script_content += f"""
# 视频 {i} 文件缺失: {link['video_name']}
echo "⚠️ 视频 {i} 文件缺失: {link['video_path']}"
"""
            
            script_content += f"""
echo "视频文件复制完成！"
echo "目标目录: {target_dir}"
ls -la "{target_dir}"
"""
            
            # 保存脚本
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 设置执行权限
            os.chmod(output_path, 0o755)
            
            logger.info(f"📜 复制脚本已保存: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ 创建复制脚本失败: {e}")
            return None

def main():
    """测试函数"""
    logger.info("🧪 测试视频链接管理器...")
    
    manager = VideoLinkManager()
    
    # 测试获取视频信息
    test_video_id = "20240425-BM-Q2-SBOX2326M&9-16/scene_001.mp4"
    video_info = manager.get_video_info(test_video_id)
    
    if video_info:
        print(f"✅ 找到视频: {test_video_id}")
        print(f"   路径: {video_info['video_path']}")
        print(f"   大小: {video_info['file_size_mb']:.2f} MB")
        print(f"   存在: {manager.check_video_exists(test_video_id)}")
    else:
        print(f"❌ 未找到视频: {test_video_id}")
    
    print(f"\n📊 总共管理 {len(manager.video_path_mapping)} 个视频文件")

if __name__ == "__main__":
    main()
