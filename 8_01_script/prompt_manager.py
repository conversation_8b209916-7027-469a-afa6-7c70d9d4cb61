#!/usr/bin/env python3
"""
Prompt管理器
用于保存和管理生成剧本时使用的完整prompt
"""

import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PromptManager:
    """Prompt管理器"""
    
    def __init__(self, prompts_dir: str = "prompts"):
        """初始化Prompt管理器"""
        self.prompts_dir = Path(prompts_dir)
        self.prompts_dir.mkdir(exist_ok=True)
        
        # Prompt类型映射
        self.prompt_type_names = {
            "comprehensive": "综合剧本",
            "product_selling_points": "产品卖点剧本",
            "usage_scenario": "使用场景剧本", 
            "user_reviews": "用户评价剧本",
            "call_to_action": "行动召唤剧本",
            "pain_point_description": "痛点描述剧本"
        }
        
        logger.info(f"✅ Prompt管理器初始化完成，目录: {self.prompts_dir}")
    
    def save_prompt(self, base_prompt: str, materials: str, prompt_type: str, 
                   model_name: str = "google/gemini-2.5-pro", 
                   additional_info: Dict = None) -> str:
        """保存完整的prompt到txt文件"""
        try:
            # 构建完整的提示词
            full_prompt = f"{base_prompt}\n\n{materials}\n\n请基于以上素材生成专业的短视频剧本。"
            
            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{prompt_type}_prompt_{timestamp}.txt"
            filepath = self.prompts_dir / filename
            
            # 获取中文名称
            chinese_name = self.prompt_type_names.get(prompt_type, prompt_type)
            
            # 构建文件头信息
            header = self._create_header(chinese_name, prompt_type, model_name, filename, additional_info)
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(header + full_prompt)
            
            logger.info(f"📝 Prompt已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ 保存Prompt失败: {e}")
            return None
    
    def _create_header(self, chinese_name: str, prompt_type: str, model_name: str, 
                      filename: str, additional_info: Dict = None) -> str:
        """创建文件头信息"""
        
        header = f"""# 视频剧本生成Prompt - {chinese_name}

## 📋 基本信息

**生成时间:** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
**Prompt类型:** {prompt_type} ({chinese_name})
**目标模型:** {model_name}
**文件名:** {filename}

"""
        
        # 添加额外信息
        if additional_info:
            header += "## 📊 生成参数\n\n"
            for key, value in additional_info.items():
                header += f"**{key}:** {value}\n"
            header += "\n"
        
        header += """## 🎯 Prompt内容

以下是发送给AI模型的完整prompt内容：

---

"""
        
        return header
    
    def save_prompt_with_metadata(self, base_prompt: str, materials: str, prompt_type: str,
                                 video_count: int, categories_used: List[str],
                                 model_name: str = "google/gemini-2.5-pro") -> str:
        """保存带有元数据的prompt"""
        
        additional_info = {
            "使用视频数量": f"{video_count}个",
            "涉及类别": ", ".join(categories_used) if categories_used else "无",
            "生成策略": "基于AI分类结果，排除Other类别，利用secondary_categories"
        }
        
        return self.save_prompt(base_prompt, materials, prompt_type, model_name, additional_info)
    
    def list_saved_prompts(self) -> List[Dict]:
        """列出已保存的prompt文件"""
        try:
            prompt_files = []
            
            for file_path in self.prompts_dir.glob("*.txt"):
                file_info = {
                    "filename": file_path.name,
                    "filepath": str(file_path),
                    "size_kb": round(file_path.stat().st_size / 1024, 2),
                    "created_time": datetime.fromtimestamp(file_path.stat().st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
                    "prompt_type": self._extract_prompt_type(file_path.name)
                }
                prompt_files.append(file_info)
            
            # 按创建时间排序
            prompt_files.sort(key=lambda x: x['created_time'], reverse=True)
            
            logger.info(f"📁 找到 {len(prompt_files)} 个prompt文件")
            return prompt_files
            
        except Exception as e:
            logger.error(f"❌ 列出prompt文件失败: {e}")
            return []
    
    def _extract_prompt_type(self, filename: str) -> str:
        """从文件名提取prompt类型"""
        try:
            # 文件名格式: {prompt_type}_prompt_{timestamp}.txt
            parts = filename.replace('.txt', '').split('_prompt_')
            if len(parts) >= 2:
                prompt_type = parts[0]
                return self.prompt_type_names.get(prompt_type, prompt_type)
            return "未知类型"
        except:
            return "未知类型"
    
    def create_prompt_summary(self) -> str:
        """创建prompt文件总结"""
        try:
            prompt_files = self.list_saved_prompts()
            
            if not prompt_files:
                return "📁 暂无保存的prompt文件"
            
            # 按类型统计
            type_counts = {}
            total_size = 0
            
            for file_info in prompt_files:
                prompt_type = file_info['prompt_type']
                type_counts[prompt_type] = type_counts.get(prompt_type, 0) + 1
                total_size += file_info['size_kb']
            
            # 生成总结
            summary = f"""# Prompt文件总结

## 📊 统计信息

**总文件数:** {len(prompt_files)}个
**总大小:** {total_size:.2f} KB
**最新文件:** {prompt_files[0]['created_time']}

## 📋 类型分布

"""
            
            for prompt_type, count in sorted(type_counts.items()):
                summary += f"- **{prompt_type}:** {count}个文件\n"
            
            summary += f"""
## 📁 最近文件

"""
            
            # 显示最近5个文件
            for file_info in prompt_files[:5]:
                summary += f"- **{file_info['filename']}** ({file_info['prompt_type']}) - {file_info['size_kb']} KB - {file_info['created_time']}\n"
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ 创建prompt总结失败: {e}")
            return "❌ 创建总结失败"
    
    def clean_old_prompts(self, keep_days: int = 7) -> int:
        """清理旧的prompt文件"""
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.now() - timedelta(days=keep_days)
            deleted_count = 0
            
            for file_path in self.prompts_dir.glob("*.txt"):
                file_time = datetime.fromtimestamp(file_path.stat().st_ctime)
                
                if file_time < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
                    logger.info(f"🗑️ 删除旧文件: {file_path.name}")
            
            logger.info(f"🧹 清理完成，删除了 {deleted_count} 个旧文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"❌ 清理旧文件失败: {e}")
            return 0

def main():
    """测试函数"""
    logger.info("🧪 测试Prompt管理器...")
    
    manager = PromptManager()
    
    # 列出现有文件
    files = manager.list_saved_prompts()
    print(f"\n找到 {len(files)} 个prompt文件:")
    for file_info in files:
        print(f"  - {file_info['filename']} ({file_info['prompt_type']}) - {file_info['size_kb']} KB")
    
    # 创建总结
    summary = manager.create_prompt_summary()
    print(f"\n{summary}")

if __name__ == "__main__":
    main()
