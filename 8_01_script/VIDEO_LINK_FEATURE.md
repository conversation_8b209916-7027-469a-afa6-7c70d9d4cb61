# 视频链接管理功能 - 详细说明

## 🎯 功能概述

为了帮助您进行后期视频合并，系统新增了完整的视频链接管理功能。每次生成剧本时，系统会自动创建对应的视频文件清单、复制脚本和完整的prompt记录。

## 📋 核心功能

### 1. **自动视频链接清单** 📎

每个剧本都会生成对应的 `*_video_links.md` 文件，包含：

#### **完整视频信息**
- ✅ **文件路径:** 完整的视频文件绝对路径
- 📊 **文件状态:** 自动检查文件是否存在
- 📏 **文件大小:** 精确的文件大小（MB）
- 🎬 **视频描述:** AI生成的视觉内容描述

#### **分类信息**
- 🎯 **主要类别:** AI预测的主要内容类别
- 🔄 **次要类别:** AI预测的次要内容类别
- 📈 **置信度分数:** AI分类的置信度（0.0-1.0）
- 🏷️ **内容标签:** 具体的视觉特征标签

#### **多媒体内容**
- 🎤 **ASR内容:** 语音识别的文字内容
- 📝 **OCR内容:** 图像中的文字识别内容
- 👁️ **视觉描述:** 详细的画面描述

### 2. **一键视频复制脚本** 📜

每个剧本都会生成对应的 `*_copy_videos.sh` 脚本，特点：

#### **智能复制**
- 📁 自动创建目标目录
- 🔢 按推荐顺序重命名（01_xxx.mp4, 02_xxx.mp4...）
- ✅ 只复制存在的视频文件
- ⚠️ 自动提示缺失的文件

#### **使用方式**
```bash
# 直接执行复制脚本
bash generated_script_copy_videos.sh

# 或者给予执行权限后运行
chmod +x generated_script_copy_videos.sh
./generated_script_copy_videos.sh
```

### 3. **完整Prompt记录** 📝

每次生成都会在 `prompts/` 目录保存完整的prompt文件：

#### **详细信息**
- 🕐 **生成时间:** 精确的生成时间戳
- 🤖 **模型信息:** 使用的AI模型名称
- 📊 **生成参数:** 视频数量、涉及类别等
- 📋 **完整Prompt:** 发送给AI的完整提示词内容

## 🔧 实际使用示例

### 示例1：综合剧本生成

运行后会生成：
```
output/
├── generated_script.md                    # 剧本文件
├── generated_script_video_links.md        # 视频链接清单 ⭐
└── generated_script_copy_videos.sh        # 复制脚本 ⭐

prompts/
└── comprehensive_prompt_20250801_115619.txt  # 完整prompt ⭐
```

### 示例2：类别专门剧本

运行后会生成：
```
output/
├── product_selling_points_script.md              # 产品卖点剧本
├── product_selling_points_script_video_links.md  # 对应视频链接 ⭐
├── product_selling_points_script_copy_videos.sh  # 对应复制脚本 ⭐
├── usage_scenario_script.md                      # 使用场景剧本
├── usage_scenario_script_video_links.md          # 对应视频链接 ⭐
└── usage_scenario_script_copy_videos.sh          # 对应复制脚本 ⭐

prompts/
├── product_selling_points_prompt_*.txt           # 对应prompt ⭐
└── usage_scenario_prompt_*.txt                   # 对应prompt ⭐
```

## 🎬 视频合并工作流

### 方法1：使用复制脚本 + FFmpeg

```bash
# 1. 执行复制脚本
bash generated_script_copy_videos.sh

# 2. 进入视频目录
cd generated_script_videos/

# 3. 创建合并列表
ls *.mp4 | sort | sed 's/^/file /' > video_list.txt

# 4. 使用FFmpeg合并
ffmpeg -f concat -safe 0 -i video_list.txt -c copy final_video.mp4
```

### 方法2：使用Python MoviePy

```python
from moviepy.editor import VideoFileClip, concatenate_videoclips

# 从链接清单中获取视频路径
video_paths = [
    "/data2/jevon/7_31_video_mashup/video/20240425-BM-Q2-SBOX2326M&9-16/scene_001.mp4",
    "/data2/jevon/7_31_video_mashup/video/20241030-BM-Q4-Men's Oxfords-SBOX2326M/scene_001.mp4",
    # ... 更多路径
]

# 加载视频片段
clips = [VideoFileClip(path) for path in video_paths]

# 合并视频
final_video = concatenate_videoclips(clips)

# 输出最终视频
final_video.write_videofile('final_merged_video.mp4')
```

### 方法3：直接使用原始路径

视频链接清单中提供了完整的文件路径，您可以：
- 直接在视频编辑软件中导入
- 编写自定义的合并脚本
- 按需选择特定的视频片段

## 📊 链接清单示例

```markdown
### ✅ 视频 1: scene_001.mp4

**基本信息:**
- **视频ID:** `20240425-BM-Q2-SBOX2326M&9-16/scene_001.mp4`
- **文件路径:** `/data2/jevon/7_31_video_mashup/video/20240425-BM-Q2-SBOX2326M&9-16/scene_001.mp4`
- **数据集:** 20240425-BM-Q2-SBOX2326M&9-16
- **文件大小:** 1.22 MB
- **文件状态:** 存在

**分类信息:**
- **主要类别:** Product Selling Points
- **次要类别:** Design Excellence, Functional Benefits
- **置信度:** 0.95
- **标签:** brown leather brogue-style sneakers, white sole, blue accents

**内容描述:**
- **视觉描述:** A close-up, stylish shot of a person wearing modern brown leather brogue-style sneakers...
- **ASR内容:** 无语音内容
- **OCR内容:** ...:
```

## 🔍 质量保证

### 文件状态检查
- ✅ **存在:** 文件路径正确，可以直接使用
- ❌ **缺失:** 文件不存在，需要检查路径或重新生成

### 统计信息
- 📊 **总视频数:** 推荐的视频总数
- ✅ **文件存在:** 实际存在的文件数量
- ❌ **文件缺失:** 缺失的文件数量
- 📏 **总文件大小:** 所有视频的总大小

## 🚀 优势特点

### 1. **完全自动化**
- 无需手动查找视频文件
- 自动生成所有必要的辅助文件
- 一键复制和合并准备

### 2. **信息完整**
- 包含所有视频的详细信息
- 提供多种合并方法的参考
- 完整的prompt记录便于调试

### 3. **易于使用**
- 清晰的文件组织结构
- 可执行的脚本文件
- 详细的使用说明

### 4. **灵活扩展**
- 支持自定义视频选择
- 可以修改复制脚本的目标目录
- 便于集成到其他工作流中

## 📋 注意事项

1. **文件路径:** 确保原始视频文件路径正确且可访问
2. **存储空间:** 复制视频前确保有足够的存储空间
3. **权限设置:** 确保对目标目录有写入权限
4. **视频格式:** 合并前检查视频格式的兼容性

---

**这个功能大大简化了从剧本生成到视频合并的整个工作流程，让您可以专注于创意内容，而不是技术细节！**
