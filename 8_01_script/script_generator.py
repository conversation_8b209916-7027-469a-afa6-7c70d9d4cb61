#!/usr/bin/env python3
"""
视频剧本生成器
基于视频分类结果，使用Gemini-2.5-Pro生成专业剧本
"""

import json
import requests
import logging
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
from prompt_manager import PromptManager
from video_link_manager import VideoLinkManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScriptGenerator:
    """视频剧本生成器"""
    
    def __init__(self):
        """初始化生成器"""
        # OpenRouter API配置
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.api_key = "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"
        self.model = "google/gemini-2.5-pro"

        # 初始化Prompt管理器
        self.prompt_manager = PromptManager()

        # 初始化视频链接管理器
        self.video_link_manager = VideoLinkManager()
        
        # 请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 类别映射到专家类型
        self.category_to_expert = {
            "Pain Point Description": "痛点类",
            "Product Selling Points": "卖点展示", 
            "Usage Scenario": "场景代入",
            "User Reviews": "用户反馈",
            "Call to Action": "KOL 种草",
            "Other": "情绪表达"
        }
        
        logger.info("✅ 脚本生成器初始化完成")
    
    def load_classification_results(self, json_file_path: str) -> List[Dict]:
        """加载分类结果"""
        logger.info(f"📂 加载分类结果: {json_file_path}")
        
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            logger.info(f"✅ 成功加载 {len(results)} 个视频分类结果")
            return results
            
        except Exception as e:
            logger.error(f"❌ 加载分类结果失败: {e}")
            return []
    
    def load_script_prompt(self, prompt_file_path: str) -> str:
        """加载剧本生成提示词"""
        logger.info(f"📂 加载剧本提示词: {prompt_file_path}")
        
        try:
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                prompt = f.read()
            
            logger.info("✅ 剧本提示词加载成功")
            return prompt
            
        except Exception as e:
            logger.error(f"❌ 加载剧本提示词失败: {e}")
            return ""
    
    def prepare_video_materials_by_category(self, results: List[Dict]) -> tuple[str, List[Dict]]:
        """按类别准备所有视频素材信息"""
        logger.info("🎬 按类别准备所有视频素材信息...")

        # 过滤掉Other类别的视频
        filtered_results = [
            video for video in results
            if video.get('primary_category', '') != 'Other'
        ]

        # 按类别分组
        category_groups = {
            "Pain Point Description": [],
            "Usage Scenario": [],
            "Product Selling Points": [],
            "User Reviews": [],
            "Call to Action": []
        }

        # 分类视频（考虑主要和次要类别）
        for video in filtered_results:
            primary_category = video.get('primary_category', '')
            secondary_categories = video.get('secondary_categories', [])

            # 添加到主要类别
            if primary_category in category_groups:
                category_groups[primary_category].append(video)

            # 添加到次要类别
            for secondary_category in secondary_categories:
                if secondary_category in category_groups and video not in category_groups[secondary_category]:
                    category_groups[secondary_category].append(video)

        # 生成按类别组织的素材文本
        materials_text = ""
        all_selected_videos = []

        # 类别映射到中文名称
        category_chinese_names = {
            "Pain Point Description": "① 痛点描述",
            "Usage Scenario": "② 使用场景",
            "Product Selling Points": "③ 产品卖点",
            "User Reviews": "④ 用户评价",
            "Call to Action": "⑤ Call to Action"
        }

        for category, videos in category_groups.items():
            if not videos:
                continue

            chinese_name = category_chinese_names.get(category, category)
            materials_text += f"### {chinese_name}\n\n"

            # 按置信度排序
            videos.sort(key=lambda x: x.get('confidence_score', 0), reverse=True)

            for video in videos:
                video_id = video.get('video_id', '')
                confidence = video.get('confidence_score', 0)
                reasoning = video.get('reasoning', '无描述')
                tags = video.get('specific_tags', [])

                # 获取ASR内容
                asr_content = self._get_asr_content(video_id)

                # 格式化为：视频号 + 置信度 + ASR内容 + 视觉描述
                materials_text += f"**{video_id}** (置信度: {confidence:.2f})\n"
                materials_text += f"- ASR内容: {asr_content}\n"
                materials_text += f"- 视觉描述: {reasoning}\n"
                materials_text += f"- 标签: {', '.join(tags)}\n\n"

                all_selected_videos.append(video)

        logger.info(f"✅ 按类别准备了 {len(all_selected_videos)} 个视频素材")
        return materials_text, all_selected_videos

    def _get_asr_content(self, video_id: str) -> str:
        """获取视频的ASR内容"""
        try:
            video_info = self.video_link_manager.get_video_info(video_id)
            if video_info and video_info.get('asr'):
                asr_list = video_info['asr']
                if asr_list:
                    return ' '.join(asr_list)
            return "无语音内容"
        except:
            return "无语音内容"
    
    def save_prompt_to_file(self, prompt: str, materials: str, prompt_type: str = "comprehensive",
                           video_count: int = 0, categories_used: List[str] = None):
        """保存完整的prompt到txt文件"""
        try:
            # 使用PromptManager保存prompt
            filepath = self.prompt_manager.save_prompt_with_metadata(
                base_prompt=prompt,
                materials=materials,
                prompt_type=prompt_type,
                video_count=video_count,
                categories_used=categories_used or [],
                model_name=self.model
            )

            return filepath

        except Exception as e:
            logger.error(f"❌ 保存Prompt失败: {e}")
            return None

    def call_gemini_api(self, prompt: str, materials: str, prompt_type: str = "comprehensive",
                       video_count: int = 0, categories_used: List[str] = None) -> str:
        """调用Gemini API生成剧本"""
        logger.info("🤖 调用Gemini API生成剧本...")

        # 保存prompt到文件
        self.save_prompt_to_file(prompt, materials, prompt_type, video_count, categories_used or [])

        # 将materials插入到prompt的"## 4️⃣ 原始素材"部分
        full_prompt = prompt.replace("## 4️⃣ 原始素材\n\n---", f"## 4️⃣ 原始素材\n\n{materials}\n---")
        full_prompt += "\n\n请基于以上素材生成专业的短视频剧本。"
        
        # 构建请求消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": full_prompt
                    }
                ]
            }
        ]
        
        # 构建请求负载
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4000
        }
        
        try:
            response = requests.post(self.api_url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                logger.info("✅ 剧本生成成功")
                return content
            else:
                logger.error(f"❌ API请求失败，状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ API调用失败: {e}")
            return ""
    
    def save_script(self, script_content: str, output_path: str):
        """保存生成的剧本"""
        logger.info(f"💾 保存剧本到: {output_path}")
        
        try:
            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 添加生成信息
            timestamp = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
            header = f"""# 视频剧本生成结果

**生成时间:** {timestamp}  
**生成模型:** {self.model}  
**数据来源:** 视频内容分类结果

---

"""
            
            full_content = header + script_content
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(full_content)
            
            logger.info(f"✅ 剧本保存成功: {output_path}")
            
        except Exception as e:
            logger.error(f"❌ 保存剧本失败: {e}")

    def generate_video_links(self, selected_videos: List[Dict], script_output_path: str, script_type: str = "综合剧本"):
        """生成视频链接文件"""
        logger.info("📎 生成视频链接文件...")

        try:
            # 生成视频链接信息
            video_links = self.video_link_manager.get_video_links_for_script(selected_videos)

            # 生成链接文件路径
            script_dir = Path(script_output_path).parent
            script_name = Path(script_output_path).stem
            links_file = script_dir / f"{script_name}_video_links.md"

            # 创建视频链接文件
            self.video_link_manager.create_video_links_file(video_links, str(links_file), script_type)

            # 创建复制脚本
            copy_script_file = script_dir / f"{script_name}_copy_videos.sh"
            target_dir = script_dir / f"{script_name}_videos"
            self.video_link_manager.create_copy_script(video_links, str(target_dir), str(copy_script_file))

            logger.info(f"📎 视频链接文件: {links_file}")
            logger.info(f"📜 视频复制脚本: {copy_script_file}")

            return str(links_file), str(copy_script_file)

        except Exception as e:
            logger.error(f"❌ 生成视频链接文件失败: {e}")
            return None, None

    def create_merged_video(self, selected_videos: List[Dict], script_output_path: str):
        """创建合并视频"""
        logger.info("🎬 开始创建合并视频...")

        try:
            # 获取视频路径列表
            video_paths = []
            for video in selected_videos:
                video_id = video.get('video_id', '')
                video_path = self.video_link_manager.get_video_path(video_id)
                if video_path and self.video_link_manager.check_video_exists(video_id):
                    video_paths.append(video_path)
                    logger.info(f"✅ 添加视频: {video_id}")
                else:
                    logger.warning(f"⚠️ 跳过缺失视频: {video_id}")

            if not video_paths:
                logger.error("❌ 没有找到可用的视频文件")
                return None

            # 生成输出路径
            script_dir = Path(script_output_path).parent
            script_name = Path(script_output_path).stem
            output_video_path = script_dir / f"{script_name}_merged.mp4"

            # 创建FFmpeg合并脚本
            self._create_ffmpeg_script(video_paths, str(output_video_path))

            logger.info(f"🎬 合并视频准备完成: {output_video_path}")
            return str(output_video_path)

        except Exception as e:
            logger.error(f"❌ 创建合并视频失败: {e}")
            return None

    def _create_ffmpeg_script(self, video_paths: List[str], output_path: str):
        """创建FFmpeg合并脚本"""
        script_dir = Path(output_path).parent
        script_file = script_dir / "merge_videos.sh"

        script_content = f"""#!/bin/bash
# 视频合并脚本
# 生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

echo "开始合并视频..."

# 创建文件列表
cat > video_list.txt << EOF
"""

        for video_path in video_paths:
            script_content += f"file '{video_path}'\n"

        script_content += f"""EOF

# 使用FFmpeg合并视频
ffmpeg -f concat -safe 0 -i video_list.txt -c copy "{output_path}"

echo "视频合并完成: {output_path}"
rm video_list.txt
"""

        # 保存脚本
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)

        # 设置执行权限
        import os
        os.chmod(script_file, 0o755)

        logger.info(f"📜 FFmpeg合并脚本已创建: {script_file}")

    def generate_script(self, classification_file: str, prompt_file: str, output_file: str, max_videos: int = 10):
        """生成剧本的主流程"""
        logger.info("🎬 开始生成视频剧本...")
        
        # 1. 加载分类结果
        results = self.load_classification_results(classification_file)
        if not results:
            logger.error("❌ 无法加载分类结果，终止生成")
            return False
        
        # 2. 加载剧本提示词
        prompt = self.load_script_prompt(prompt_file)
        if not prompt:
            logger.error("❌ 无法加载剧本提示词，终止生成")
            return False
        
        # 3. 准备视频素材（按类别组织所有素材）
        materials, selected_videos = self.prepare_video_materials_by_category(results)
        
        # 4. 调用API生成剧本
        script_content = self.call_gemini_api(prompt, materials, "comprehensive")
        if not script_content:
            logger.error("❌ 剧本生成失败")
            return False
        
        # 5. 保存剧本
        self.save_script(script_content, output_file)

        # 6. 生成合并视频
        self.create_merged_video(selected_videos, output_file)

        logger.info("🎉 视频剧本生成完成！")
        return True

def main():
    """主函数"""
    logger.info("🚀 启动视频剧本生成器...")
    
    # 文件路径配置
    classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
    prompt_file = "/data2/jevon/8_01_script/data/mash_up_script.md"
    output_file = "/data2/jevon/8_01_script/output/generated_script.md"
    
    try:
        # 初始化生成器
        generator = ScriptGenerator()
        
        # 生成剧本
        success = generator.generate_script(
            classification_file=classification_file,
            prompt_file=prompt_file,
            output_file=output_file,
            max_videos=10
        )
        
        if success:
            logger.info("✅ 剧本生成任务完成！")
            logger.info(f"📄 查看生成的剧本: {output_file}")
        else:
            logger.error("❌ 剧本生成任务失败")
            
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
