#!/usr/bin/env python3
"""
简化的剧本生成器
只输出3个核心文件：
1. 完整的输入Prompt
2. LLM的输出剧本
3. 合并视频脚本
"""

import logging
from datetime import datetime
from script_generator import ScriptGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """简化的主函数"""
    logger.info("🚀 启动简化剧本生成器...")
    logger.info("📋 输出内容：")
    logger.info("   1. 完整的输入Prompt（包含5个类别所有素材）")
    logger.info("   2. LLM的输出剧本")
    logger.info("   3. 视频合并脚本")
    
    # 文件路径配置
    classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
    prompt_file = "/data2/jevon/8_01_script/data/mash_up_script.md"
    
    # 生成时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 输出文件路径
    output_script = f"/data2/jevon/8_01_script/output/final_script_{timestamp}.md"
    
    try:
        # 初始化生成器
        generator = ScriptGenerator()
        
        # 生成剧本
        logger.info("🎬 开始生成最终剧本...")
        success = generator.generate_script(
            classification_file=classification_file,
            prompt_file=prompt_file,
            output_file=output_script
        )
        
        if success:
            logger.info("🎉 剧本生成完成！")
            logger.info("📁 输出文件：")
            logger.info(f"   📝 1. 完整Prompt: prompts/comprehensive_prompt_{timestamp}.txt")
            logger.info(f"   🤖 2. LLM输出剧本: {output_script}")
            logger.info(f"   🎬 3. 视频合并脚本: output/merge_videos.sh")
            
            print("\n" + "="*60)
            print("✅ 生成完成！您现在有3个核心文件：")
            print("="*60)
            print(f"📝 1. 输入Prompt: prompts/comprehensive_prompt_{timestamp}.txt")
            print(f"🤖 2. LLM剧本: {output_script}")
            print(f"🎬 3. 合并脚本: output/merge_videos.sh")
            print("="*60)
            print("🎯 使用方法：")
            print("   1. 查看Prompt了解输入内容")
            print("   2. 查看剧本了解AI输出")
            print("   3. 执行 bash output/merge_videos.sh 合并视频")
            print("="*60)
            
        else:
            logger.error("❌ 剧本生成失败")
            
    except Exception as e:
        logger.error(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
