# 简化使用指南

## 🎯 **您只需要3个核心文件**

根据您的需求，系统现在只输出3个核心文件：

### **1. 完整的输入Prompt** 📝
- **文件位置:** `prompts/comprehensive_prompt_*.txt`
- **内容:** 包含5个类别所有413个素材的完整prompt
- **作用:** 了解发送给LLM的完整输入内容

### **2. LLM的输出剧本** 🤖
- **文件位置:** `output/final_script_*.md`
- **内容:** AI生成的专业视频剧本
- **作用:** 实际的剧本内容，包含分镜、台词、时间安排

### **3. 视频合并脚本** 🎬
- **文件位置:** `output/merge_videos.sh`
- **内容:** 可执行的FFmpeg合并命令
- **作用:** 一键合并生成最终视频

## 🚀 **简化使用方式**

### **方法1：使用简化脚本**
```bash
cd /data2/jevon/8_01_script
python simple_generate.py
```

### **方法2：使用原有脚本（选择1）**
```bash
cd /data2/jevon/8_01_script
python run_script_generation.py
# 选择 "1" - 生成综合剧本
```

## 📋 **完整工作流程**

### **步骤1：生成剧本**
```bash
python simple_generate.py
```

### **步骤2：查看输出**
```bash
# 查看输入Prompt（了解AI接收的内容）
cat prompts/comprehensive_prompt_*.txt

# 查看AI生成的剧本
cat output/final_script_*.md

# 查看合并脚本
cat output/merge_videos.sh
```

### **步骤3：合并视频**
```bash
# 执行合并脚本
bash output/merge_videos.sh
```

## 📊 **输出文件说明**

### **Prompt文件特点：**
- **包含素材:** 413个视频素材
- **分类组织:** 5个类别完整展示
- **信息完整:** ASR + 视觉描述 + 标签 + 置信度
- **格式标准:** 按照您要求的① ② ③ ④ ⑤格式

### **剧本文件特点：**
- **AI选择:** 从413个素材中智能选择最佳组合
- **专业结构:** 25-40秒高转化剧本
- **完整信息:** 分镜 + 台词 + 时间安排 + 制作指导

### **合并脚本特点：**
- **自动化:** 一键执行FFmpeg合并
- **路径完整:** 包含所有推荐视频的完整路径
- **即用即合:** 直接生成最终视频文件

## 🎯 **核心优势**

### **简化输出**
- ❌ 不再生成18个复杂文件
- ✅ 只生成3个核心文件
- ✅ 清晰明了，易于理解

### **完整功能**
- ✅ 413个素材完整输入
- ✅ AI智能选择最佳组合
- ✅ 一键视频合并

### **高效工作流**
```
运行脚本 → 3个文件 → 合并视频 → 完成
```

## 📁 **文件结构**

```
8_01_script/
├── simple_generate.py              # 简化生成脚本 ⭐
├── prompts/
│   └── comprehensive_prompt_*.txt  # 完整输入Prompt ⭐
└── output/
    ├── final_script_*.md           # LLM输出剧本 ⭐
    └── merge_videos.sh             # 视频合并脚本 ⭐
```

## 🎬 **最终效果**

1. **输入:** 413个视频素材按5个类别组织
2. **处理:** AI从中选择最佳组合
3. **输出:** 专业剧本 + 合并视频

**简单、清晰、高效！**

---

**使用建议:** 先运行 `python simple_generate.py`，查看生成的3个文件，然后执行合并脚本即可！
