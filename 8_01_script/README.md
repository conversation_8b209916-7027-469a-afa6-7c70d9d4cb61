# 视频剧本生成系统

## 📋 项目概述

基于视频内容分类结果，使用Gemini-2.5-Pro大语言模型自动生成专业的短视频剧本。系统支持综合剧本生成和按类别专门剧本生成。

## 🎯 核心功能

### 1. 综合剧本生成
- 基于最佳15个视频素材生成综合性剧本
- 融合多种内容类型，创造完整的营销故事
- **自动生成视频链接清单和复制脚本，便于后期视频合并** ⭐

### 2. 类别专门剧本生成
- **产品卖点剧本** - 专注产品特性和优势展示
- **使用场景剧本** - 展示产品在实际使用场景中的应用
- **用户评价剧本** - 基于用户反馈和评价的真实剧本
- **行动召唤剧本** - 引导用户购买行为的说服性剧本
- **痛点描述剧本** - 描述用户痛点和问题的共鸣剧本

**注意:** 系统自动排除Other类别的视频，专注于有明确营销价值的内容类别。

## 📁 文件结构

```
8_01_script/
├── README.md                          # 项目说明文档
├── run_script_generation.py           # 一键运行脚本 ⭐
├── script_generator.py                # 核心剧本生成器
├── batch_script_generator.py          # 批量剧本生成器
├── data/                              # 输入数据
│   └── mash_up_script.md              # 剧本生成提示词模板
├── prompts/                           # Prompt文件目录 ⭐
│   └── *_prompt_*.txt                 # 每次生成的完整prompt文件
└── output/                            # 输出结果
    ├── comprehensive_script.md        # 综合剧本
    ├── comprehensive_script_video_links.md    # 视频链接清单 ⭐
    ├── comprehensive_script_copy_videos.sh    # 视频复制脚本 ⭐
    ├── product_selling_points_script.md       # 产品卖点剧本
    ├── product_selling_points_script_video_links.md  # 对应视频链接 ⭐
    ├── usage_scenario_script.md               # 使用场景剧本
    ├── user_reviews_script.md                 # 用户评价剧本
    ├── call_to_action_script.md               # 行动召唤剧本
    └── pain_point_description_script.md       # 痛点描述剧本
```

## 🚀 快速开始

### 一键运行
```bash
cd /data2/jevon/8_01_script
python run_script_generation.py
```

### 菜单选项
1. **生成综合剧本** - 基于最佳15个视频生成综合剧本 + 视频链接清单
2. **批量生成类别专门剧本** - 按5个类别分别生成专门剧本 + 对应视频链接
3. **全部生成** - 同时生成综合剧本和类别剧本 + 所有视频链接
4. **退出** - 退出程序

### 🎬 新增功能：视频链接管理

每次生成剧本时，系统会自动创建：

#### 1. **视频链接清单** (`*_video_links.md`)
- 📋 完整的视频文件信息（路径、大小、状态）
- 🎯 分类信息（主要类别、次要类别、置信度）
- 📝 内容描述（视觉描述、ASR、OCR内容）
- 🔧 FFmpeg和Python合并命令参考

#### 2. **视频复制脚本** (`*_copy_videos.sh`)
- 📜 自动生成的bash脚本
- 📁 按推荐顺序复制视频到指定目录
- 🔢 自动重命名（01_xxx.mp4, 02_xxx.mp4...）
- ✅ 可直接执行：`bash *_copy_videos.sh`

#### 3. **Prompt文件** (`prompts/*.txt`)
- 📝 每次生成的完整prompt内容
- 🕐 包含时间戳和生成参数
- 🔍 便于调试和优化prompt策略

## 🔧 技术规格

### API配置
- **模型:** Google Gemini-2.5-Pro
- **API服务:** OpenRouter
- **请求限制:** 每次请求间隔2秒
- **最大Token:** 4000

### 数据来源
- **分类结果:** `/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json`
- **提示词模板:** `/data2/jevon/8_01_script/data/mash_up_script.md`

### 素材选择策略
- **综合剧本:** 选择置信度最高的15个视频
- **类别剧本:** 按类别筛选，每类选择5-8个最佳视频

## 📊 类别映射

| 分类结果类别 | 剧本专家类型 | 剧本重点 |
|-------------|-------------|----------|
| Product Selling Points | 卖点展示 | 产品特性和优势 |
| Usage Scenario | 场景代入 | 实际使用场景 |
| User Reviews | 用户反馈 | 真实用户体验 |
| Call to Action | KOL种草 | 购买引导 |
| Pain Point Description | 痛点类 | 问题共鸣 |

## 🎬 剧本结构

### 综合剧本结构
1. **开场吸引** - 抓住观众注意力
2. **痛点共鸣** - 引起观众共鸣
3. **产品展示** - 展示产品特性
4. **场景应用** - 实际使用场景
5. **用户证言** - 真实用户反馈
6. **行动召唤** - 引导购买行为

### 类别专门剧本结构
每个类别都有专门的剧本结构和重点，详见各类别的生成提示词。

## 📈 使用建议

### 业务人员
1. **选择综合剧本** - 适合全面的产品营销
2. **选择特定类别剧本** - 针对特定营销目标

### 内容创作者
1. **参考剧本结构** - 学习专业的剧本组织方式
2. **调整内容细节** - 根据实际需求调整具体内容
3. **结合视觉素材** - 配合原始视频素材使用

### 营销团队
1. **A/B测试** - 使用不同类别剧本进行效果测试
2. **渠道适配** - 根据不同平台特点选择合适剧本
3. **效果追踪** - 监控不同剧本的转化效果

## 🔍 质量保证

### 素材筛选
- 基于置信度排序选择最佳素材
- 确保每个类别有足够的代表性素材
- 过滤低质量和无关素材

### 内容生成
- 使用专业的剧本生成提示词
- 针对不同类别定制专门的生成策略
- 确保剧本的逻辑性和连贯性

### 输出格式
- 统一的Markdown格式输出
- 包含生成时间和素材信息
- 清晰的剧本结构和段落划分

## 🛠️ 自定义配置

### 修改素材数量
在相应的生成器中修改 `max_videos` 参数：
- 综合剧本：默认15个视频
- 类别剧本：默认5-8个视频

### 调整API参数
在 `ScriptGenerator` 类中修改：
- `temperature`: 控制创意程度 (默认0.7)
- `max_tokens`: 控制输出长度 (默认4000)

### 自定义提示词
修改 `/data2/jevon/8_01_script/data/mash_up_script.md` 文件来调整剧本生成风格。

## 📋 注意事项

1. **API限制** - 注意OpenRouter的API调用限制
2. **网络连接** - 确保网络连接稳定
3. **文件权限** - 确保输出目录有写入权限
4. **素材质量** - 生成质量依赖于输入素材的质量

## 🎉 预期效果

- **高质量剧本** - 基于AI生成的专业剧本结构
- **多样化内容** - 6种不同类型的专门剧本
- **实用性强** - 可直接用于视频制作和营销
- **效率提升** - 大幅减少剧本创作时间

---

**系统版本:** v1.0.0  
**最后更新:** 2025年8月1日  
**技术支持:** 基于Gemini-2.5-Pro的智能剧本生成
