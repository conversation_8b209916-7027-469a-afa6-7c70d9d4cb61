# 视频剧本生成Prompt - 用户评价剧本

## 📋 基本信息

**生成时间:** 2025年08月01日 14:19:40
**Prompt类型:** user_reviews (用户评价剧本)
**目标模型:** google/gemini-2.5-pro
**文件名:** user_reviews_prompt_20250801_141940.txt

## 📊 生成参数

**使用视频数量:** 0个
**涉及类别:** 无
**生成策略:** 基于AI分类结果，排除Other类别，利用secondary_categories

## 🎯 Prompt内容

以下是发送给AI模型的完整prompt内容：

---

# 🎥 基于专家框架的「镜头级故事脚本」生成 Prompt v3.0

## 0️⃣ 你的身份

你是 **「首席编剧 + 分镜导演 + 营销转化官」**，你的任务是在 **不改动原始画面** 的前提下，重写旁白/字幕，将不超过 10 段素材拼接成一条 **25-40 秒** 的高转化短视频。

---

## 1️⃣ 交付目标

1. 精选 ≤10 段素材，输出 25-40 秒成片  
2. 每段素材需标注专家类型（从 7 种中选择其一）  
3. 重写 ASR（旁白）与 OCR（字幕），服务故事叙事  
4. 前 0-3 秒为 Hook，每 5 秒必须有内容刺激，结尾需 CTA  
5. 输出必须通过结构自检表校验

---

## 2️⃣ 专家类型 & 对应故事模板

| 类型 | 专家结构 | 对应故事模板 |
|------|------------|----------------|
| 痛点类 | ①痛点 → ②卖点 → ③场景 → ④CTA | 「崩溃一天」→「救星降临」→「新生活」→「召唤」 |
| 卖点展示 | ①卖点 → ②场景 → ③口碑 → ④CTA | 「神秘登场」→「极限挑战」→「见证震惊」→「你来试」 |
| 场景代入 | ①场景 → ②痛点 → ③卖点 → ④CTA | 「平凡一天」→「突发困扰」→「自然融入」→「生活升级」 |
| 用户反馈 | ①口碑 → ②痛点 → ③卖点 → ④CTA | 「朋友安利」→「同感困扰」→「真香改变」→「快来试」 |
| KOL 种草 | ①场景 → ②卖点 → ③口碑 → ④CTA | 「达人发现」→「专业解析」→「粉丝验证」→「立即拔草」 |
| 情绪表达 | ①情绪 → ②痛点 → ③卖点暗示 → ④场景 → ⑤CTA | 「生活应该」→「总有烦恼」→「其实可好」→「即刻拥有」 |
| 反差/反转 | ①旧体验 → ②新体验 → ③对比 → ④CTA | 「以为如此」→「结果却」→「震撼反差」→「来体验」 |

> 📝 可混合使用模板，但结构顺序必须保持一致。

---

## 3️⃣ 工作流程

1. 阅读原始素材 → 二次标记专家类型  
2. 筛选片段 → 优先级：画质 > 信息量 > 时长 > 多样性  
3. 选择匹配故事模板 → 编排节奏  
4. 重写 ASR / OCR（保证嘴型基本匹配）  
5. 输出剧本并运行结构自检表

---

## 4️⃣ 输入格式

请按以下格式提供原始素材：

```
视频号-片段号 | 时长 | 视觉描述 | 原ASR | 原OCR
```

---

## 5️⃣ 输出格式（务必遵守）

### 🎯 专家框架选择

- 类型：XXX  
- 选择依据：XXX  
- 故事主题：XXX  

### 📚 故事概念

- 标题：XXX  
- 故事模板：XXX  
- 核心冲突：XXX  
- 情感基调：XXX  
- 主要角色：XXX  

### 🎬 分幕剧本（总时长 X 秒 / 共 X 段）

| # | 素材ID | 原时长→用时 | 类型 | 视觉描述 | 改写ASR | 改写OCR | 音乐/音效 | 情感/叙事功能 |
|---|--------|-------------|------|----------|----------|----------|-----------|----------------|
| 1 | 2-01   | 3.5→3.0s    | 痛点类 | …        | …        | …        | …         | Hook           |
| … | …      | …           | …     | …        | …        | …        | …         | …              |
| X | 7-02   | 4.8→4.5s    | CTA   | …        | …        | …        | …         | CTA            |

### 🔄 结构自检

- ✅ 专家结构①：……  
- ✅ 专家结构②：……  
- ✅ 专家结构③：……  
- ✅ 专家结构④：……（⑤ 如有）  

### 🚀 爆款要素

- 3 秒 Hook：XXX  
- 情绪价值：XXX  
- 代入视角：XXX  
- 节奏控制：0-3s / 3-8s / … / CTA  

### ⏱ 时长统计

痛点 Xs ｜ 场景 Xs ｜ 卖点 Xs ｜ 口碑 Xs ｜ KOL Xs ｜ 情绪 Xs ｜ 反差 Xs ｜ CTA Xs ｜ 总计 Xs  

### 💡 优化建议

1. …  
2. …

---

## 6️⃣ 关键守则

1. 全中文输出，必要英文可保留  
2. 素材 ≤10 段，成片总长 25-40 秒  
3. 旁白需嘴型基本匹配；无声镜头可自由添加 VO  
4. CTA = 行动动词 + 跳转方式（如扫码、点击、关注等）

---


## 用户评价剧本生成要求

请基于提供的用户评价类视频素材，生成一个真实可信的用户反馈剧本。重点关注：

1. **真实性** - 模拟真实用户的语言和表达
2. **多样性** - 展示不同用户的使用体验
3. **具体细节** - 包含具体的使用感受和细节
4. **情感共鸣** - 引起观众的情感共鸣

剧本结构：
- 用户介绍（简单背景介绍）
- 购买原因（为什么选择这个产品）
- 使用体验（详细使用感受）
- 意外惊喜（超出预期的地方）
- 推荐理由（为什么推荐给别人）


### ④ 用户评价

**SBOX2326M-0425-KOL/scene_001.mp4** (置信度: 0.95)
- ASR内容:  If girls are allowed to wear makeup to work, I should be able to wear these.
- 视觉描述: The video features a first-person perspective unboxing sequence, which is a strong indicator of the 'User Reviews' category. The personal statement in the ASR ('If girls are allowed to wear makeup to work, I should be able to wear these') further supports this classification. The secondary category 'Product Selling Points' is indicated by the close-up of the shoes and the description of their design features. The 'Usage Scenario' is suggested by the mention of workplace appropriateness in the ASR. The specific tags reflect the unboxing, personal perspective, shoe design, and context of use.
- 标签: unboxing, first-person perspective, dress sneakers, personal statement, workplace fashion

**SBOX2326M-0425-KOL/scene_006.mp4** (置信度: 0.95)
- ASR内容:  but they're super comfortable.
- 视觉描述: The primary focus is on showcasing the comfort of the shoe through a close-up demonstration of the insole, which aligns with the 'Product Selling Points' category. The audio transcript and on-screen caption emphasize comfort ('super comfortable'), a key selling point. The secondary category 'User Reviews' is included due to the first-person presentation style and personal endorsement of comfort. The visual and audio elements strongly match the indicators for 'Product Selling Points' with clear feature highlights and positive product attributes.
- 标签: comfort, insole, shoe demonstration, positive feedback, visual close-up

**BM_Urgent-488_0418_V1/scene_001.mp4** (置信度: 0.90)
- ASR内容: 无语音内容
- 视觉描述: The visual description clearly depicts an unboxing sequence, which is a hallmark of user-generated content or review-style videos. The top-down view and focus on revealing the shoes align with first-person perspective content. While there are no audio cues, the visual alone strongly suggests a user review context. The secondary category 'Product Selling Points' is included because the description highlights the shoes' appearance (modern, casual dress shoes) which could be part of feature showcasing, though the primary focus remains on the unboxing experience.
- 标签: unboxing, casual dress shoes, top-down view, white and brown shoes, shoebox opening

**BM_Urgent-488_0418_V1/Urgent-488_0418_V1_scene_001.mp4** (置信度: 0.90)
- ASR内容: 无语音内容
- 视觉描述: The visual description clearly shows an unboxing sequence, which is a hallmark of user-generated content or first-person reviews. The absence of any audio transcript does not detract from this classification, as the visual alone strongly suggests a user's personal experience with the product. The secondary category 'Product Selling Points' is included due to the focus on the shoes' modern design and the detailed reveal of the product, which could also serve to highlight its features. The specific tags reflect the key elements of the content, such as the unboxing process and the type of shoes shown.
- 标签: unboxing, casual dress shoes, top-down view, modern design, first-person perspective

**SBOX2326M-KOL/scene_008.mp4** (置信度: 0.90)
- ASR内容:  do these make me look taller? But they're super comfortable.
- 视觉描述: The primary focus of the content is on the discomfort of the shoes, as indicated by the audio transcript ('they're so uncomfortable') and the visual description pointing to the insole where the size is located. This matches the Pain Point Description category, which includes references to discomfort and product shortcomings. The secondary category of Product Selling Points is included due to the mention of the shoes making the wearer look taller, which could be seen as a feature highlight. The User Reviews category is also a secondary match due to the first-person perspective in the audio. The specific tags are derived from key elements in the description and audio.
- 标签: discomfort, insole, size, taller, uncomfortable

**SBOX2326M-0425-03/scene_004.mp4** (置信度: 0.90)
- ASR内容:  I've been short my whole entire life and now I finally get to feel like when it's
- 视觉描述: The content primarily addresses a personal pain point (being short) and suggests the shoes as a solution, matching Pain Point Description indicators ('I've been short my whole entire life'). Secondary categories include Product Selling Points (highlighting thick soles for height) and User Reviews (first-person narrative style). The visual text and ASR strongly emphasize overcoming a personal limitation, which is a classic pain point marketing approach.
- 标签: height increase, dress shoes, personal struggle, thick-soled, first-person narrative

**SBOX2326M-0425-03/scene_006.mp4** (置信度: 0.90)
- ASR内容:  but they're super comfortable.
- 视觉描述: The visual description focuses on showcasing the shoe's interior (orange and blue patterned insole) with expressive presentation, which aligns with Product Selling Points' emphasis on feature highlights. The audio transcript 'but they're super comfortable' reinforces this by highlighting a functional benefit. The secondary category User Reviews is considered due to the first-person style presentation, though it's less prominent than the product feature focus.
- 标签: comfortable insole, expressive presentation, shoe interior showcase

**SBOX2326M-0425-KOL/scene_005.mp4** (置信度: 0.90)
- ASR内容:  Finally get to feel like what it's like to be six foot. Not only do these make me look taller.
- 视觉描述: The content primarily focuses on highlighting product benefits (making the wearer look/feel taller) through close-up usage demonstration and on-screen text, matching Product Selling Points criteria. Secondary elements include first-person perspective audio ('Finally get to feel...') suggesting User Reviews, and environmental context (wood-floor room) indicating Usage Scenario. The brown sandal comparison is a minor element not strong enough for Pain Point classification.
- 标签: height enhancement, comfort claims, visual comparison, on-screen text, first-person narration

**SBOX2326M-0425-KOL/scene_003.mp4** (置信度: 0.90)
- ASR内容:  is taller. Am I wrong for this?
- 视觉描述: The primary focus is on highlighting product features ('shoes that make me 3 inches taller', 'Breathe Easy'), which aligns with the Product Selling Points category. The text overlays and direct question ('Am I wrong for this?') suggest elements of User Reviews and Call to Action, though these are secondary to the main product feature showcase. The visual and audio elements strongly emphasize the product's benefits rather than pain points or neutral usage scenarios.
- 标签: height increase, breathability, size availability, text overlays, male voiceover

**SBOX2326M-KOL/scene_007.mp4** (置信度: 0.85)
- ASR内容:  to be like what it's like to be six foot.
- 视觉描述: The primary focus of the video is on showcasing the product's features, specifically the 'Men's Comfortable Casual Oxford Shoes,' as indicated by the close-up shot and the text overlays highlighting comfort and style. The secondary category 'Usage Scenario' is applicable due to the indoor setting and the act of trying on the shoes, which suggests a neutral environment showcase. The 'User Reviews' category is a secondary match because of the first-person perspective in trying on the shoe and the informal tone of the text overlay ('I hope you can find the size'). The ASR transcript does not provide significant additional context but does not contradict the classification. The confidence score is slightly reduced due to the limited audio content.
- 标签: Close-up shot, Comfortable Casual Oxford Shoes, Text overlays, Indoor setting, Trying on shoes

**SBOX2326M--KOL/scene_002.mp4** (置信度: 0.85)
- ASR内容:  They're also a super nice lesson.
- 视觉描述: The visual description focuses on a close-up shot of a shoe with specific details being pointed out, such as the size information, which aligns with the 'Product Selling Points' category. The on-screen text and yellow arrow reinforce this as a feature highlight. The audio transcript is unclear but does not contradict the primary category. The secondary category 'Usage Scenario' is considered due to the neutral environment and the man's casual appearance, while 'User Reviews' is a potential match if the man is providing personal feedback, though this is less clear from the given information.
- 标签: close-up shot, size information, on-screen text, shoe detail, demonstration



请基于以上素材生成专业的短视频剧本。