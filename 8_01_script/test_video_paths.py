#!/usr/bin/env python3
"""
测试视频路径映射
验证video_id和实际文件路径的对应关系
"""

import json
import os
import logging
from video_link_manager import VideoLinkManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """测试视频路径映射"""
    logger.info("🧪 测试视频路径映射...")
    
    try:
        # 初始化视频链接管理器
        manager = VideoLinkManager()
        
        # 加载分类结果
        classification_file = "/data2/jevon/7_31_video_mashup_merge/reports/video_classification_results_enhanced.json"
        with open(classification_file, 'r', encoding='utf-8') as f:
            classification_results = json.load(f)
        
        logger.info(f"✅ 加载了 {len(classification_results)} 个分类结果")
        
        # 测试几个具体的video_id
        test_video_ids = [
            "20241120-BM-Q4-BM_SBOX2326M_2023-07-20-Q3/SBOX2326M_2023-07-20-Q3_scene_002.mp4",
            "20240425-BM-Q2-SBOX2326M&9-16/scene_001.mp4",
            "SBOX2326M-KOL/scene_008.mp4"
        ]
        
        print("\n" + "="*80)
        print("🔍 测试具体video_id的路径映射")
        print("="*80)
        
        for video_id in test_video_ids:
            print(f"\n📹 测试video_id: {video_id}")
            
            # 获取视频路径
            video_path = manager.get_video_path(video_id)
            
            if video_path:
                print(f"✅ 找到路径: {video_path}")
                
                # 检查文件是否存在
                if os.path.exists(video_path):
                    file_size = os.path.getsize(video_path) / (1024 * 1024)  # MB
                    print(f"✅ 文件存在，大小: {file_size:.2f} MB")
                else:
                    print(f"❌ 文件不存在: {video_path}")
            else:
                print(f"❌ 未找到路径映射")
        
        # 统计总体情况
        print("\n" + "="*80)
        print("📊 总体统计")
        print("="*80)
        
        total_videos = len(classification_results)
        found_paths = 0
        existing_files = 0
        missing_files = 0
        
        for result in classification_results:
            video_id = result.get('video_id', '')
            video_path = manager.get_video_path(video_id)
            
            if video_path:
                found_paths += 1
                if os.path.exists(video_path):
                    existing_files += 1
                else:
                    missing_files += 1
        
        print(f"📋 总视频数: {total_videos}")
        print(f"✅ 找到路径: {found_paths} ({found_paths/total_videos*100:.1f}%)")
        print(f"📁 文件存在: {existing_files} ({existing_files/total_videos*100:.1f}%)")
        print(f"❌ 文件缺失: {missing_files} ({missing_files/total_videos*100:.1f}%)")
        print(f"🔍 未映射: {total_videos - found_paths} ({(total_videos - found_paths)/total_videos*100:.1f}%)")
        
        # 显示一些未映射的video_id示例
        unmapped_examples = []
        for result in classification_results[:10]:  # 只检查前10个
            video_id = result.get('video_id', '')
            if not manager.get_video_path(video_id):
                unmapped_examples.append(video_id)
        
        if unmapped_examples:
            print(f"\n❌ 未映射的video_id示例:")
            for example in unmapped_examples[:5]:  # 只显示前5个
                print(f"   - {example}")
        
        # 显示映射统计
        print(f"\n📊 路径映射总数: {len(manager.video_path_mapping)}")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
