# -*- coding: utf-8 -*-
"""
Google Gemini视频API核心架构 - 精简版
包含：重试机制、错误处理、文件验证
"""
import time
import os
from google import genai
from google.genai import types
from google.genai.errors import ClientError

# --- 配置 ---
GOOGLE_API_KEY = 'YOUR_GOOGLE_API_KEY_HERE'
MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB
DEFAULT_MAX_RETRIES = 3
DEFAULT_BASE_DELAY = 12

# --- 核心视频处理类 ---
class GeminiVideoProcessor:
    """Gemini视频处理器"""
    
    def __init__(self, api_key=None, max_retries=DEFAULT_MAX_RETRIES, base_delay=DEFAULT_BASE_DELAY):
        self.api_key = api_key or GOOGLE_API_KEY
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.client = genai.Client(api_key=self.api_key)
    
    def validate_video_file(self, video_file_path):
        """验证视频文件"""
        if not os.path.exists(video_file_path):
            raise FileNotFoundError(f"视频文件不存在: {video_file_path}")
        
        file_size = os.path.getsize(video_file_path)
        print(f"视频文件大小: {file_size / (1024*1024):.2f} MB")
        
        if file_size > MAX_FILE_SIZE:
            print(f"警告：视频文件可能超过{MAX_FILE_SIZE/(1024*1024)}MB限制")
        
        return file_size
    
    def process_video_with_retry(self, video_file_path, prompt="简要总结这个视频的内容。"):
        """带重试机制的视频处理"""
        self.validate_video_file(video_file_path)
        
        for attempt in range(self.max_retries):
            try:
                print(f"尝试第 {attempt + 1} 次处理视频...")
                
                # 读取视频文件
                with open(video_file_path, 'rb') as f:
                    video_bytes = f.read()
                
                # 发送请求
                response = self.client.models.generate_content(
                    model="models/gemini-2.5-pro",
                    contents=types.Content(
                        parts=[
                            types.Part(
                                inline_data=types.Blob(data=video_bytes, mime_type='video/mp4')
                            ),
                            types.Part(text=prompt)
                        ]
                    )
                )
                
                return response.text
                
            except ClientError as e:
                if e.status_code == 429:  # RESOURCE_EXHAUSTED
                    retry_delay = self.base_delay * (2 ** attempt)  # 指数退避
                    print(f"配额限制错误，等待 {retry_delay} 秒后重试...")
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        print("已达到最大重试次数，请稍后再试")
                        return None
                else:
                    print(f"API错误 (状态码: {e.status_code}): {str(e)}")
                    return None
                    
            except Exception as e:
                print(f"其他错误: {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.base_delay)
                    continue
                return None
        
        return None
    
    def batch_process_videos(self, video_paths, prompt="简要总结这个视频的内容。"):
        """批量处理视频"""
        results = {}
        
        for video_path in video_paths:
            print(f"\n处理视频: {os.path.basename(video_path)}")
            result = self.process_video_with_retry(video_path, prompt)
            results[video_path] = result
            
            # 批量处理间隔
            time.sleep(2)
        
        return results

# --- 错误处理策略 ---
class ErrorHandler:
    """错误处理和重试策略"""
    
    @staticmethod
    def classify_error(error):
        """分类错误类型"""
        if isinstance(error, ClientError):
            if error.status_code == 429:
                return "rate_limit"
            elif error.status_code >= 500:
                return "server_error"
            else:
                return "client_error"
        elif isinstance(error, FileNotFoundError):
            return "file_not_found"
        else:
            return "unknown_error"
    
    @staticmethod
    def calculate_backoff_delay(attempt, base_delay=12, max_delay=300):
        """计算退避延迟"""
        delay = min(max_delay, base_delay * (2 ** attempt))
        return delay

# --- 使用示例 ---
def example_usage():
    """使用示例"""
    # 初始化处理器
    processor = GeminiVideoProcessor(
        api_key=GOOGLE_API_KEY,
        max_retries=3,
        base_delay=12
    )
    
    # 单个视频处理
    video_file = "/path/to/your/video.mp4"
    
    try:
        result = processor.process_video_with_retry(video_file)
        if result:
            print("\n=== 视频分析结果 ===")
            print(result)
        else:
            print("\n处理失败")
    except Exception as e:
        print(f"处理过程中出错: {e}")
    
    # 批量处理示例
    video_files = [
        "/path/to/video1.mp4",
        "/path/to/video2.mp4"
    ]
    
    batch_results = processor.batch_process_videos(video_files)
    for video_path, result in batch_results.items():
        print(f"\n{os.path.basename(video_path)}: {'成功' if result else '失败'}")

# --- 工具函数 ---
def validate_api_key(api_key):
    """验证API密钥"""
    if not api_key or api_key == 'YOUR_GOOGLE_API_KEY_HERE':
        raise ValueError("请设置有效的Google API密钥")
    return True

def get_video_info(video_path):
    """获取视频文件信息"""
    if not os.path.exists(video_path):
        return None
    
    file_size = os.path.getsize(video_path)
    return {
        'path': video_path,
        'size_mb': file_size / (1024 * 1024),
        'size_bytes': file_size,
        'exists': True
    }

# --- 配置验证 ---
def setup_client(api_key=None):
    """设置客户端"""
    key = api_key or GOOGLE_API_KEY
    validate_api_key(key)
    return genai.Client(api_key=key)

if __name__ == "__main__":
    # 检查配置
    if GOOGLE_API_KEY == 'YOUR_GOOGLE_API_KEY_HERE':
        print("⚠️ 请先设置Google API密钥")
    else:
        example_usage()
