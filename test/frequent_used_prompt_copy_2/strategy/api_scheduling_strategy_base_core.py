# -*- coding: utf-8 -*-
"""
API调度策略基础版 - 标杆参考文件

核心功能：
- 令牌桶限流器：控制API调用频率
- 优先级重试队列：智能重试失败任务
- 线程安全存储：防止数据竞争和索引错误
- 并发控制：动态调整工作线程数

设计原则：
- 线程安全：所有操作都考虑多线程环境
- 原子操作：文件写入使用原子替换
- 错误恢复：完善的异常处理机制
- 索引准确：唯一标识符确保数据一致性
"""
import os
import json
import requests
import time
import threading
import random
import heapq
from pathlib import Path
from typing import Dict, Any, Optional

# --- 基础配置 ---
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_TOKEN = os.environ.get("SILICONFLOW_API_TOKEN", "YOUR_TOKEN_HERE")
DEFAULT_MODEL = "Pro/deepseek-ai/DeepSeek-V3"
MAX_RETRIES = 10
API_RATE_LIMIT = 30
MAX_FIELD_WORKERS = 3
MIN_WORKERS = 2

# --- 基础令牌桶限流器 ---
class TokenBucketRateLimiter:
    """基础令牌桶限流器实现"""
    def __init__(self, tokens_per_minute=60):
        self.tokens_per_minute = tokens_per_minute
        self.tokens_per_second = tokens_per_minute / 60.0
        self.bucket_capacity = int(tokens_per_minute * 1.5)
        self.lock = threading.Lock()
        self.current_tokens = self.bucket_capacity
        self.last_refill_time = time.time()
        self.rate_limit_hit_count = 0
        self.consecutive_failures = 0
        self.current_workers = MAX_FIELD_WORKERS

    def _refill_tokens(self):
        """重新填充令牌"""
        now = time.time()
        time_passed = now - self.last_refill_time
        new_tokens = time_passed * self.tokens_per_second
        
        if new_tokens > 0:
            self.current_tokens = min(self.bucket_capacity, self.current_tokens + new_tokens)
            self.last_refill_time = now

    def get_token(self, timeout=None):
        """尝试获取一个令牌"""
        with self.lock:
            self._refill_tokens()
            
            if self.current_tokens < 1:
                required_time = (1 - self.current_tokens) / self.tokens_per_second
                
                if timeout is not None and required_time > timeout:
                    return False
                
                self.lock.release()
                try:
                    jitter = random.uniform(0, 0.1 * required_time)
                    time.sleep(required_time + jitter)
                finally:
                    self.lock.acquire()
                
                self._refill_tokens()
                
                if self.current_tokens < 1:
                    return False
            
            self.current_tokens -= 1
            return True

    def wait_if_needed(self):
        """等待直到获取到令牌"""
        while not self.get_token():
            time.sleep(0.1)

    def report_rate_limit_hit(self):
        """报告遇到了速率限制"""
        with self.lock:
            self.rate_limit_hit_count += 1
            self.consecutive_failures += 1

            # 动态减少并发数
            if self.consecutive_failures > 5 and self.current_workers > MIN_WORKERS:
                self.current_workers = max(MIN_WORKERS, self.current_workers - 1)

    def report_success(self):
        """报告请求成功"""
        with self.lock:
            self.consecutive_failures = 0

            # 逐渐恢复并发数
            if self.rate_limit_hit_count > 0 and self.current_workers < MAX_FIELD_WORKERS:
                if self.rate_limit_hit_count < 5:
                    self.current_workers = min(MAX_FIELD_WORKERS, self.current_workers + 1)
                    self.rate_limit_hit_count = 0

    def get_current_workers(self):
        """获取当前建议的工作线程数"""
        with self.lock:
            return self.current_workers

# --- 基础优先级重试队列 ---
class PriorityRetryQueue:
    """基础优先级重试队列"""
    def __init__(self):
        self.queue = []
        self.entry_count = 0
        self.lock = threading.Lock()
        self.retry_counts = {}

    def add_task(self, field_key, unique_descriptions, prompt_template, file_name, retry_count=0):
        """添加任务到优先级队列"""
        with self.lock:
            task_key = (field_key, file_name)
            self.retry_counts[task_key] = max(self.retry_counts.get(task_key, 0), retry_count)

            priority = -self.retry_counts[task_key]  # 重试次数多的优先级高
            heapq.heappush(self.queue, (priority, self.entry_count, field_key, unique_descriptions, prompt_template, file_name))
            self.entry_count += 1

    def get_all_tasks_sorted(self):
        """获取所有任务，按优先级排序"""
        with self.lock:
            sorted_tasks = sorted(self.queue)
            tasks = [(item[2], item[3], item[4], item[5], -item[0]) for item in sorted_tasks]
            self.queue = []
            self.retry_counts = {}
            return tasks

    def is_empty(self):
        with self.lock:
            return len(self.queue) == 0

    def size(self):
        with self.lock:
            return len(self.queue)

# --- 线程安全的结果存储管理器 ---
class ThreadSafeResultStorage:
    """
    线程安全的结果存储管理器 - 企业级可靠性版本

    核心改进：
    1. 立即保存机制 - 防止进度丢失
    2. 多重备份策略 - 防止单点故障
    3. 一致性验证 - 自动检测和修复
    4. 定期检查 - 实时监控数据完整性
    """

    def __init__(self, output_dir: str):
        """
        初始化存储管理器

        Args:
            output_dir (str): 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.file_locks = {}  # 文件锁字典
        self.lock_manager = threading.Lock()  # 管理文件锁的锁

        # 可靠性增强
        self.last_consistency_check = time.time()
        self.consistency_check_interval = 300  # 每5分钟检查一次

        # 启动时验证数据一致性
        self._startup_consistency_check()

    def _startup_consistency_check(self):
        """启动时进行一致性检查"""
        try:
            # 扫描所有结果文件，验证数据完整性
            for result_file in self.output_dir.glob("*.json"):
                if result_file.name.endswith('.tmp') or result_file.name.endswith('.lock'):
                    continue

                try:
                    with open(result_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 验证基本结构
                    if 'results' not in data or 'metadata' not in data:
                        self._repair_file_structure(result_file, data)

                except Exception:
                    # 损坏的文件尝试从备份恢复
                    self._attempt_file_recovery(result_file)

        except Exception:
            pass  # 静默处理启动检查错误

    def _repair_file_structure(self, file_path: Path, data: Dict[str, Any]):
        """修复文件结构"""
        if 'results' not in data:
            data['results'] = {}
        if 'metadata' not in data:
            data['metadata'] = {
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_tasks': len(data.get('results', {})),
                'last_updated': time.strftime('%Y-%m-%d %H:%M:%S')
            }

        # 原子写入修复后的数据
        temp_file = file_path.with_suffix('.tmp')
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        temp_file.replace(file_path)

    def _attempt_file_recovery(self, file_path: Path):
        """尝试从备份恢复文件"""
        backup_patterns = [
            file_path.with_suffix('.backup'),
            file_path.parent / f"{file_path.stem}_backup.json"
        ]

        for backup_path in backup_patterns:
            if backup_path.exists():
                try:
                    backup_path.replace(file_path)
                    break
                except Exception:
                    continue

    def _get_file_lock(self, file_path: Path) -> threading.Lock:
        """获取文件对应的锁对象"""
        with self.lock_manager:
            file_key = str(file_path)
            if file_key not in self.file_locks:
                self.file_locks[file_key] = threading.Lock()
            return self.file_locks[file_key]

    def save_result_safely(self,
                          file_name: str,
                          task_key: str,
                          result_data: Dict[str, Any],
                          metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        线程安全地保存结果到JSON文件 - 企业级可靠性版本

        改进点：
        1. 多重备份策略
        2. 立即保存机制
        3. 数据完整性验证
        4. 定期一致性检查

        Args:
            file_name (str): 输出文件名
            task_key (str): 任务唯一标识符
            result_data (Dict): 要保存的结果数据
            metadata (Dict, optional): 额外的元数据

        Returns:
            bool: 保存是否成功
        """
        output_file = self.output_dir / file_name
        file_lock = self._get_file_lock(output_file)

        with file_lock:
            try:
                # 读取现有数据
                existing_data = self._load_existing_data_safely(output_file)

                # 添加新结果
                existing_data['results'][task_key] = {
                    'data': result_data,
                    'processed_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'thread_id': threading.current_thread().ident
                }

                # 更新元数据
                if metadata:
                    existing_data['metadata'].update(metadata)
                existing_data['metadata']['last_updated'] = time.strftime('%Y-%m-%d %H:%M:%S')
                existing_data['metadata']['total_tasks'] = len(existing_data['results'])

                # 多重保存策略
                success_count = self._save_with_multiple_backups(output_file, existing_data)

                # 定期一致性检查
                self._periodic_consistency_check()

                return success_count > 0

            except Exception:
                return False

    def _load_existing_data_safely(self, output_file: Path) -> Dict[str, Any]:
        """安全地加载现有数据"""
        existing_data = {}

        # 尝试加载主文件
        if output_file.exists():
            try:
                with open(output_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except Exception:
                # 主文件损坏，尝试备份文件
                backup_file = output_file.with_suffix('.backup')
                if backup_file.exists():
                    try:
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)
                    except Exception:
                        existing_data = {}

        # 确保基本结构存在
        if 'results' not in existing_data:
            existing_data['results'] = {}
        if 'metadata' not in existing_data:
            existing_data['metadata'] = {
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_tasks': 0,
                'last_updated': None
            }

        return existing_data

    def _save_with_multiple_backups(self, output_file: Path, data: Dict[str, Any]) -> int:
        """多重备份保存策略"""
        success_count = 0

        # 1. 保存主文件
        if self._atomic_save(output_file, data):
            success_count += 1

        # 2. 保存备份文件
        backup_file = output_file.with_suffix('.backup')
        if self._atomic_save(backup_file, data):
            success_count += 1

        # 3. 时间戳备份（每小时一个）
        current_time = time.strftime('%Y%m%d_%H')
        timestamp_backup = output_file.parent / f"{output_file.stem}_{current_time}.json"
        if not timestamp_backup.exists():  # 避免重复创建
            if self._atomic_save(timestamp_backup, data):
                success_count += 1

        return success_count

    def _atomic_save(self, file_path: Path, data: Dict[str, Any]) -> bool:
        """原子保存操作"""
        try:
            temp_file = file_path.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            temp_file.replace(file_path)
            return True
        except Exception:
            return False

    def _periodic_consistency_check(self):
        """定期一致性检查"""
        current_time = time.time()
        if current_time - self.last_consistency_check > self.consistency_check_interval:
            self.last_consistency_check = current_time
            self._verify_data_consistency()

    def _verify_data_consistency(self):
        """验证数据一致性"""
        try:
            for result_file in self.output_dir.glob("*.json"):
                if result_file.name.endswith(('.tmp', '.backup')):
                    continue

                # 验证主文件与备份文件的一致性
                backup_file = result_file.with_suffix('.backup')
                if backup_file.exists():
                    try:
                        with open(result_file, 'r', encoding='utf-8') as f:
                            main_data = json.load(f)
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            backup_data = json.load(f)

                        # 如果主文件数据较少，用备份文件恢复
                        main_count = len(main_data.get('results', {}))
                        backup_count = len(backup_data.get('results', {}))

                        if backup_count > main_count:
                            self._atomic_save(result_file, backup_data)

                    except Exception:
                        continue
        except Exception:
            pass  # 静默处理一致性检查错误

    def load_existing_results(self, file_name: str) -> Dict[str, Any]:
        """
        加载现有的结果数据 - 使用安全加载机制

        Args:
            file_name (str): 文件名

        Returns:
            Dict: 现有的结果数据
        """
        output_file = self.output_dir / file_name
        file_lock = self._get_file_lock(output_file)

        with file_lock:
            return self._load_existing_data_safely(output_file)

    def is_task_completed(self, file_name: str, task_key: str) -> bool:
        """
        检查任务是否已完成

        Args:
            file_name (str): 文件名
            task_key (str): 任务标识符

        Returns:
            bool: 任务是否已完成
        """
        existing_data = self.load_existing_results(file_name)
        return task_key in existing_data.get('results', {})

    def get_completion_stats(self, file_name: str) -> Dict[str, int]:
        """
        获取完成统计信息

        Args:
            file_name (str): 文件名

        Returns:
            Dict: 统计信息
        """
        existing_data = self.load_existing_results(file_name)
        results = existing_data.get('results', {})

        return {
            'total_completed': len(results),
            'last_updated': existing_data.get('metadata', {}).get('last_updated'),
            'created_at': existing_data.get('metadata', {}).get('created_at')
        }

    def recover_from_backups(self, file_name: str) -> bool:
        """
        从备份文件恢复数据

        Args:
            file_name (str): 要恢复的文件名

        Returns:
            bool: 恢复是否成功
        """
        output_file = self.output_dir / file_name
        file_lock = self._get_file_lock(output_file)

        with file_lock:
            # 查找可用的备份文件
            backup_candidates = [
                output_file.with_suffix('.backup'),
                *sorted(self.output_dir.glob(f"{output_file.stem}_*.json"), reverse=True)
            ]

            best_backup = None
            max_tasks = 0

            # 选择任务数最多的备份文件
            for backup_file in backup_candidates:
                if backup_file.exists() and backup_file != output_file:
                    try:
                        with open(backup_file, 'r', encoding='utf-8') as f:
                            backup_data = json.load(f)

                        task_count = len(backup_data.get('results', {}))
                        if task_count > max_tasks:
                            max_tasks = task_count
                            best_backup = backup_file

                    except Exception:
                        continue

            # 执行恢复
            if best_backup and max_tasks > 0:
                try:
                    best_backup.replace(output_file)
                    return True
                except Exception:
                    return False

            return False

    def cleanup_old_backups(self, keep_days: int = 7):
        """
        清理旧的备份文件

        Args:
            keep_days (int): 保留天数
        """
        try:
            cutoff_time = time.time() - (keep_days * 24 * 3600)

            for backup_file in self.output_dir.glob("*_*.json"):
                try:
                    if backup_file.stat().st_mtime < cutoff_time:
                        backup_file.unlink()
                except Exception:
                    continue

        except Exception:
            pass  # 静默处理清理错误

# --- 基础API调用函数 ---
def call_llm_for_clustering(unique_descriptions, prompt_template, file_name, field_key,
                           current_retry_count=0, storage_manager=None, auto_save=True):
    """
    基础LLM API调用函数 - 集成安全存储功能

    Args:
        unique_descriptions: 要处理的描述列表
        prompt_template: 提示模板
        file_name: 输出文件名
        field_key: 字段键名
        current_retry_count: 当前重试次数
        storage_manager: 存储管理器实例
        auto_save: 是否自动保存结果
    """
    if not unique_descriptions:
        return None, "No descriptions provided", {'total_attempts': 0, 'failed_attempts': 0}

    file_basename = os.path.basename(file_name)
    task_key = f"{field_key}_{hash(str(unique_descriptions))}"  # 生成唯一任务标识

    # 检查是否已经处理过 - 使用可靠的缓存机制
    if storage_manager and auto_save:
        if storage_manager.is_task_completed(file_basename, task_key):
            existing_data = storage_manager.load_existing_results(file_basename)
            if task_key in existing_data.get('results', {}):
                existing_result = existing_data['results'][task_key]['data']
                return existing_result, None, {'total_attempts': 0, 'failed_attempts': 0, 'from_cache': True}

    # 准备API统计
    api_stats = {
        'total_attempts': 0,
        'failed_attempts': 0,
        'failure_rate': 0.0,
        'error_types': {},
        'token_usage': None
    }

    # 准备提示
    item_list_str = repr(unique_descriptions)
    final_prompt = prompt_template.replace("{item_list_string}", item_list_str)

    messages = [{"role": "user", "content": final_prompt}]
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": DEFAULT_MODEL,
        "messages": messages,
        "stream": False,
        "temperature": 0.5,
        "max_tokens": 4000
    }

    for attempt in range(MAX_RETRIES):
        api_stats['total_attempts'] += 1
        
        try:
            response = requests.post(API_URL, headers=headers, json=payload, timeout=240)
            response.raise_for_status()
            llm_response = response.json()

            # 保存token使用信息
            if "usage" in llm_response:
                api_stats['token_usage'] = llm_response["usage"]

            if ("choices" in llm_response and len(llm_response["choices"]) > 0 and 
                "message" in llm_response["choices"][0] and "content" in llm_response["choices"][0]["message"]):
                
                content = llm_response["choices"][0]["message"]["content"].strip()

                # 自动保存结果
                if storage_manager and auto_save:
                    result_data = {
                        'field_key': field_key,
                        'input_descriptions': unique_descriptions,
                        'llm_response': content,
                        'retry_count': current_retry_count,
                        'api_stats': api_stats
                    }

                    metadata = {
                        'model_used': DEFAULT_MODEL,
                        'prompt_template': prompt_template[:100] + "..." if len(prompt_template) > 100 else prompt_template
                    }

                    storage_manager.save_result_safely(file_basename, task_key, result_data, metadata)

                return content, None, api_stats
            else:
                api_stats['failed_attempts'] += 1
                error_type = "invalid_response_structure"
                api_stats['error_types'][error_type] = api_stats['error_types'].get(error_type, 0) + 1

        except requests.exceptions.HTTPError as http_err:
            api_stats['failed_attempts'] += 1
            status_code = http_err.response.status_code
            
            if status_code == 429:
                error_type = "rate_limit"
                wait_time = min(60, 2 ** attempt)
                if attempt < MAX_RETRIES - 1:
                    time.sleep(wait_time)
                    continue
            else:
                error_type = f"http_{status_code}"
            
            api_stats['error_types'][error_type] = api_stats['error_types'].get(error_type, 0) + 1

        except requests.exceptions.RequestException as e:
            api_stats['failed_attempts'] += 1
            error_type = type(e).__name__
            api_stats['error_types'][error_type] = api_stats['error_types'].get(error_type, 0) + 1
            
            if attempt < MAX_RETRIES - 1:
                wait_time = min(60, 2 ** attempt)
                time.sleep(wait_time)
                continue

    # 计算失败率
    if api_stats['total_attempts'] > 0:
        api_stats['failure_rate'] = (api_stats['failed_attempts'] / api_stats['total_attempts']) * 100

    return None, f"API调用失败，尝试了{MAX_RETRIES}次", api_stats

# --- 全局实例 ---
api_limiter = TokenBucketRateLimiter(API_RATE_LIMIT)
retry_queue = PriorityRetryQueue()
# 默认存储管理器 - 可以根据需要自定义输出目录
default_storage_manager = ThreadSafeResultStorage("./api_results")

# --- 使用示例 ---
def basic_usage_example():
    """基础使用示例 - 展示企业级可靠性功能"""
    # 创建存储管理器（自动启用可靠性检查）
    storage_manager = ThreadSafeResultStorage("./output")

    # 等待API限流
    api_limiter.wait_if_needed()

    # API调用（自动多重备份和一致性检查）
    descriptions = ["item1", "item2", "item3"]
    prompt = "Analyze: {item_list_string}"

    result, error, stats = call_llm_for_clustering(
        descriptions, prompt, "results.json", "field_key",
        storage_manager=storage_manager, auto_save=True
    )

    if result:
        api_limiter.report_success()

        # 可选：手动触发数据恢复（如果需要）
        # storage_manager.recover_from_backups("results.json")

        # 可选：清理旧备份文件
        # storage_manager.cleanup_old_backups(keep_days=7)

    else:
        api_limiter.report_rate_limit_hit()
        retry_queue.add_task("field_key", descriptions, prompt, "results.json", retry_count=1)

    return result, error, stats
