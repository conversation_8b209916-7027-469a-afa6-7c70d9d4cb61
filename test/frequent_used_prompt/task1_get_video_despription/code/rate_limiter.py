#!/usr/bin/env python3
"""
令牌桶限流器
参考api_scheduling_strategy_base.py的TokenBucketRateLimiter实现
专门为视频分析API调用提供智能限流功能
"""

import time
import random
import threading
from typing import Dict, Optional


class VideoAPIRateLimiter:
    """视频API令牌桶限流器"""
    
    def __init__(self, requests_per_minute: int = 30, bucket_capacity: Optional[int] = None):
        """
        初始化令牌桶限流器
        
        Args:
            requests_per_minute (int): 每分钟允许的请求数
            bucket_capacity (Optional[int]): 令牌桶容量，默认为每分钟请求数的1.5倍
        """
        self.requests_per_minute = requests_per_minute
        self.requests_per_second = requests_per_minute / 60.0
        self.bucket_capacity = bucket_capacity or int(requests_per_minute * 1.5)
        
        self.lock = threading.Lock()
        self.current_tokens = self.bucket_capacity  # 初始令牌数为桶容量
        self.last_refill_time = time.time()  # 上次填充时间
        
        # 动态调整参数
        self.rate_limit_hit_count = 0  # 遇到429的次数
        self.consecutive_failures = 0  # 连续失败次数
        self.additional_wait = 0  # 额外等待时间(秒)
        
        # 统计信息
        self.total_wait_time = 0  # 总等待时间
        self.wait_count = 0  # 等待次数
        self.total_requests = 0  # 总请求数
        self.successful_requests = 0  # 成功请求数
        
        print(f"🪣 初始化视频API限流器: 每分钟 {requests_per_minute} 个请求, 桶容量 {self.bucket_capacity}")
    
    def _refill_tokens(self):
        """重新填充令牌"""
        now = time.time()
        time_passed = now - self.last_refill_time
        new_tokens = time_passed * self.requests_per_second
        
        if new_tokens > 0:
            self.current_tokens = min(self.bucket_capacity, self.current_tokens + new_tokens)
            self.last_refill_time = now
    
    def acquire_token(self, timeout: Optional[float] = None) -> bool:
        """
        获取一个令牌
        
        Args:
            timeout (Optional[float]): 等待令牌的最大时间（秒），如果为None则无限等待
            
        Returns:
            bool: 是否成功获取到令牌
        """
        start_time = time.time()
        
        with self.lock:
            self._refill_tokens()
            
            # 如果令牌不足，计算需要等待的时间
            if self.current_tokens < 1:
                # 计算获得一个令牌需要的时间
                required_time = (1 - self.current_tokens) / self.requests_per_second
                
                # 如果设置了超时且等待时间超过超时时间，则放弃
                if timeout is not None and required_time > timeout:
                    return False
                
                # 应用额外等待时间（作为惩罚/退避机制）
                wait_time = required_time + min(self.additional_wait, 2.0)
                if self.additional_wait > 0:
                    self.additional_wait = max(0, self.additional_wait - 1.0)
                
                # 释放锁，以便其他线程可以在我们等待的同时获取令牌
                self.lock.release()
                try:
                    # 记录等待统计信息
                    self.wait_count += 1
                    self.total_wait_time += wait_time
                    
                    # 添加随机抖动
                    jitter = random.uniform(0, 0.1 * wait_time)
                    actual_wait = wait_time + jitter
                    
                    print(f"⏳ API限流等待 {actual_wait:.2f} 秒...")
                    time.sleep(actual_wait)
                finally:
                    # 重新获取锁
                    self.lock.acquire()
                
                # 等待后重新填充令牌
                self._refill_tokens()
                
                # 再次检查令牌是否足够
                if self.current_tokens < 1:
                    # 超时或其他线程可能获取了令牌
                    return False
            
            # 获取令牌
            self.current_tokens -= 1
            self.total_requests += 1
            return True
    
    def wait_for_token(self):
        """等待直到获取到令牌"""
        while not self.acquire_token():
            # 如果获取令牌失败，尝试再次获取
            time.sleep(0.1)  # 短暂等待避免CPU忙等
    
    def report_rate_limit_hit(self):
        """报告遇到了速率限制(429错误)"""
        with self.lock:
            self.rate_limit_hit_count += 1
            self.consecutive_failures += 1
            
            # 如果连续遇到多次速率限制，增加额外等待时间
            if self.consecutive_failures > 3:
                self.additional_wait += self.consecutive_failures * 0.5
                print(f"⚠️ 检测到频繁的速率限制，增加额外等待时间: {self.additional_wait:.1f}秒")
                
                # 降低每分钟请求速率
                if self.consecutive_failures > 5:
                    old_rate = self.requests_per_minute
                    self.requests_per_minute = max(10, self.requests_per_minute - 5)
                    self.requests_per_second = self.requests_per_minute / 60.0
                    if old_rate != self.requests_per_minute:
                        print(f"⚠️ 降低请求速率至每分钟{self.requests_per_minute}个")
    
    def report_success(self):
        """报告请求成功"""
        with self.lock:
            self.successful_requests += 1
            # 重置连续失败计数
            self.consecutive_failures = 0
            
            # 如果一段时间内成功率很高，可以逐渐恢复请求速率
            if self.rate_limit_hit_count > 0 and self.successful_requests % 10 == 0:
                success_rate = self.successful_requests / self.total_requests
                if success_rate > 0.9:  # 成功率超过90%
                    # 逐渐恢复请求速率
                    original_rate = 30  # 原始速率
                    if self.requests_per_minute < original_rate:
                        self.requests_per_minute = min(original_rate, self.requests_per_minute + 2)
                        self.requests_per_second = self.requests_per_minute / 60.0
                        print(f"ℹ️ 检测到高成功率，恢复请求速率至每分钟{self.requests_per_minute}个")
    
    def report_failure(self, is_rate_limit: bool = False):
        """
        报告请求失败
        
        Args:
            is_rate_limit (bool): 是否为速率限制错误
        """
        if is_rate_limit:
            self.report_rate_limit_hit()
        else:
            with self.lock:
                self.consecutive_failures += 1
    
    def get_current_rate(self) -> float:
        """获取当前请求速率（每分钟）"""
        with self.lock:
            return self.requests_per_minute
    
    def get_statistics(self) -> Dict:
        """获取限流器统计信息"""
        with self.lock:
            stats = {
                "requests_per_minute": self.requests_per_minute,
                "current_tokens": self.current_tokens,
                "bucket_capacity": self.bucket_capacity,
                "rate_limit_hit_count": self.rate_limit_hit_count,
                "consecutive_failures": self.consecutive_failures,
                "additional_wait": self.additional_wait,
                "total_requests": self.total_requests,
                "successful_requests": self.successful_requests,
                "success_rate": (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0,
                "avg_wait_time": self.total_wait_time / max(1, self.wait_count) if self.wait_count > 0 else 0,
                "total_wait_time": self.total_wait_time,
                "wait_count": self.wait_count
            }
            return stats
    
    def print_statistics(self):
        """打印限流器统计信息"""
        stats = self.get_statistics()
        
        print(f"\n{'='*60}")
        print("🪣 API限流器统计信息:")
        print(f"{'='*60}")
        print(f"当前请求速率: {stats['requests_per_minute']} 请求/分钟")
        print(f"当前令牌数: {stats['current_tokens']:.2f}")
        print(f"桶容量: {stats['bucket_capacity']}")
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功请求: {stats['successful_requests']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        print(f"速率限制次数: {stats['rate_limit_hit_count']}")
        print(f"连续失败次数: {stats['consecutive_failures']}")
        print(f"等待次数: {stats['wait_count']}")
        print(f"总等待时间: {stats['total_wait_time']:.2f} 秒")
        print(f"平均等待时间: {stats['avg_wait_time']:.2f} 秒")
        
        if stats['additional_wait'] > 0:
            print(f"额外等待时间: {stats['additional_wait']:.1f} 秒")
    
    def reset_statistics(self):
        """重置统计信息"""
        with self.lock:
            self.rate_limit_hit_count = 0
            self.consecutive_failures = 0
            self.additional_wait = 0
            self.total_wait_time = 0
            self.wait_count = 0
            self.total_requests = 0
            self.successful_requests = 0
            print("🔄 限流器统计信息已重置")
    
    def adjust_rate(self, new_rate: int):
        """
        手动调整请求速率
        
        Args:
            new_rate (int): 新的每分钟请求数
        """
        with self.lock:
            old_rate = self.requests_per_minute
            self.requests_per_minute = max(1, new_rate)
            self.requests_per_second = self.requests_per_minute / 60.0
            print(f"🔧 手动调整请求速率: {old_rate} -> {self.requests_per_minute} 请求/分钟")
    
    def is_healthy(self) -> bool:
        """
        检查限流器是否处于健康状态
        
        Returns:
            bool: 是否健康（成功率高，连续失败少）
        """
        with self.lock:
            if self.total_requests == 0:
                return True
            
            success_rate = self.successful_requests / self.total_requests
            return success_rate > 0.8 and self.consecutive_failures < 5
