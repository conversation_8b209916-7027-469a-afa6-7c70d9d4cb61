#!/usr/bin/env python3
"""
配置文件
包含API密钥、路径配置和其他设置
"""

import os

# Google Gemini API配置 - 支持多个密钥轮询
GOOGLE_API_KEYS = [
    'AIzaSyCmHPmam0i6Ey1slKi5EJAUkpFk3Glh8nU',  # 原有密钥1
    'AIzaSyBmnec3Q3xUXAxdQgTWW1Zb2Hc8X7EAFw0',  # 原有密钥2
    'AIzaSyB-mUyIx5K50GcoIlQLGVwAwfMYukUvXpk',  # 新增密钥1
    'AIzaSyAPGLZKx2QLuJnSyMjS3doJV-houfbkFeE',  # 新增密钥2
    'AIzaSyD4vSOxSTvCoaRaRYDSzVVNfYOGV7GfAMI',  # 新增密钥3
    'AIzaSyBUcGQ3psoRmhbOMAvZRE5UX47g8HwCkIY',  # 来自GitHub爬取
    'AIzaSyCbwrC2E-HU63CjHCiw-JC04aImc-J1HI8',  # 来自GitHub爬取
    'AIzaSyBsZOq8JM1kekHdtASRKwnsv08EdDb73nQ',  # 来自GitHub爬取
    'AIzaSyCyKs56rgTt3fYsOLLSujMHEftHP8GaawQ',  # 来自GitHub爬取
    'AIzaSyBl-87J2-s-ZGCRmzPlV3owJk0byDHAs2s',  # 来自GitHub爬取
    'AIzaSyAEAP5d99WP8iq170T8sLaEJV0Efflkv30',  # 来自GitHub爬取
    'AIzaSyDNL8Xvu00B521s4LQ_ZP7ZSXkcoR9c0Zc',  # 来自GitHub爬取
    'AIzaSyDbaLX5GK2ljlHIm0E4YgPFeGQE9Rkatx8',  # 来自GitHub爬取
    'AIzaSyBW1tJl-oy29FV7dWGD7fUVbKC5pk5OisE',  # 来自GitHub爬取
    'AIzaSyAUgjuvMXd1MkKZodMnZrarYykURJY-fmQ',  # 来自GitHub爬取
    'AIzaSyBgKKVwF4TEuRUv5XeMMkhB9nhN85P_IMU',  # 来自GitHub爬取
    'AIzaSyCoKd7csnqOfMM0e4kRGG-D3jSHlsDtXZM',  # 来自GitHub爬取
    'AIzaSyB4s1LNm2PFeC5jnSAeiGGvTpLllyvtsuk',  # 来自GitHub爬取
    'AIzaSyAtXLC5O9ojla9wZANhjgtu2RjeRfyST_c',  # 来自GitHub爬取
    'AIzaSyCCppYabwSL6VfLOSwv-uOLaqd-JBODUVg',  # 来自GitHub爬取
    'AIzaSyBwAGf5ZKHJNsfrXE4cpP5dO18nLjA-o2U',  # 来自GitHub爬取
]    

# 路径配置
DEFAULT_INPUT_DIR = '/data2/jevon/7_24_video_tag/task1_get_video_despription/video'
DEFAULT_OUTPUT_DIR = '/data2/jevon/7_24_video_tag/task1_get_video_despription/output'
VIDEO_RESIZE_MODULE_PATH = '/data2/jevon/7_24_video_tag/task1_get_video_despription'

# 视频处理配置
MAX_FILE_SIZE_MB = 19  # 最大文件大小限制（MB）
MAX_RETRIES = 3  # API调用最大重试次数
BASE_DELAY = 5  # 基础延迟时间（秒）- 更激进
PROCESSING_DELAY = 0.5  # 处理间隔延迟（秒）- 更激进

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.avi', '.mov', '.mkv']

# Gemini API配置
GEMINI_MODEL = "models/gemini-2.5-pro"
GEMINI_MIME_TYPE = 'video/mp4'

# 输出文件配置
OUTPUT_FILE_SUFFIX = '_video_analysis.json'
TEMP_COMPRESSED_DIR = 'temp_compressed'
VIDEO_DESC_DIR = 'video_descriptions_mercy_v1_original'  # 视频描述存储目录

# 日志配置
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# 进度保存配置
PROGRESS_FILE = 'processing_progress.json'  # 进度保存文件名
CHECKPOINT_INTERVAL = 5  # 每处理多少个视频保存一次进度

# API轮询配置 - 更激进的设置
API_SWITCH_DELAY = 1  # API切换后的等待时间（秒）- 更激进
API_COOLDOWN_TIME = 180  # API密钥冷却时间（秒），3分钟后可重新尝试失败的密钥 - 更激进
MAX_CONSECUTIVE_FAILURES = 2  # 单个API密钥最大连续失败次数 - 更激进

# 错误处理配置
SAVE_ON_ALL_API_FAILED = True  # 当所有API都失效时是否保存当前进度
AUTO_RESUME_ON_RESTART = True  # 重启时是否自动从上次进度继续

# 视频分析提示词 - 增强版，强制JSON格式输出
VIDEO_ANALYSIS_PROMPT = """Analyze this footwear product advertisement videoin the following JSON format:
    {
        "description": "Describe what you see in the video in a simple, direct way (e.g., 'In a forest environment, the camera focuses on a rough, textured shoe surface')"
    }.
    Answer in valid JSON format only.
"""
