#!/usr/bin/env python3
"""
智能重试管理器
参考api_scheduling_strategy_base.py的PriorityRetryQueue实现
专门处理视频分析任务的重试逻辑
"""

import time
import heapq
import threading
from typing import List, Tuple, Optional
from pathlib import Path


class VideoRetryManager:
    """视频处理重试管理器"""
    
    def __init__(self):
        """初始化重试管理器"""
        self.retry_queue = []  # 优先级队列
        self.entry_count = 0  # 用于相同优先级时的FIFO顺序
        self.lock = threading.Lock()  # 线程安全锁
        self.retry_counts = {}  # 追踪每个视频的重试次数 {video_path: retry_count}
        self.failure_reasons = {}  # 记录失败原因 {video_path: [reasons]}
        
        # 重试配置
        self.max_retries = 3
        self.retry_delays = [30, 60, 120]  # 重试延迟时间（秒）
        
        print("🔄 智能重试管理器已初始化")
    
    def add_failed_video(self, video_path: Path, error_reason: str, retry_count: int = 0):
        """
        添加失败的视频到重试队列
        
        Args:
            video_path (Path): 视频文件路径
            error_reason (str): 失败原因
            retry_count (int): 当前重试次数
        """
        with self.lock:
            video_path_str = str(video_path)
            
            # 更新重试次数
            if video_path_str in self.retry_counts:
                self.retry_counts[video_path_str] = max(self.retry_counts[video_path_str], retry_count)
            else:
                self.retry_counts[video_path_str] = retry_count
            
            # 记录失败原因
            if video_path_str not in self.failure_reasons:
                self.failure_reasons[video_path_str] = []
            self.failure_reasons[video_path_str].append(error_reason)
            
            # 检查是否超过最大重试次数
            if self.retry_counts[video_path_str] >= self.max_retries:
                print(f"❌ 视频 {video_path.name} 已达到最大重试次数 ({self.max_retries})，放弃重试")
                return False
            
            # 优先级取反，使重试次数多的任务优先级更高
            priority = -self.retry_counts[video_path_str]
            
            # 添加到优先级队列
            heapq.heappush(
                self.retry_queue, 
                (priority, self.entry_count, video_path_str, error_reason, time.time())
            )
            self.entry_count += 1
            
            print(f"🔄 添加视频到重试队列: {video_path.name} (重试次数: {retry_count + 1}, 原因: {error_reason})")
            return True
    
    def get_next_retry_video(self) -> Optional[Tuple[str, str, int]]:
        """
        获取下一个需要重试的视频
        
        Returns:
            Optional[Tuple[str, str, int]]: (video_path, error_reason, retry_count) 或 None
        """
        with self.lock:
            if not self.retry_queue:
                return None
            
            # 获取优先级最高的任务
            priority, entry_count, video_path_str, error_reason, add_time = heapq.heappop(self.retry_queue)
            retry_count = self.retry_counts.get(video_path_str, 0)
            
            # 检查是否需要延迟
            current_time = time.time()
            if retry_count > 0 and retry_count <= len(self.retry_delays):
                required_delay = self.retry_delays[retry_count - 1]
                elapsed_time = current_time - add_time
                
                if elapsed_time < required_delay:
                    # 重新放回队列，等待更长时间
                    heapq.heappush(
                        self.retry_queue,
                        (priority, entry_count, video_path_str, error_reason, add_time)
                    )
                    remaining_delay = required_delay - elapsed_time
                    print(f"⏳ 视频 {Path(video_path_str).name} 需要等待 {remaining_delay:.1f} 秒后重试")
                    return None
            
            return video_path_str, error_reason, retry_count
    
    def get_all_retry_videos(self) -> List[Tuple[str, str, int]]:
        """
        获取所有待重试的视频（按优先级排序）
        
        Returns:
            List[Tuple[str, str, int]]: [(video_path, error_reason, retry_count), ...]
        """
        with self.lock:
            retry_videos = []
            temp_queue = []
            
            # 提取所有任务
            while self.retry_queue:
                item = heapq.heappop(self.retry_queue)
                priority, entry_count, video_path_str, error_reason, add_time = item
                retry_count = self.retry_counts.get(video_path_str, 0)
                
                retry_videos.append((video_path_str, error_reason, retry_count))
                temp_queue.append(item)
            
            # 重新放回队列
            for item in temp_queue:
                heapq.heappush(self.retry_queue, item)
            
            return retry_videos
    
    def clear_retry_queue(self):
        """清空重试队列"""
        with self.lock:
            self.retry_queue.clear()
            self.retry_counts.clear()
            self.failure_reasons.clear()
            self.entry_count = 0
            print("🗑️ 重试队列已清空")
    
    def is_empty(self) -> bool:
        """检查重试队列是否为空"""
        with self.lock:
            return len(self.retry_queue) == 0
    
    def size(self) -> int:
        """获取重试队列大小"""
        with self.lock:
            return len(self.retry_queue)
    
    def get_retry_statistics(self) -> dict:
        """获取重试统计信息"""
        with self.lock:
            stats = {
                'total_retry_videos': len(self.retry_counts),
                'pending_retries': len(self.retry_queue),
                'retry_distribution': {},
                'common_failure_reasons': {}
            }
            
            # 重试次数分布
            for video_path, retry_count in self.retry_counts.items():
                if retry_count not in stats['retry_distribution']:
                    stats['retry_distribution'][retry_count] = 0
                stats['retry_distribution'][retry_count] += 1
            
            # 常见失败原因
            all_reasons = []
            for reasons in self.failure_reasons.values():
                all_reasons.extend(reasons)
            
            reason_counts = {}
            for reason in all_reasons:
                reason_counts[reason] = reason_counts.get(reason, 0) + 1
            
            # 取前5个最常见的原因
            stats['common_failure_reasons'] = dict(
                sorted(reason_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            )
            
            return stats
    
    def print_statistics(self):
        """打印重试统计信息"""
        stats = self.get_retry_statistics()
        
        print(f"\n{'='*60}")
        print("🔄 重试管理器统计信息:")
        print(f"{'='*60}")
        print(f"总重试视频数: {stats['total_retry_videos']}")
        print(f"待重试视频数: {stats['pending_retries']}")
        
        if stats['retry_distribution']:
            print("\n重试次数分布:")
            for retry_count, count in sorted(stats['retry_distribution'].items()):
                print(f"  {retry_count} 次重试: {count} 个视频")
        
        if stats['common_failure_reasons']:
            print("\n常见失败原因:")
            for reason, count in stats['common_failure_reasons'].items():
                print(f"  {reason}: {count} 次")
    
    def should_retry(self, video_path: Path, error_reason: str) -> bool:
        """
        判断是否应该重试某个视频
        
        Args:
            video_path (Path): 视频文件路径
            error_reason (str): 错误原因
            
        Returns:
            bool: 是否应该重试
        """
        video_path_str = str(video_path)
        current_retry_count = self.retry_counts.get(video_path_str, 0)
        
        # 检查重试次数限制
        if current_retry_count >= self.max_retries:
            return False
        
        # 某些错误类型不值得重试
        non_retryable_errors = [
            "文件不存在",
            "文件格式不支持",
            "文件损坏",
            "权限不足"
        ]
        
        for non_retryable in non_retryable_errors:
            if non_retryable in error_reason:
                print(f"⚠️ 错误类型不适合重试: {error_reason}")
                return False
        
        return True
    
    def get_failure_history(self, video_path: Path) -> List[str]:
        """
        获取视频的失败历史
        
        Args:
            video_path (Path): 视频文件路径
            
        Returns:
            List[str]: 失败原因列表
        """
        video_path_str = str(video_path)
        return self.failure_reasons.get(video_path_str, [])
    
    def mark_video_success(self, video_path: Path):
        """
        标记视频处理成功，从重试记录中移除
        
        Args:
            video_path (Path): 视频文件路径
        """
        with self.lock:
            video_path_str = str(video_path)
            
            # 从记录中移除
            if video_path_str in self.retry_counts:
                del self.retry_counts[video_path_str]
            
            if video_path_str in self.failure_reasons:
                del self.failure_reasons[video_path_str]
            
            # 从队列中移除（重建队列）
            new_queue = []
            while self.retry_queue:
                item = heapq.heappop(self.retry_queue)
                if item[2] != video_path_str:  # video_path_str在索引2
                    new_queue.append(item)
            
            # 重建队列
            self.retry_queue = new_queue
            heapq.heapify(self.retry_queue)
            
            print(f"✅ 视频 {video_path.name} 处理成功，已从重试记录中移除")
