#!/usr/bin/env python3
"""
多线程视频处理器
基于原有的EnhancedVideoProcessorV2，实现3线程并发处理
"""

import os
import json
import time
import sys
import random
import threading
from pathlib import Path
from typing import List, Optional, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
from google import genai
from google.genai import types
from google.genai.errors import ClientError

# 导入配置和自定义模块
from config import *
from progress_manager import ThreadSafeProgressManager
from enhanced_api_manager import EnhancedAPIKeyManager
from response_validator import SimpleResponseValidator
from retry_manager import VideoRetryManager
from rate_limiter import VideoAPIRateLimiter
from performance_monitor import PerformanceMonitor

# 添加video_resize模块的路径
sys.path.append(VIDEO_RESIZE_MODULE_PATH)
from video_resize import resize_video


class MultithreadedVideoProcessor:
    """多线程视频处理器"""
    
    def __init__(self, input_dir=None, output_dir=None, max_file_size_mb=None, 
                 resume=True, num_threads=3):
        """
        初始化多线程视频处理器
        
        Args:
            input_dir (str): 输入视频目录
            output_dir (str): 输出结果目录
            max_file_size_mb (int): 最大文件大小限制（MB）
            resume (bool): 是否从上次进度继续
            num_threads (int): 工作线程数量
        """
        self.input_dir = Path(input_dir or DEFAULT_INPUT_DIR)
        self.output_dir = Path(output_dir or DEFAULT_OUTPUT_DIR)
        self.max_file_size_mb = max_file_size_mb or MAX_FILE_SIZE_MB
        self.max_file_size_bytes = self.max_file_size_mb * 1024 * 1024
        self.resume = resume
        self.num_threads = num_threads
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.progress_manager = ThreadSafeProgressManager(self.output_dir)
        self.api_manager = EnhancedAPIKeyManager()
        self.response_validator = SimpleResponseValidator(max_retries=3)
        self.retry_manager = VideoRetryManager()
        self.rate_limiter = VideoAPIRateLimiter(requests_per_minute=60)  # 更激进的限流
        self.performance_monitor = PerformanceMonitor(monitor_interval=15.0)  # 15秒监控间隔
        
        # 线程管理
        self.task_queue = Queue()
        self.result_queue = Queue()
        self.shutdown_event = threading.Event()
        self.thread_pool = None
        
        # 统计信息
        self.session_stats = {
            'start_time': time.time(),
            'videos_processed_this_session': 0,
            'videos_failed_this_session': 0,
            'videos_skipped_this_session': 0,
            'api_calls_made': 0,
            'api_calls_successful': 0,
            'total_processing_time': 0,
            'thread_stats': {}
        }
        
        # 线程锁
        self.stats_lock = threading.Lock()
        
        print(f"🚀 初始化多线程视频处理器")
        print(f"   输入目录: {self.input_dir}")
        print(f"   输出目录: {self.output_dir}")
        print(f"   文件大小限制: {self.max_file_size_mb}MB")
        print(f"   工作线程数: {self.num_threads}")
        print(f"   断点续传: {'启用' if self.resume else '禁用'}")
    
    def get_video_files(self) -> List[Path]:
        """获取所有待处理的视频文件"""
        video_files = []
        for root, dirs, files in os.walk(self.input_dir):
            for file in files:
                if any(file.lower().endswith(ext) for ext in SUPPORTED_VIDEO_FORMATS):
                    video_files.append(Path(root) / file)
        
        # 按路径排序以确保处理顺序一致
        video_files.sort()
        return video_files
    
    def check_and_compress_video(self, video_path: Path) -> Optional[Path]:
        """
        检查视频大小，如果超过限制则压缩
        
        Args:
            video_path (Path): 视频文件路径
            
        Returns:
            Optional[Path]: 处理后的视频文件路径，失败返回None
        """
        try:
            file_size = video_path.stat().st_size
            thread_id = threading.current_thread().ident
            print(f"📹 [线程{thread_id}] 视频文件大小: {file_size / (1024*1024):.2f} MB")
            
            if file_size > self.max_file_size_bytes:
                print(f"📦 [线程{thread_id}] 文件超过{self.max_file_size_mb}MB限制，开始压缩...")
                
                # 创建临时压缩目录
                temp_dir = self.output_dir / TEMP_COMPRESSED_DIR
                temp_dir.mkdir(exist_ok=True)
                
                # 压缩视频
                compressed_path = resize_video(str(video_path), str(temp_dir))
                
                if compressed_path:
                    print(f"✅ [线程{thread_id}] 视频压缩成功: {compressed_path}")
                    return Path(compressed_path)
                else:
                    print(f"❌ [线程{thread_id}] 视频压缩失败: {video_path}")
                    return None
            
            return video_path
            
        except Exception as e:
            thread_id = threading.current_thread().ident
            print(f"❌ [线程{thread_id}] 检查视频文件失败: {e}")
            return None
    
    def process_video_with_gemini(self, video_path: Path, max_retries=None, base_delay=None) -> Optional[str]:
        """
        使用Gemini API处理视频，支持多线程API调度
        
        Args:
            video_path (Path): 视频文件路径
            max_retries (int): 最大重试次数
            base_delay (int): 基础延迟时间
            
        Returns:
            Optional[str]: API返回的分析结果，失败返回None
        """
        max_retries = max_retries or MAX_RETRIES
        base_delay = base_delay or BASE_DELAY
        thread_id = threading.current_thread().ident
        
        for attempt in range(max_retries):
            # 获取当前线程的客户端（在共享模式下会尽力分配）
            client = self.api_manager.get_client_for_thread()
            if client is None:
                # 检查是否还有可用的API密钥
                if not self.api_manager.has_available_keys():
                    print(f"❌ [线程{thread_id}] 所有API密钥都已失效，无法继续处理")
                    return None
                else:
                    print(f"⚠️ [线程{thread_id}] 暂时无法获取API客户端，等待后重试...")
                    time.sleep(2)  # 等待2秒后重试
                    continue
            
            try:
                print(f"🔄 [线程{thread_id}] 尝试第 {attempt + 1} 次处理视频: {video_path.name}")

                # 等待限流器令牌
                self.rate_limiter.wait_for_token()

                with self.stats_lock:
                    self.session_stats['api_calls_made'] += 1

                # 更新性能监控
                self.performance_monitor.update_stats(api_calls=1)

                # 检查文件是否存在
                if not video_path.exists():
                    print(f"❌ [线程{thread_id}] 视频文件不存在: {video_path}")
                    return None
                
                # 读取视频文件
                with open(video_path, 'rb') as f:
                    video_bytes = f.read()
                
                # 发送请求
                response = client.models.generate_content(
                    model=GEMINI_MODEL,
                    contents=types.Content(
                        parts=[
                            types.Part(
                                inline_data=types.Blob(data=video_bytes, mime_type=GEMINI_MIME_TYPE)
                            ),
                            types.Part(text=VIDEO_ANALYSIS_PROMPT)
                        ]
                    )
                )
                
                # 标记请求成功
                self.api_manager.mark_request_success()
                self.rate_limiter.report_success()
                
                with self.stats_lock:
                    self.session_stats['api_calls_successful'] += 1

                # 更新性能监控
                self.performance_monitor.update_stats(successful_api_calls=1)

                return response.text
                
            except ClientError as e:
                # 处理API错误
                status_code = getattr(e, 'status_code', None)
                error_message = str(e)
                
                # 检测API额度限制相关的错误信息
                quota_keywords = ['quota', 'limit', 'exceeded', 'rate limit', '403', '429', '503']
                is_quota_error = any(keyword.lower() in error_message.lower() for keyword in quota_keywords)
                
                if is_quota_error or status_code in [403, 429, 503]:
                    print(f"🚫 [线程{thread_id}] API额度受限: {error_message}")
                    self.rate_limiter.report_rate_limit_hit()
                else:
                    print(f"🚫 [线程{thread_id}] API错误: {error_message}")
                    self.rate_limiter.report_failure(is_rate_limit=False)

                # 标记请求失败并尝试重新分配密钥
                if self.api_manager.mark_request_failed(e):
                    print(f"🔄 [线程{thread_id}] 已重新分配API密钥，继续尝试...")
                    continue
                else:
                    print(f"❌ [线程{thread_id}] 无法重新分配API密钥")
                    return None
                    
            except Exception as e:
                print(f"❌ [线程{thread_id}] 其他错误: {str(e)}")
                
                # 对于非API错误，等待后重试
                if attempt < max_retries - 1:
                    retry_delay = base_delay * (2 ** attempt) + random.uniform(0.1, 0.5)
                    print(f"⏳ [线程{thread_id}] 等待 {retry_delay:.2f} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                else:
                    return None
        
        return None

    def extract_json_from_response(self, response_text: str, video_path: str) -> Optional[Dict]:
        """
        从API响应中提取JSON内容 - 使用简化版响应验证器

        Args:
            response_text (str): API响应文本
            video_path (str): 视频路径

        Returns:
            Optional[Dict]: 解析后的JSON数据，失败返回None
        """
        return self.response_validator.validate_and_fix_response(response_text, video_path)

    def save_result(self, video_path: Path, analysis_result: Dict, raw_response: str):
        """
        保存分析结果到对应位置（线程安全）

        Args:
            video_path (Path): 原始视频文件路径
            analysis_result (Dict): 解析后的分析结果
            raw_response (str): 原始API响应
        """
        try:
            thread_id = threading.current_thread().ident

            # 获取相对于输入目录的路径结构
            relative_path = video_path.relative_to(self.input_dir)

            # 构建输出文件路径
            dataset_name = relative_path.parts[0]  # 数据集名称
            scene_name = video_path.stem  # 场景名称

            # 1. 保存视频描述结果到新的文件夹
            video_desc_dir = self.output_dir / VIDEO_DESC_DIR
            video_desc_dir.mkdir(exist_ok=True)

            desc_result_data = {
                'video_info': {
                    'dataset': dataset_name,
                    'scene': scene_name,
                    'original_path': str(video_path),
                    'file_size_mb': video_path.stat().st_size / (1024 * 1024),
                    'processed_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'processed_by_thread': thread_id
                },
                'video_description': analysis_result,
                'raw_response': raw_response
            }

            desc_output_file = video_desc_dir / f"{dataset_name}_video_descriptions.json"

            # 使用文件锁确保线程安全的文件写入
            lock_file = desc_output_file.with_suffix('.lock')

            # 简单的文件锁机制
            max_wait = 30  # 最多等待30秒
            wait_time = 0
            while lock_file.exists() and wait_time < max_wait:
                time.sleep(0.1)
                wait_time += 0.1

            try:
                # 创建锁文件
                lock_file.touch()

                # 读取或创建视频描述文件
                existing_desc_data = {}
                if desc_output_file.exists():
                    try:
                        with open(desc_output_file, 'r', encoding='utf-8') as f:
                            existing_desc_data = json.load(f)
                    except Exception as e:
                        print(f"⚠️ [线程{thread_id}] 读取现有视频描述文件失败: {e}")
                        existing_desc_data = {}

                if 'scenes' not in existing_desc_data:
                    existing_desc_data['scenes'] = {}

                existing_desc_data['scenes'][scene_name] = desc_result_data

                # 原子写入
                temp_file = desc_output_file.with_suffix('.tmp')
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_desc_data, f, ensure_ascii=False, indent=2)

                temp_file.replace(desc_output_file)
                print(f"✅ [线程{thread_id}] 视频描述已保存到: {desc_output_file}")

            finally:
                # 删除锁文件
                if lock_file.exists():
                    lock_file.unlink()

        except Exception as e:
            thread_id = threading.current_thread().ident
            print(f"❌ [线程{thread_id}] 保存结果失败: {e}")

    def worker_thread(self, thread_id: int):
        """
        工作线程函数

        Args:
            thread_id (int): 线程ID
        """
        print(f"🔧 工作线程 {thread_id} 启动")

        # 初始化线程统计
        with self.stats_lock:
            self.session_stats['thread_stats'][thread_id] = {
                'processed': 0,
                'failed': 0,
                'start_time': time.time(),
                'last_activity': time.time()
            }

        while not self.shutdown_event.is_set():
            try:
                # 从队列获取任务，超时1秒
                video_path = self.task_queue.get(timeout=1)

                if video_path is None:  # 毒丸，退出信号
                    break

                # 更新线程活动时间
                with self.stats_lock:
                    self.session_stats['thread_stats'][thread_id]['last_activity'] = time.time()

                # 处理视频
                success = self.process_single_video(video_path, thread_id)

                # 更新线程统计
                with self.stats_lock:
                    if success:
                        self.session_stats['thread_stats'][thread_id]['processed'] += 1
                    else:
                        self.session_stats['thread_stats'][thread_id]['failed'] += 1

                # 标记任务完成
                self.task_queue.task_done()

            except Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                print(f"❌ 工作线程 {thread_id} 发生异常: {e}")
                # 标记任务完成（如果有的话）
                try:
                    self.task_queue.task_done()
                except:
                    pass

        print(f"🔧 工作线程 {thread_id} 退出")

    def process_single_video(self, video_path: Path, thread_id: int) -> bool:
        """
        处理单个视频文件（多线程版本）

        Args:
            video_path (Path): 视频文件路径
            thread_id (int): 处理线程ID

        Returns:
            bool: 处理是否成功
        """
        video_path_str = str(video_path)
        start_time = time.time()

        print(f"\n{'='*80}")
        print(f"🎬 [线程{thread_id}] 开始处理视频: {video_path.name}")
        print(f"📁 [线程{thread_id}] 数据集: {video_path.parent.name}")
        print(f"{'='*80}")

        try:
            # 检查是否已经处理过
            if self.progress_manager.is_video_processed(video_path_str):
                print(f"⏭️ [线程{thread_id}] 视频已处理，跳过: {video_path.name}")
                self.progress_manager.mark_video_skipped(video_path_str)
                with self.stats_lock:
                    self.session_stats['videos_skipped_this_session'] += 1
                return True

            # 检查并压缩视频
            processed_video_path = self.check_and_compress_video(video_path)
            if processed_video_path is None:
                print(f"❌ [线程{thread_id}] 视频预处理失败: {video_path}")
                self.progress_manager.mark_video_failed(video_path_str, thread_id)
                with self.stats_lock:
                    self.session_stats['videos_failed_this_session'] += 1
                return False

            # 使用Gemini API分析视频
            raw_response = self.process_video_with_gemini(processed_video_path)
            if raw_response is None:
                error_reason = "API调用失败"
                print(f"❌ [线程{thread_id}] {error_reason}: {video_path}")

                # 检查是否应该重试
                if self.retry_manager.should_retry(video_path, error_reason):
                    self.retry_manager.add_failed_video(video_path, error_reason)
                else:
                    self.progress_manager.mark_video_failed(video_path_str, thread_id)

                with self.stats_lock:
                    self.session_stats['videos_failed_this_session'] += 1
                return False

            # 解析JSON结果 - 使用简化版解析器
            analysis_result = self.extract_json_from_response(raw_response, video_path_str)
            if analysis_result is None:
                # 检查是否应该重试JSON解析
                if self.response_validator.should_retry(video_path_str):
                    print(f"⚠️ [线程{thread_id}] JSON解析失败，将重试: {video_path}")
                    # 重新放入队列进行重试
                    self.task_queue.put(video_path)
                    return False
                else:
                    print(f"❌ [线程{thread_id}] JSON解析失败次数超限，跳过: {video_path}")
                    self.progress_manager.mark_video_failed(video_path_str, thread_id)
                    with self.stats_lock:
                        self.session_stats['videos_failed_this_session'] += 1
                    return False

            # 保存结果
            self.save_result(video_path, analysis_result, raw_response)

            # 清理临时压缩文件
            if processed_video_path != video_path:
                try:
                    processed_video_path.unlink()
                    print(f"🗑️ [线程{thread_id}] 已清理临时文件: {processed_video_path.name}")
                except Exception as e:
                    print(f"⚠️ [线程{thread_id}] 清理临时文件失败: {e}")

            # 标记为已处理
            self.progress_manager.mark_video_processed(video_path_str, thread_id)
            with self.stats_lock:
                self.session_stats['videos_processed_this_session'] += 1

            # 更新性能监控
            self.performance_monitor.update_stats(videos_processed=1)

            # 从重试管理器中移除（如果存在）
            self.retry_manager.mark_video_success(video_path)

            # 计算处理时间
            processing_time = time.time() - start_time
            with self.stats_lock:
                self.session_stats['total_processing_time'] += processing_time

            print(f"✅ [线程{thread_id}] 视频处理完成: {video_path.name} (耗时: {processing_time:.2f}秒)")
            return True

        except Exception as e:
            print(f"❌ [线程{thread_id}] 处理视频时发生异常: {e}")
            self.progress_manager.mark_video_failed(video_path_str, thread_id)
            with self.stats_lock:
                self.session_stats['videos_failed_this_session'] += 1
            return False

    def process_all_videos(self):
        """
        处理所有视频文件，支持多线程并发处理
        """
        print("🔍 开始扫描视频文件...")
        all_video_files = self.get_video_files()

        if not all_video_files:
            print("❌ 未找到任何视频文件")
            return

        print(f"📊 找到 {len(all_video_files)} 个视频文件")

        # 设置总视频数量
        self.progress_manager.set_total_videos(len(all_video_files))

        # 获取剩余未处理的视频
        all_video_paths = [str(video) for video in all_video_files]
        remaining_videos = self.progress_manager.get_remaining_videos(all_video_paths)

        if not remaining_videos:
            print("✅ 所有视频都已处理完成")
            self.print_final_statistics()
            return

        print(f"📋 剩余待处理视频: {len(remaining_videos)} 个")

        # 如果是断点续传，显示进度信息
        if self.resume and len(remaining_videos) < len(all_video_files):
            print("🔄 检测到断点续传模式")
            self.progress_manager.print_statistics()

        # 将视频任务添加到队列
        for video_path_str in remaining_videos:
            video_path = Path(video_path_str)
            self.task_queue.put(video_path)

        # 设置活跃线程数
        self.progress_manager.set_active_threads(self.num_threads)

        # 启动性能监控
        self.performance_monitor.start_monitoring()

        # 启动工作线程
        threads = []
        for i in range(self.num_threads):
            thread = threading.Thread(target=self.worker_thread, args=(i+1,))
            thread.daemon = True
            thread.start()
            threads.append(thread)
            print(f"🚀 启动工作线程 {i+1}")

        try:
            # 等待所有任务完成
            print(f"⏳ 等待 {len(remaining_videos)} 个视频处理完成...")
            self.task_queue.join()

            print("✅ 所有视频处理任务已完成")

        except KeyboardInterrupt:
            print("\n⚠️ 用户中断处理")
            self.shutdown_event.set()
        finally:
            # 发送退出信号给所有线程
            for _ in range(self.num_threads):
                self.task_queue.put(None)  # 毒丸

            # 等待所有线程退出
            for thread in threads:
                thread.join(timeout=5)

            # 停止性能监控
            self.performance_monitor.stop_monitoring()

            # 清理死亡线程资源
            self.api_manager.cleanup_dead_threads()

            # 最终保存进度
            print("💾 保存最终进度...")
            self.progress_manager.save_progress(force=True)

            # 打印最终统计信息
            self.print_final_statistics()

    def print_final_statistics(self):
        """打印最终统计信息"""
        print(f"\n{'='*80}")
        print("🎯 最终处理统计 (多线程版)")
        print(f"{'='*80}")

        # 进度统计
        self.progress_manager.print_statistics()

        # 会话统计
        session_time = time.time() - self.session_stats['start_time']
        print(f"\n本次会话统计:")
        print(f"  会话时长: {session_time:.2f} 秒")
        print(f"  本次处理: {self.session_stats['videos_processed_this_session']} 个")
        print(f"  本次失败: {self.session_stats['videos_failed_this_session']} 个")
        print(f"  本次跳过: {self.session_stats['videos_skipped_this_session']} 个")
        print(f"  API调用: {self.session_stats['api_calls_made']} 次")
        print(f"  API成功: {self.session_stats['api_calls_successful']} 次")

        if self.session_stats['api_calls_made'] > 0:
            api_success_rate = (self.session_stats['api_calls_successful'] / self.session_stats['api_calls_made']) * 100
            print(f"  API成功率: {api_success_rate:.1f}%")

        if self.session_stats['videos_processed_this_session'] > 0:
            avg_time = self.session_stats['total_processing_time'] / self.session_stats['videos_processed_this_session']
            print(f"  平均处理时间: {avg_time:.2f} 秒/视频")

        # 线程统计
        print(f"\n线程处理统计:")
        with self.stats_lock:
            for thread_id, stats in self.session_stats['thread_stats'].items():
                thread_time = time.time() - stats['start_time']
                print(f"  线程 {thread_id}: 成功 {stats['processed']}, 失败 {stats['failed']}, 运行时间 {thread_time:.2f}秒")

        # API密钥统计
        self.api_manager.print_statistics()

        # 重试管理器统计
        self.retry_manager.print_statistics()

        # API限流器统计
        self.rate_limiter.print_statistics()

        # JSON解析统计
        self.response_validator.print_statistics()

        # 性能监控报告
        self.performance_monitor.print_performance_report()
