#!/usr/bin/env python3
"""
增强版API密钥管理器 - 多线程版本
支持智能轮询、冷却时间、失败统计等功能，并提供线程安全保障
"""

import time
import random
import threading
from typing import Dict, List, Optional, Set, Tuple
from google import genai
from google.genai.errors import ClientError
from config import (
    GOOGLE_API_KEYS,
    API_SWITCH_DELAY,
    API_COOLDOWN_TIME,
    MAX_CONSECUTIVE_FAILURES
)


class EnhancedAPIKeyManager:
    """增强版API密钥管理器 - 多线程安全版本"""

    def __init__(self, api_keys: List[str] = None):
        """
        初始化API密钥管理器

        Args:
            api_keys (List[str]): API密钥列表，默认使用配置文件中的密钥
        """
        self.api_keys = api_keys or GOOGLE_API_KEYS
        self.current_key_index = 0

        # 线程安全锁
        self.lock = threading.RLock()  # 使用可重入锁

        # 为每个线程维护独立的客户端
        self.thread_clients = {}  # {thread_id: client}
        self.thread_key_assignments = {}  # {thread_id: key_index}

        # 密钥状态跟踪
        self.key_stats = {}
        self.failed_keys = set()
        self.last_failure_time = {}  # 记录每个密钥最后失败的时间
        self.consecutive_failures = {}  # 记录每个密钥连续失败次数

        # 密钥使用计数器（用于负载均衡）
        self.key_usage_count = {}

        # 初始化密钥统计
        for i, key in enumerate(self.api_keys):
            self.key_stats[key] = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'last_used': None,
                'last_success': None,
                'last_failure': None,
                'consecutive_failures': 0
            }
            self.consecutive_failures[key] = 0
            self.key_usage_count[key] = 0

        print(f"🔑 初始化多线程API密钥管理器，共 {len(self.api_keys)} 个密钥")
    
    def get_client_for_thread(self) -> Optional[genai.Client]:
        """
        为当前线程获取专用的API客户端

        Returns:
            Optional[genai.Client]: API客户端，如果没有可用的则返回None
        """
        thread_id = threading.current_thread().ident

        with self.lock:
            # 检查当前线程是否已有客户端
            if thread_id in self.thread_clients:
                return self.thread_clients[thread_id]

            # 为新线程分配密钥和创建客户端
            available_key_index = self._assign_key_to_thread(thread_id)
            if available_key_index is not None:
                try:
                    api_key = self.api_keys[available_key_index]
                    client = genai.Client(api_key=api_key)
                    self.thread_clients[thread_id] = client
                    self.thread_key_assignments[thread_id] = available_key_index
                    # 不再独占分配，允许多线程共享
                    # self.key_stats[api_key]['assigned_thread'] = thread_id
                    print(f"🔄 线程 {thread_id} 使用API密钥: {api_key[:20]}... (可共享)")
                    return client
                except Exception as e:
                    print(f"❌ 为线程 {thread_id} 创建API客户端失败: {e}")
                    self._mark_key_failed(self.api_keys[available_key_index], str(e))
                    return None
            else:
                print(f"❌ 没有可用的API密钥分配给线程 {thread_id}")
                return None

    def _assign_key_to_thread(self, thread_id: int) -> Optional[int]:
        """
        为线程分配可用的API密钥索引

        Args:
            thread_id (int): 线程ID

        Returns:
            Optional[int]: 分配的密钥索引，如果没有可用的则返回None
        """
        current_time = time.time()

        # 首先检查是否有密钥已经过了冷却期
        for key in list(self.failed_keys):
            if key in self.last_failure_time:
                time_since_failure = current_time - self.last_failure_time[key]
                if time_since_failure >= API_COOLDOWN_TIME:
                    self.failed_keys.discard(key)
                    self.consecutive_failures[key] = 0
                    print(f"🔄 API密钥冷却完成，重新启用: {key[:20]}...")

        # 寻找可用的密钥（允许多线程共享，优先选择使用次数最少的）
        available_keys = []
        for i, key in enumerate(self.api_keys):
            if (key not in self.failed_keys and
                self.consecutive_failures.get(key, 0) < MAX_CONSECUTIVE_FAILURES):
                # 移除assigned_thread限制，允许多线程共享API密钥
                available_keys.append((i, key, self.key_usage_count[key]))

        if available_keys:
            # 按使用次数排序，选择使用最少的密钥
            available_keys.sort(key=lambda x: x[2])
            selected_index, selected_key, _ = available_keys[0]
            self.key_usage_count[selected_key] += 1
            print(f"🔄 为线程 {thread_id} 分配API密钥: {selected_key[:20]}... (共享模式)")
            return selected_index

        return None
    
    def _get_next_available_key(self) -> Optional[str]:
        """
        获取下一个可用的API密钥
        
        Returns:
            Optional[str]: 可用的API密钥，如果没有则返回None
        """
        current_time = time.time()
        
        # 首先检查是否有密钥已经过了冷却期
        cooled_down_keys = []
        for key in self.failed_keys.copy():
            if key in self.last_failure_time:
                time_since_failure = current_time - self.last_failure_time[key]
                if time_since_failure >= API_COOLDOWN_TIME:
                    cooled_down_keys.append(key)
                    self.failed_keys.discard(key)
                    self.consecutive_failures[key] = 0
                    print(f"🔄 API密钥冷却完成，重新启用: {key[:20]}...")
        
        # 寻找可用的密钥
        attempts = 0
        while attempts < len(self.api_keys):
            current_key = self.api_keys[self.current_key_index]
            
            # 检查密钥是否可用
            if (current_key not in self.failed_keys and 
                self.consecutive_failures.get(current_key, 0) < MAX_CONSECUTIVE_FAILURES):
                return current_key
            
            # 移动到下一个密钥
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            attempts += 1
        
        # 如果所有密钥都不可用，检查是否有冷却完成的密钥
        if cooled_down_keys:
            # 选择最近成功过的密钥
            best_key = None
            best_success_time = 0
            
            for key in cooled_down_keys:
                last_success = self.key_stats[key].get('last_success')
                if last_success and last_success > best_success_time:
                    best_success_time = last_success
                    best_key = key
            
            if best_key:
                # 更新当前密钥索引
                self.current_key_index = self.api_keys.index(best_key)
                return best_key
            else:
                # 随机选择一个冷却完成的密钥
                selected_key = random.choice(cooled_down_keys)
                self.current_key_index = self.api_keys.index(selected_key)
                return selected_key
        
        return None
    
    def get_client(self) -> Optional[genai.Client]:
        """
        获取当前线程的API客户端（兼容旧接口）

        Returns:
            Optional[genai.Client]: API客户端，如果没有可用的则返回None
        """
        return self.get_client_for_thread()
    
    def _mark_key_failed(self, api_key: str, error_msg: str = ""):
        """
        标记API密钥失败（线程安全）

        Args:
            api_key (str): 失败的API密钥
            error_msg (str): 错误信息
        """
        with self.lock:
            current_time = time.time()

            # 更新统计信息
            if api_key in self.key_stats:
                self.key_stats[api_key]['failed_requests'] += 1
                self.key_stats[api_key]['last_failure'] = current_time
                self.key_stats[api_key]['last_used'] = current_time

            # 更新失败计数
            self.consecutive_failures[api_key] = self.consecutive_failures.get(api_key, 0) + 1
            self.last_failure_time[api_key] = current_time

            # 如果连续失败次数超过阈值，标记为失败并释放线程分配
            if self.consecutive_failures[api_key] >= MAX_CONSECUTIVE_FAILURES:
                self.failed_keys.add(api_key)
                self._release_key_from_thread(api_key)
                print(f"❌ API密钥达到最大失败次数，暂时禁用: {api_key[:20]}... (错误: {error_msg[:100]})")
            else:
                print(f"⚠️ API密钥失败 ({self.consecutive_failures[api_key]}/{MAX_CONSECUTIVE_FAILURES}): {api_key[:20]}...")

    def _release_key_from_thread(self, api_key: str):
        """
        从线程中释放API密钥分配（在共享模式下，只是标记密钥失效）

        Args:
            api_key (str): 要释放的API密钥
        """
        # 在共享模式下，不需要清理特定线程，只需要标记密钥失效
        # 所有使用该密钥的线程都会在下次请求时重新分配
        print(f"🔄 API密钥 {api_key[:20]}... 已标记为失效，所有使用该密钥的线程将重新分配")
    
    def mark_request_success(self, api_key: str = None):
        """
        标记API请求成功（线程安全）

        Args:
            api_key (str): 成功的API密钥，如果为None则使用当前线程的密钥
        """
        if api_key is None:
            api_key = self.get_current_key_for_thread()

        if api_key and api_key in self.key_stats:
            with self.lock:
                current_time = time.time()
                self.key_stats[api_key]['total_requests'] += 1
                self.key_stats[api_key]['successful_requests'] += 1
                self.key_stats[api_key]['last_success'] = current_time
                self.key_stats[api_key]['last_used'] = current_time

                # 重置连续失败计数
                self.consecutive_failures[api_key] = 0

                # 从失败集合中移除（如果存在）
                self.failed_keys.discard(api_key)

    def get_current_key_for_thread(self) -> Optional[str]:
        """
        获取当前线程使用的API密钥

        Returns:
            Optional[str]: 当前线程的API密钥
        """
        thread_id = threading.current_thread().ident
        with self.lock:
            if thread_id in self.thread_key_assignments:
                key_index = self.thread_key_assignments[thread_id]
                if 0 <= key_index < len(self.api_keys):
                    return self.api_keys[key_index]
        return None
    
    def mark_request_failed(self, error: Exception, api_key: str = None) -> bool:
        """
        标记API请求失败并尝试为当前线程重新分配密钥

        Args:
            error (Exception): 错误对象
            api_key (str): 失败的API密钥，如果为None则使用当前线程的密钥

        Returns:
            bool: 是否成功为当前线程重新分配密钥
        """
        if api_key is None:
            api_key = self.get_current_key_for_thread()

        if api_key:
            error_msg = str(error)

            # 检测是否为配额限制错误
            quota_keywords = ['quota', 'limit', 'exceeded', 'rate limit', '403', '429', '503']
            is_quota_error = any(keyword.lower() in error_msg.lower() for keyword in quota_keywords)

            # 如果是配额错误，获取状态码进行更详细的判断
            status_code = getattr(error, 'status_code', None)
            if status_code in [403, 429, 503] or is_quota_error:
                print(f"⚠️ 检测到API配额限制错误，密钥 {api_key[:20]}... 可能已达到使用限制")
                # 对于配额错误，立即将密钥标记为失败状态
                with self.lock:
                    self.consecutive_failures[api_key] = MAX_CONSECUTIVE_FAILURES

            self._mark_key_failed(api_key, error_msg)

            # 更新总请求计数
            with self.lock:
                if api_key in self.key_stats:
                    self.key_stats[api_key]['total_requests'] += 1

        # 尝试为当前线程重新分配密钥
        return self.reassign_key_for_current_thread()
    
    def reassign_key_for_current_thread(self) -> bool:
        """
        为当前线程重新分配API密钥

        Returns:
            bool: 是否成功重新分配
        """
        thread_id = threading.current_thread().ident

        with self.lock:
            # 清理当前线程的分配（在共享模式下只清理线程本地数据）
            if thread_id in self.thread_clients:
                del self.thread_clients[thread_id]
                if thread_id in self.thread_key_assignments:
                    del self.thread_key_assignments[thread_id]

            # 尝试分配新的密钥
            new_key_index = self._assign_key_to_thread(thread_id)
            if new_key_index is not None:
                try:
                    api_key = self.api_keys[new_key_index]
                    client = genai.Client(api_key=api_key)
                    self.thread_clients[thread_id] = client
                    self.thread_key_assignments[thread_id] = new_key_index
                    # 在共享模式下不设置独占分配

                    # 添加切换延迟
                    if API_SWITCH_DELAY > 0:
                        print(f"⏳ 线程 {thread_id} API切换延迟 {API_SWITCH_DELAY} 秒...")
                        time.sleep(API_SWITCH_DELAY)

                    print(f"🔄 线程 {thread_id} 切换到新API密钥: {api_key[:20]}...")
                    return True
                except Exception as e:
                    print(f"❌ 为线程 {thread_id} 重新分配API密钥失败: {e}")
                    self._mark_key_failed(self.api_keys[new_key_index], str(e))
                    return False
            else:
                print(f"❌ 没有可用的API密钥重新分配给线程 {thread_id}")
                return False
    
    def get_current_key(self) -> Optional[str]:
        """
        获取当前线程使用的API密钥（兼容旧接口）

        Returns:
            Optional[str]: 当前线程的API密钥
        """
        return self.get_current_key_for_thread()
    
    def has_available_keys(self) -> bool:
        """
        检查是否还有可用的API密钥

        Returns:
            bool: 是否有可用密钥
        """
        with self.lock:
            # 检查是否有未分配的可用密钥
            current_time = time.time()

            # 首先检查冷却完成的密钥
            for key in list(self.failed_keys):
                if key in self.last_failure_time:
                    time_since_failure = current_time - self.last_failure_time[key]
                    if time_since_failure >= API_COOLDOWN_TIME:
                        self.failed_keys.discard(key)
                        self.consecutive_failures[key] = 0

            # 检查是否有可用密钥
            available_count = 0
            for key in self.api_keys:
                if (key not in self.failed_keys and
                    self.consecutive_failures.get(key, 0) < MAX_CONSECUTIVE_FAILURES):
                    available_count += 1

            if available_count == 0:
                print("⚠️ 当前没有可用的API密钥")
                self._log_key_status()
                return False

            return True
    
    def _log_key_status(self):
        """记录所有密钥的状态信息"""
        print("📊 API密钥状态总览:")
        for i, key in enumerate(self.api_keys):
            key_display = f"{key[:15]}..."
            status = "❌失效" if key in self.failed_keys else "✅可用"
            consecutive = self.consecutive_failures.get(key, 0)
            last_failure = self.last_failure_time.get(key)
            
            if last_failure:
                time_since_failure = int(time.time() - last_failure)
                cooldown_remaining = max(0, API_COOLDOWN_TIME - time_since_failure)
                if cooldown_remaining > 0:
                    status += f" (冷却中: {cooldown_remaining}秒)"
            
            print(f"  {i+1}. {key_display} {status} (连续失败: {consecutive})")
        
        # 检查是否所有密钥都因为配额问题失效
        all_quota_exhausted = all(
            self.consecutive_failures.get(key, 0) >= MAX_CONSECUTIVE_FAILURES 
            for key in self.api_keys
        )
        if all_quota_exhausted:
            print("🚫 所有API密钥似乎都已达到配额限制")
            print("💡 请等待配额重置或检查API密钥是否有效")
    
    def reset_all_keys(self):
        """重置所有密钥状态（用于新的处理周期）"""
        with self.lock:
            self.failed_keys.clear()
            self.last_failure_time.clear()
            for key in self.consecutive_failures:
                self.consecutive_failures[key] = 0

            # 清理所有线程分配
            self.thread_clients.clear()
            self.thread_key_assignments.clear()

            # 重置密钥使用计数
            for key in self.api_keys:
                self.key_usage_count[key] = 0

            self.current_key_index = 0
            print("🔄 已重置所有API密钥状态和线程分配")

    def cleanup_dead_threads(self):
        """清理已死亡线程的资源"""
        import threading
        active_thread_ids = {t.ident for t in threading.enumerate()}

        with self.lock:
            dead_threads = []
            for thread_id in list(self.thread_clients.keys()):
                if thread_id not in active_thread_ids:
                    dead_threads.append(thread_id)

            for thread_id in dead_threads:
                if thread_id in self.thread_key_assignments:
                    key_index = self.thread_key_assignments[thread_id]
                    if key_index < len(self.api_keys):
                        api_key = self.api_keys[key_index]
                        self.key_stats[api_key]['assigned_thread'] = None

                del self.thread_clients[thread_id]
                if thread_id in self.thread_key_assignments:
                    del self.thread_key_assignments[thread_id]

                print(f"🧹 清理死亡线程 {thread_id} 的资源")

    def get_thread_statistics(self) -> Dict:
        """获取线程分配统计信息"""
        with self.lock:
            active_threads = len(self.thread_clients)

            return {
                'active_threads': active_threads,
                'available_keys': len(self.api_keys) - len(self.failed_keys),
                'failed_keys': len(self.failed_keys),
                'thread_assignments': dict(self.thread_key_assignments),
                'shared_mode': True  # 标识为共享模式
            }
    
    def get_key_statistics(self) -> Dict:
        """
        获取API密钥使用统计

        Returns:
            Dict: 统计信息
        """
        with self.lock:
            total_requests = sum(stats['total_requests'] for stats in self.key_stats.values())
            total_successful = sum(stats['successful_requests'] for stats in self.key_stats.values())
            total_failed = sum(stats['failed_requests'] for stats in self.key_stats.values())

            thread_stats = self.get_thread_statistics()

            return {
                'total_keys': len(self.api_keys),
                'available_keys': len(self.api_keys) - len(self.failed_keys),
                'failed_keys': len(self.failed_keys),

                'active_threads': thread_stats['active_threads'],
                'total_requests': total_requests,
                'successful_requests': total_successful,
                'failed_requests': total_failed,
                'success_rate': (total_successful / total_requests * 100) if total_requests > 0 else 0,
                'current_key': self.get_current_key()
            }
    
    def print_statistics(self):
        """打印API密钥使用统计"""
        stats = self.get_key_statistics()
        print(f"\n{'='*60}")
        print("API密钥使用统计 (多线程版):")
        print(f"{'='*60}")
        print(f"总密钥数: {stats['total_keys']}")
        print(f"可用密钥: {stats['available_keys']}")
        print(f"失效密钥: {stats['failed_keys']}")
        print(f"活跃线程: {stats['active_threads']} (共享模式)")
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功请求: {stats['successful_requests']}")
        print(f"失败请求: {stats['failed_requests']}")
        print(f"成功率: {stats['success_rate']:.1f}%")

        # 显示每个密钥的详细状态
        print(f"\n密钥详细状态:")
        with self.lock:
            for key in self.api_keys:
                key_stats = self.key_stats[key]
                status = "❌失效" if key in self.failed_keys else "✅可用"
                usage_count = self.key_usage_count.get(key, 0)

                print(f"  {key[:20]}... {status} [使用次数:{usage_count}] "
                      f"(成功:{key_stats['successful_requests']} "
                      f"失败:{key_stats['failed_requests']} "
                      f"连续失败:{self.consecutive_failures.get(key, 0)})")
