#!/usr/bin/env python3
"""
视频压缩脚本
使用ffmpeg将视频压缩到720p分辨率
"""

import os
import subprocess
import sys

def resize_video(input_path, output_dir=None):
    """
    压缩视频到720p分辨率
    
    Args:
        input_path (str): 输入视频文件路径
        output_dir (str, optional): 输出目录，如果为None则使用输入文件所在目录
    
    Returns:
        str: 输出文件路径，如果失败返回None
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_path):
        print(f"错误: 输入文件不存在: {input_path}")
        return None
    
    # 获取输入文件信息
    input_dir = os.path.dirname(input_path)
    input_filename = os.path.basename(input_path)
    name_without_ext = os.path.splitext(input_filename)[0]
    
    # 确定输出目录
    if output_dir is None:
        output_dir = input_dir
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成输出文件名
    output_filename = f"{name_without_ext}_ys.mp4"
    output_path = os.path.join(output_dir, output_filename)
    
    # 构建ffmpeg命令
    cmd = [
        'ffmpeg',
        '-i', input_path,
        '-vf', 'scale=-2:720',
        '-c:v', 'libx264',
        '-crf', '30',
        '-preset', 'slow',
        '-tune', 'film',
        '-c:a', 'copy',
        '-y',  # 覆盖输出文件
        output_path
    ]
    
    print(f"开始压缩视频: {input_filename}")
    print(f"输出文件: {output_filename}")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 执行ffmpeg命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        
        print(f"✅ 视频压缩完成: {output_path}")
        
        # 显示文件大小对比
        input_size = os.path.getsize(input_path) / (1024 * 1024)  # MB
        output_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        compression_ratio = (1 - output_size / input_size) * 100
        
        print(f"原始文件大小: {input_size:.1f} MB")
        print(f"压缩后文件大小: {output_size:.1f} MB")
        print(f"压缩率: {compression_ratio:.1f}%")
        
        return output_path
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 视频压缩失败: {e}")
        print(f"错误输出: {e.stderr}")
        return None
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        return None

if __name__ == "__main__":
    resize_video("/data2/mercy/project/test/videos/Ada-Tiktok-@jorgee/scene_005.mp4")