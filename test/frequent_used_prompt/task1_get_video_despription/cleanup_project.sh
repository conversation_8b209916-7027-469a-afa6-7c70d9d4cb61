#!/bin/bash
# 项目精简清理脚本
# 生成时间: $(date)

PROJECT_DIR="/data2/jevon/7_22_video_matrix_breif_prompt_PREFER_data_v2"
BACKUP_DIR="/data2/jevon/7_22_video_matrix_breif_prompt_PREFER_data_v2_backup/$(date +%Y%m%d_%H%M%S)"

echo "🗂️ 视频处理项目精简清理"
echo "=========================="

cd "$PROJECT_DIR/code"

echo "📋 要删除的冗余文件:"
echo ""

# 冗余的处理器文件 (保留最新的v2版本)
echo "🔧 处理器文件 (保留enhanced_video_processor_v2.py):"
echo "  - enhanced_video_processor.py (旧版本)"
echo "  - optimized_video_processor.py (优化版本，已被v2取代)" 
echo "  - video_processor.py (基础版本)"
echo "  - demo_processor.py (演示版本)"

# 冗余的运行脚本 (保留最新的v2版本)
echo "🚀 运行脚本 (保留run_enhanced_analysis_v2.py):"
echo "  - run_enhanced_analysis.py (旧版本)"
echo "  - run_optimized_analysis.py (优化版本)"
echo "  - run_video_analysis.py (基础版本)"

# 冗余的测试文件
echo "🧪 测试文件 (保留核心测试):"
echo "  - test_optimization.py (优化测试，功能已合并)"
echo "  - test_system.py (基础系统测试)"
echo "  - test_enhanced_system.py (增强测试，已被basic替代)"

# 工具脚本 (保留有用的)
echo "🛠️ 工具脚本:"
echo "  - batch_process.py (批处理，功能已集成到主程序)"
echo "  - process_failed.py (失败处理，功能已集成)"
echo "  - monitor.py (监控，功能已集成)"

# 冗余文档
echo "📚 文档文件 (保留README.md和当前配置):"
echo "  - COMPLETION_REPORT.md (完成报告，历史文档)" 
echo "  - OPTIMIZATION_REPORT.md (优化报告，历史文档)"
echo "  - USAGE.md (使用说明，内容已过时)"
echo "  - README_ENHANCED.md (增强说明，与README重复)"

# Shell脚本
echo "🔧 Shell脚本 (保留最新版本):"
echo "  - start_processing.sh (启动脚本，已有更新版本)"

echo ""
echo "📊 统计信息:"
echo "  当前总文件数: $(find . -type f | wc -l)"
echo "  计划删除文件数: 14"
echo "  精简后文件数: $(($(find . -type f | wc -l) - 14))"

read -p "确认执行清理? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "开始清理..."
    
    # 删除冗余处理器
    rm -f enhanced_video_processor.py
    rm -f optimized_video_processor.py  
    rm -f video_processor.py
    rm -f demo_processor.py
    
    # 删除冗余运行脚本
    rm -f run_enhanced_analysis.py
    rm -f run_optimized_analysis.py
    rm -f run_video_analysis.py
    
    # 删除冗余测试文件
    rm -f test_optimization.py
    rm -f test_system.py
    rm -f test_enhanced_system.py
    
    # 删除冗余工具脚本
    rm -f batch_process.py
    rm -f process_failed.py
    rm -f monitor.py
    
    # 删除冗余文档
    rm -f COMPLETION_REPORT.md
    rm -f OPTIMIZATION_REPORT.md
    rm -f USAGE.md
    rm -f README_ENHANCED.md
    
    # 删除旧的shell脚本
    rm -f start_processing.sh
    
    echo "✅ 清理完成!"
    echo "📊 精简后文件数: $(find . -type f | wc -l)"
    
    # 清理__pycache__
    echo "🗑️ 清理缓存文件..."
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true
    
    echo "✅ 项目精简完成!"
else
    echo "❌ 取消清理操作"
fi