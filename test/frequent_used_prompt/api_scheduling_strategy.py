# -*- coding: utf-8 -*-
import os
import json
import requests
import ast
import time
import glob
from datetime import datetime, timezone
from collections import Counter, defaultdict
import pandas as pd
import concurrent.futures
from tqdm import tqdm
import sys
import copy
import re
import threading
import math
import random
import heapq
import numpy as np

# --- LLM API 配置 ---
API_URL = "https://api.siliconflow.cn/v1/chat/completions"
API_TOKEN = os.environ.get("SILICONFLOW_API_TOKEN", "YOUR_FALLBACK_API_TOKEN_HERE")
DEFAULT_MODEL = "Pro/deepseek-ai/DeepSeek-V3"

# --- 安全和警告 ---
if API_TOKEN == "YOUR_FALLBACK_API_TOKEN_HERE":
     print("⚠️ WARNING: Using a placeholder API token. Please set the SILICONFLOW_API_TOKEN environment variable or replace the fallback token in the script.")
     # sys.exit("Error: API Token not configured.") # Uncomment to enforce token setting
elif API_TOKEN == "sk-ntycrbdyqnamkcsnlfmfwtiqdetcddvlvsldpkdibujhikjo":
     print("⚠️ 警告：使用硬编码的API令牌。考虑使用环境变量 SILICONFLOW_API_TOKEN。")

# --- 并发和重试配置 ---
MAX_RETRIES = 10
MAX_RETRY_DELAY = 60
BASE_RETRY_DELAY = 1

# --- 并发和速率限制配置 ---
MAX_FILE_WORKERS = 3
MAX_FIELD_WORKERS = 3
API_RATE_LIMIT = 30
MIN_WORKERS = 2  # 最小并发数

# --- 输入/输出 配置 ---
INPUT_DIRECTORY = "/data2/jevon/4_07_ailisting_test/data526_1"
OUTPUT_DIRECTORY = "/data2/jevon/4_07_ailisting_test/clustering_output_multi_526_1"

# --- 用于存储所有处理结果和状态的全局变量 ---
all_file_results = []
style_processing_stats = {}  # 记录每个style的处理统计
all_token_usages = []  # 所有API调用的token使用情况
style_token_stats = {}  # 每个style的token统计信息
style_data_counts = {}  # 每个style的数据数量

# --- LLM 聚类 Prompts (One for each field type) ---

# Base prompt structure - 修改基础提示使其生成更具体的描述
base_prompt_cluster = '''
You are an expert data analyst specializing in customer feedback analysis.
Your task is to analyze the following list of {data_type_description} (extracted from customer reviews' "Pros" section) and group them into meaningful clusters based on their semantic similarity.
For each cluster, provide a specific and descriptive label that captures the key product qualities mentioned. Use descriptive adjectives and be specific about product attributes.

**Input:** A Python list of strings, where each string is a {data_type_singular}.

**Output:** Return a Python dictionary string where:
- Keys are specific, descriptive labels with adjectives that describe actual product qualities (e.g., {example_labels}).
- Values are Python lists containing the *original* items from the input list that belong to that cluster.

**Example Input List:**
{example_input_list}

**Example Output Dictionary String:**
```python
{example_output_dict}
```

**Instructions:**
1. Analyze the semantic meaning of each item in the input list.
2. Group items with similar meanings together.
3. Create SPECIFIC, DESCRIPTIVE labels that include adjectives and concrete qualities (e.g., "excellent waterproofing" rather than just "waterproofing" or "water resistance").
4. Avoid vague category-style labels like "Comfort ; Fit" - instead use descriptive phrases like "extremely comfortable fit" or "perfect sizing".
5. Make your labels directly describe product qualities similar to: "good waterproofing, excellent cushioning, durable, strong support, high quality, lightweight"
6. Ensure every input item is assigned to exactly one cluster.
7. Format the output strictly as a Python dictionary string as shown in the example. Do not add any explanations outside the dictionary string.
8. If the input list is empty or contains no meaningful items, return an empty dictionary string: `{}`.

**Now, please cluster the following list of {data_type_description}:**

{item_list_string}
'''

# --- Specific Prompt Details ---

# 1. For Summary Description - 修改示例标签和输出
prompt_cluster_summary = base_prompt_cluster.replace(
    "{data_type_description}", "product advantages (summaries)"
).replace(
    "{data_type_singular}", "advantage summary"
).replace(
    "{example_labels}", '"Extremely comfortable fit", "High-quality durable material", "Stylish attractive design", "Excellent value for money", "Outstanding long-lasting durability"'
).replace(
    "{example_input_list}", "['Comfortable fit', 'Looks great', 'Good price', 'Very comfy', 'Stylish design', 'Feels durable', 'Excellent value', 'Well-made', 'Perfect fit']"
).replace(
    "{example_output_dict}", '''{
    "Extremely comfortable perfect fit": ["Comfortable fit", "Very comfy", "Perfect fit"],
    "Stylish attractive appearance": ["Looks great", "Stylish design"],
    "Great value for money": ["Good price", "Excellent value"],
    "Durable high-quality construction": ["Feels durable", "Well-made"]
}'''
)

# 2. For Usage Scenario Description - 修改示例标签和输出
prompt_cluster_scenario = base_prompt_cluster.replace(
    "{data_type_description}", "usage scenarios"
).replace(
    "{data_type_singular}", "usage scenario"
).replace(
    "{example_labels}", '"Perfect for daily casual wear", "Ideal for work environment", "Excellent for outdoor activities", "Great for special occasions", "Wonderful gift option"'
).replace(
    "{example_input_list}", "['Walking around town', 'Using them for work', 'Good for hiking', 'Wore to a party', 'Bought as a gift', 'Everyday use', 'Outdoor work']"
).replace(
    "{example_output_dict}", '''{
    "Perfect for everyday casual use": ["Walking around town", "Everyday use"],
    "Ideal for work environments": ["Using them for work", "Outdoor work"],
    "Excellent for outdoor activities": ["Good for hiking"],
    "Great for social events": ["Wore to a party"],
    "Wonderful gift choice": ["Bought as a gift"]
}'''
)

# 3. For Functionality Description - 修改示例标签和输出
prompt_cluster_functionality = base_prompt_cluster.replace(
    "{data_type_description}", "product functionalities or features praised"
).replace(
    "{data_type_singular}", "functionality/feature"
).replace(
    "{example_labels}", '"Superior grip and traction", "Excellent waterproofing", "Outstanding comfort and cushioning", "Highly durable materials", "Lightweight comfortable design", "Great breathability"'
).replace(
    "{example_input_list}", "['Soles have good grip', 'Kept my feet dry in rain', 'Very comfortable cushioning', 'Material feels sturdy', 'Look stylish', 'Good ankle support', 'Lightweight feel', 'Waterproof']"
).replace(
    "{example_output_dict}", '''{
    "Excellent grip and traction": ["Soles have good grip"],
    "Superior waterproofing": ["Kept my feet dry in rain", "Waterproof"],
    "Outstanding comfort features": ["Very comfortable cushioning", "Good ankle support", "Lightweight feel"],
    "Highly durable construction": ["Material feels sturdy"],
    "Stylish attractive design": ["Look stylish"]
}'''
)

# Mapping field names to their prompts
PROMPT_MAP = {
    "Summary": prompt_cluster_summary,
    "Usage Scenario": prompt_cluster_scenario,
    "Functionality": prompt_cluster_functionality,
}

# --- 性能指标收集器 ---
class PerformanceMetrics:
    def __init__(self, window_size=100):
        """
        性能指标收集器

        :param window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.lock = threading.Lock()

        # 响应时间指标
        self.response_times = []  # 响应时间列表
        self.request_timestamps = []  # 请求时间戳

        # 成功率指标
        self.success_count = 0
        self.total_count = 0
        self.recent_results = []  # 最近的成功/失败结果

        # 错误统计
        self.error_counts = defaultdict(int)
        self.recent_errors = []  # 最近的错误记录

        # 吞吐量指标
        self.completed_requests = []  # 完成请求的时间戳

        # 性能趋势
        self.performance_history = []  # 性能历史记录

    def record_request_start(self):
        """记录请求开始"""
        return time.time()

    def record_request_complete(self, start_time, success=True, error_type=None):
        """记录请求完成"""
        with self.lock:
            end_time = time.time()
            response_time = end_time - start_time

            # 记录响应时间
            self.response_times.append(response_time)
            self.request_timestamps.append(end_time)

            # 维护滑动窗口
            if len(self.response_times) > self.window_size:
                self.response_times.pop(0)
                self.request_timestamps.pop(0)

            # 记录成功率
            self.total_count += 1
            if success:
                self.success_count += 1
                self.recent_results.append(True)
            else:
                self.recent_results.append(False)
                if error_type:
                    self.error_counts[error_type] += 1
                    self.recent_errors.append((end_time, error_type))

            # 维护成功率滑动窗口
            if len(self.recent_results) > self.window_size:
                removed = self.recent_results.pop(0)
                if removed and self.success_count > 0:
                    self.success_count -= 1
                if self.total_count > 0:
                    self.total_count -= 1

            # 记录完成时间
            self.completed_requests.append(end_time)
            if len(self.completed_requests) > self.window_size:
                self.completed_requests.pop(0)

            # 清理过期错误记录
            current_time = time.time()
            self.recent_errors = [(t, e) for t, e in self.recent_errors
                                if current_time - t < 300]  # 保留5分钟内的错误

    def get_avg_response_time(self):
        """获取平均响应时间"""
        with self.lock:
            return np.mean(self.response_times) if self.response_times else 0

    def get_p95_response_time(self):
        """获取P95响应时间"""
        with self.lock:
            return np.percentile(self.response_times, 95) if self.response_times else 0

    def get_success_rate(self):
        """获取成功率"""
        with self.lock:
            return (self.success_count / self.total_count) if self.total_count > 0 else 1.0

    def get_current_qps(self):
        """获取当前QPS"""
        with self.lock:
            if len(self.completed_requests) < 2:
                return 0

            current_time = time.time()
            # 计算最近60秒的QPS
            recent_requests = [t for t in self.completed_requests
                             if current_time - t <= 60]
            return len(recent_requests) / 60.0

    def get_error_rate_by_type(self, error_type, time_window=300):
        """获取特定错误类型的错误率"""
        with self.lock:
            current_time = time.time()
            recent_errors_of_type = [t for t, e in self.recent_errors
                                   if e == error_type and current_time - t <= time_window]
            recent_total = len([t for t in self.request_timestamps
                              if current_time - t <= time_window])
            return len(recent_errors_of_type) / recent_total if recent_total > 0 else 0

    def get_performance_summary(self):
        """获取性能摘要"""
        with self.lock:
            # 直接计算，避免重复获取锁
            avg_response_time = np.mean(self.response_times) if self.response_times else 0
            p95_response_time = np.percentile(self.response_times, 95) if self.response_times else 0
            success_rate = (self.success_count / self.total_count) if self.total_count > 0 else 1.0

            # 计算当前QPS
            current_time = time.time()
            recent_requests = [t for t in self.request_timestamps
                             if current_time - t <= 60]
            current_qps = len(recent_requests) / 60.0

            return {
                'avg_response_time': avg_response_time,
                'p95_response_time': p95_response_time,
                'success_rate': success_rate,
                'current_qps': current_qps,
                'total_requests': len(self.response_times),
                'error_counts': dict(self.error_counts),
                'recent_error_rate': len(self.recent_errors) / len(self.request_timestamps) if self.request_timestamps else 0
            }

# --- 自适应并发控制器 ---
class AdaptiveConcurrencyController:
    def __init__(self, initial_concurrency=3, min_concurrency=1, max_concurrency=10):
        """
        自适应并发控制器

        :param initial_concurrency: 初始并发数
        :param min_concurrency: 最小并发数
        :param max_concurrency: 最大并发数
        """
        self.current_concurrency = initial_concurrency
        self.min_concurrency = min_concurrency
        self.max_concurrency = max_concurrency

        self.lock = threading.Lock()

        # AIMD参数
        self.additive_increase = 1  # 加性增加步长
        self.multiplicative_decrease = 0.7  # 乘性减少因子

        # 性能阈值
        self.target_response_time = 2.0  # 目标响应时间(秒)
        self.max_response_time = 5.0  # 最大可接受响应时间
        self.min_success_rate = 0.8  # 最小成功率
        self.target_success_rate = 0.95  # 目标成功率

        # 调整历史
        self.adjustment_history = []
        self.last_adjustment_time = time.time()
        self.adjustment_cooldown = 30  # 调整冷却时间(秒)

        # 性能指标
        self.metrics = PerformanceMetrics()

        print(f"🎛️ 初始化自适应并发控制器: 初始并发数 {initial_concurrency}, 范围 [{min_concurrency}, {max_concurrency}]")

    def should_adjust(self):
        """判断是否应该调整并发数"""
        current_time = time.time()
        return (current_time - self.last_adjustment_time) >= self.adjustment_cooldown

    def calculate_adjustment(self):
        """计算并发数调整"""
        with self.lock:
            if not self.should_adjust():
                return 0

            performance = self.metrics.get_performance_summary()

            # 如果没有足够的数据，不调整
            if performance['total_requests'] < 5:
                return 0

            avg_response_time = performance['avg_response_time']
            p95_response_time = performance['p95_response_time']
            success_rate = performance['success_rate']
            current_qps = performance['current_qps']

            # 决策逻辑
            adjustment = 0
            reason = ""

            # 1. 检查成功率
            if success_rate < self.min_success_rate:
                # 成功率过低，大幅降低并发
                adjustment = -max(1, int(self.current_concurrency * (1 - self.multiplicative_decrease)))
                reason = f"成功率过低 ({success_rate:.2f} < {self.min_success_rate})"

            # 2. 检查响应时间
            elif p95_response_time > self.max_response_time:
                # P95响应时间过长，降低并发
                adjustment = -max(1, int(self.current_concurrency * 0.3))
                reason = f"P95响应时间过长 ({p95_response_time:.2f}s > {self.max_response_time}s)"

            elif avg_response_time > self.target_response_time:
                # 平均响应时间超过目标，小幅降低并发
                adjustment = -1
                reason = f"平均响应时间超标 ({avg_response_time:.2f}s > {self.target_response_time}s)"

            # 3. 检查是否可以增加并发
            elif (success_rate >= self.target_success_rate and
                  avg_response_time <= self.target_response_time * 0.8 and
                  p95_response_time <= self.max_response_time * 0.7):
                # 性能良好，可以增加并发
                adjustment = self.additive_increase
                reason = f"性能良好，可增加并发 (成功率: {success_rate:.2f}, 响应时间: {avg_response_time:.2f}s)"

            # 4. 检查错误率
            rate_limit_error_rate = self.metrics.get_error_rate_by_type('rate_limit')
            if rate_limit_error_rate > 0.1:  # 10%以上的限流错误
                adjustment = min(adjustment, -max(1, int(self.current_concurrency * 0.5)))
                reason += f" + 限流错误率高 ({rate_limit_error_rate:.2f})"

            return adjustment, reason

    def adjust_concurrency(self):
        """调整并发数"""
        adjustment, reason = self.calculate_adjustment()

        if adjustment == 0:
            return self.current_concurrency

        with self.lock:
            old_concurrency = self.current_concurrency
            self.current_concurrency = max(
                self.min_concurrency,
                min(self.max_concurrency, self.current_concurrency + adjustment)
            )

            if self.current_concurrency != old_concurrency:
                self.last_adjustment_time = time.time()
                self.adjustment_history.append({
                    'timestamp': time.time(),
                    'old_concurrency': old_concurrency,
                    'new_concurrency': self.current_concurrency,
                    'adjustment': adjustment,
                    'reason': reason
                })

                # 保持历史记录不超过100条
                if len(self.adjustment_history) > 100:
                    self.adjustment_history.pop(0)

                print(f"🎛️ 并发数调整: {old_concurrency} -> {self.current_concurrency} ({reason})")

            return self.current_concurrency

    def get_current_concurrency(self):
        """获取当前并发数"""
        with self.lock:
            return self.current_concurrency

    def record_request(self, start_time, success=True, error_type=None):
        """记录请求结果"""
        self.metrics.record_request_complete(start_time, success, error_type)

    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            performance = self.metrics.get_performance_summary()
            return {
                'current_concurrency': self.current_concurrency,
                'min_concurrency': self.min_concurrency,
                'max_concurrency': self.max_concurrency,
                'adjustment_count': len(self.adjustment_history),
                'last_adjustment_time': self.last_adjustment_time,
                'performance': performance,
                'recent_adjustments': self.adjustment_history[-5:] if self.adjustment_history else []
            }

# --- 令牌桶限流器实现 ---
class EnhancedTokenBucketRateLimiter:
    def __init__(self, tokens_per_minute=60, bucket_capacity=None, adaptive=True):
        """
        增强的令牌桶限流器

        :param tokens_per_minute: 每分钟生成的令牌数（即允许的请求数）
        :param bucket_capacity: 令牌桶容量，默认为每分钟令牌数的1.5倍
        :param adaptive: 是否启用自适应调整
        """
        self.initial_tokens_per_minute = tokens_per_minute
        self.tokens_per_minute = tokens_per_minute
        self.tokens_per_second = tokens_per_minute / 60.0
        self.bucket_capacity = bucket_capacity or int(tokens_per_minute * 1.5)
        self.adaptive = adaptive

        self.lock = threading.Lock()
        self.current_tokens = self.bucket_capacity  # 初始令牌数为桶容量
        self.last_refill_time = time.time()  # 上次填充时间

        # 动态调整参数
        self.min_tokens_per_minute = max(10, tokens_per_minute * 0.2)  # 最小速率
        self.max_tokens_per_minute = tokens_per_minute * 3  # 最大速率
        self.rate_adjustment_factor = 0.1  # 速率调整因子

        # 错误统计
        self.rate_limit_hit_count = 0  # 计数器：记录遇到429的次数
        self.consecutive_failures = 0  # 计数器：记录连续失败的次数
        self.consecutive_successes = 0  # 计数器：记录连续成功的次数
        self.additional_wait = 0  # 额外等待时间(秒)

        # 统计信息
        self.total_wait_time = 0  # 总等待时间
        self.wait_count = 0  # 等待次数

        # 性能指标收集器
        self.metrics = PerformanceMetrics()

        # 自适应并发控制器
        self.concurrency_controller = AdaptiveConcurrencyController(
            initial_concurrency=MAX_FIELD_WORKERS,
            min_concurrency=MIN_WORKERS,
            max_concurrency=MAX_FIELD_WORKERS * 2
        )

        # 速率调整历史
        self.rate_adjustment_history = []
        self.last_rate_adjustment_time = time.time()
        self.rate_adjustment_cooldown = 60  # 速率调整冷却时间(秒)

        print(f"🪣 初始化增强令牌桶限流器: 每分钟 {tokens_per_minute} 个令牌, 桶容量 {self.bucket_capacity}, 自适应: {adaptive}")

    def _should_adjust_rate(self):
        """判断是否应该调整速率"""
        current_time = time.time()
        return (current_time - self.last_rate_adjustment_time) >= self.rate_adjustment_cooldown

    def _calculate_optimal_rate(self):
        """计算最优速率"""
        if not self.adaptive or not self._should_adjust_rate():
            return self.tokens_per_minute

        performance = self.metrics.get_performance_summary()

        # 如果没有足够的数据，不调整
        if performance['total_requests'] < 10:
            return self.tokens_per_minute

        success_rate = performance['success_rate']
        avg_response_time = performance['avg_response_time']
        current_qps = performance['current_qps']
        rate_limit_error_rate = self.metrics.get_error_rate_by_type('rate_limit')

        # 计算目标速率
        target_rate = self.tokens_per_minute
        adjustment_reason = ""

        # 1. 基于成功率调整
        if success_rate < 0.8:
            # 成功率低，降低速率
            target_rate *= 0.8
            adjustment_reason += f"成功率低({success_rate:.2f}) "
        elif success_rate > 0.95 and rate_limit_error_rate < 0.05:
            # 成功率高且限流错误少，可以提高速率
            target_rate *= 1.1
            adjustment_reason += f"成功率高({success_rate:.2f}) "

        # 2. 基于响应时间调整
        if avg_response_time > 3.0:
            # 响应时间长，降低速率
            target_rate *= 0.9
            adjustment_reason += f"响应时间长({avg_response_time:.2f}s) "
        elif avg_response_time < 1.0:
            # 响应时间短，可以提高速率
            target_rate *= 1.05
            adjustment_reason += f"响应时间短({avg_response_time:.2f}s) "

        # 3. 基于限流错误率调整
        if rate_limit_error_rate > 0.1:
            # 限流错误率高，大幅降低速率
            target_rate *= 0.7
            adjustment_reason += f"限流错误率高({rate_limit_error_rate:.2f}) "

        # 4. 基于实际QPS调整
        theoretical_max_qps = self.tokens_per_minute / 60.0
        if current_qps > theoretical_max_qps * 0.8:
            # 实际QPS接近理论最大值，可以尝试提高
            target_rate *= 1.02
            adjustment_reason += f"QPS接近上限({current_qps:.2f}/{theoretical_max_qps:.2f}) "

        # 限制在合理范围内
        target_rate = max(self.min_tokens_per_minute,
                         min(self.max_tokens_per_minute, target_rate))

        return target_rate, adjustment_reason.strip()

    def _adjust_rate_if_needed(self):
        """根据性能指标调整速率"""
        if not self.adaptive:
            return

        result = self._calculate_optimal_rate()
        if isinstance(result, tuple):
            target_rate, reason = result
        else:
            target_rate = result
            reason = ""

        if abs(target_rate - self.tokens_per_minute) > self.tokens_per_minute * 0.05:  # 5%以上的变化才调整
            with self.lock:
                old_rate = self.tokens_per_minute
                self.tokens_per_minute = target_rate
                self.tokens_per_second = target_rate / 60.0

                # 调整桶容量
                self.bucket_capacity = int(target_rate * 1.5)
                self.current_tokens = min(self.current_tokens, self.bucket_capacity)

                self.last_rate_adjustment_time = time.time()
                self.rate_adjustment_history.append({
                    'timestamp': time.time(),
                    'old_rate': old_rate,
                    'new_rate': target_rate,
                    'reason': reason
                })

                # 保持历史记录不超过50条
                if len(self.rate_adjustment_history) > 50:
                    self.rate_adjustment_history.pop(0)

                print(f"🎯 速率调整: {old_rate:.1f} -> {target_rate:.1f} 令牌/分钟 ({reason})")

    def _refill_tokens(self):
        """重新填充令牌"""
        now = time.time()
        time_passed = now - self.last_refill_time
        new_tokens = time_passed * self.tokens_per_second

        if new_tokens > 0:
            self.current_tokens = min(self.bucket_capacity, self.current_tokens + new_tokens)
            self.last_refill_time = now

    def get_token(self, timeout=None):
        """
        尝试获取一个令牌

        :param timeout: 等待令牌的最大时间（秒），如果为None则无限等待
        :return: 是否成功获取到令牌
        """
        # 在获取令牌前调整速率
        self._adjust_rate_if_needed()

        with self.lock:
            self._refill_tokens()

            # 如果令牌不足，计算需要等待的时间
            if self.current_tokens < 1:
                # 计算获得一个令牌需要的时间
                required_time = (1 - self.current_tokens) / self.tokens_per_second

                # 如果设置了超时且等待时间超过超时时间，则放弃
                if timeout is not None and required_time > timeout:
                    return False

                # 应用额外等待时间（作为惩罚/退避机制）
                wait_time = required_time + min(self.additional_wait, 1.0)
                if self.additional_wait > 0:
                    self.additional_wait = max(0, self.additional_wait - 1.0)

                # 释放锁，以便其他线程可以在我们等待的同时获取令牌
                self.lock.release()
                try:
                    # 记录等待统计信息
                    self.wait_count += 1
                    self.total_wait_time += wait_time

                    # 添加随机抖动
                    jitter = random.uniform(0, 0.1 * wait_time)
                    time.sleep(wait_time + jitter)
                finally:
                    # 重新获取锁
                    self.lock.acquire()

                # 等待后重新填充令牌
                self._refill_tokens()

                # 再次检查令牌是否足够
                if self.current_tokens < 1:
                    # 超时或其他线程可能获取了令牌
                    return False

            # 获取令牌
            self.current_tokens -= 1
            return True

    def wait_if_needed(self):
        """等待直到获取到令牌"""
        request_start_time = self.metrics.record_request_start()
        success = False
        try:
            while not self.get_token():
                # 如果获取令牌失败，尝试再次获取
                # 这在timeout参数被设置且达到超时时可能发生
                time.sleep(0.1)  # 短暂等待避免CPU忙等
            success = True
            return request_start_time
        finally:
            if not success:
                # 记录获取令牌失败
                self.metrics.record_request_complete(request_start_time, False, 'token_acquisition_failed')

    def report_rate_limit_hit(self):
        """报告遇到了速率限制(429错误)"""
        with self.lock:
            self.rate_limit_hit_count += 1
            self.consecutive_failures += 1
            self.consecutive_successes = 0  # 重置连续成功计数

            # 如果连续遇到多次速率限制，增加额外等待时间
            if self.consecutive_failures > 3:
                self.additional_wait += self.consecutive_failures * 0.5

                # 立即降低速率
                if self.adaptive:
                    old_rate = self.tokens_per_minute
                    self.tokens_per_minute = max(self.min_tokens_per_minute,
                                               self.tokens_per_minute * 0.8)
                    self.tokens_per_second = self.tokens_per_minute / 60.0
                    if old_rate != self.tokens_per_minute:
                        print(f"⚠️ 检测到频繁限流，立即降低速率至每分钟{self.tokens_per_minute:.1f}个")

    def report_success(self):
        """报告请求成功"""
        with self.lock:
            # 重置连续失败计数
            self.consecutive_failures = 0
            self.consecutive_successes += 1

    def get_current_workers(self):
        """获取当前建议的工作线程数"""
        return self.concurrency_controller.get_current_concurrency()

    def record_request_result(self, start_time, success=True, error_type=None):
        """记录请求结果"""
        self.metrics.record_request_complete(start_time, success, error_type)
        self.concurrency_controller.record_request(start_time, success, error_type)

    def get_stats(self):
        """获取限流器统计信息"""
        with self.lock:
            concurrency_stats = self.concurrency_controller.get_stats()
            performance = self.metrics.get_performance_summary()

            stats = {
                "tokens_per_minute": self.tokens_per_minute,
                "initial_tokens_per_minute": self.initial_tokens_per_minute,
                "current_tokens": self.current_tokens,
                "bucket_capacity": self.bucket_capacity,
                "rate_limit_hit_count": self.rate_limit_hit_count,
                "consecutive_failures": self.consecutive_failures,
                "consecutive_successes": self.consecutive_successes,
                "additional_wait": self.additional_wait,
                "avg_wait_time": self.total_wait_time / max(1, self.wait_count) if self.wait_count > 0 else 0,
                "total_wait_time": self.total_wait_time,
                "wait_count": self.wait_count,
                "adaptive": self.adaptive,
                "rate_adjustments": len(self.rate_adjustment_history),
                "recent_rate_adjustments": self.rate_adjustment_history[-3:] if self.rate_adjustment_history else [],
                "concurrency_stats": concurrency_stats,
                "performance": performance
            }
            return stats
    
    def _refill_tokens(self):
        """重新填充令牌"""
        now = time.time()
        time_passed = now - self.last_refill_time
        new_tokens = time_passed * self.tokens_per_second
        
        if new_tokens > 0:
            self.current_tokens = min(self.bucket_capacity, self.current_tokens + new_tokens)
            self.last_refill_time = now
    


# 优先级重试队列实现
class PriorityRetryQueue:
    def __init__(self):
        self.queue = []  # 优先级队列
        self.entry_count = 0  # 用于相同优先级时的FIFO顺序
        self.lock = threading.Lock()  # 线程安全锁
        self.retry_counts = {}  # 追踪每个任务的重试次数 {(field_key, file_name): retry_count}
    
    def add_task(self, field_key, unique_descriptions, prompt_template, file_name, retry_count=0):
        """添加任务到优先级队列"""
        with self.lock:
            task_key = (field_key, file_name)
            # 更新或记录重试次数
            if task_key in self.retry_counts:
                self.retry_counts[task_key] = max(self.retry_counts[task_key], retry_count)
            else:
                self.retry_counts[task_key] = retry_count
            
            # 优先级取反，使重试次数多的任务优先级更高
            priority = -self.retry_counts[task_key]
            
            # 使用heapq管理优先级队列
            heapq.heappush(self.queue, (priority, self.entry_count, field_key, unique_descriptions, prompt_template, file_name))
            self.entry_count += 1
            
            if len(self.queue) == 1 or priority < self.queue[0][0]:
                # 如果是第一个任务或优先级更高，打印提示
                print(f"ℹ️ 添加高优先级任务: {field_key} 在 {os.path.basename(file_name)} (重试次数: {retry_count})")
    
    def get_all_tasks_sorted(self):
        """获取所有任务，按优先级排序"""
        with self.lock:
            # 复制队列并按优先级排序
            sorted_tasks = sorted(self.queue)
            tasks = [(item[2], item[3], item[4], item[5], -item[0]) for item in sorted_tasks]  # (field_key, unique_descriptions, prompt_template, file_name, retry_count)
            self.queue = []  # 清空队列
            self.retry_counts = {}  # 清空重试计数
            return tasks
    
    def is_empty(self):
        """检查队列是否为空"""
        with self.lock:
            return len(self.queue) == 0
    
    def size(self):
        """获取队列大小"""
        with self.lock:
            return len(self.queue)

# 创建全局限流器和重试队列实例
api_limiter = EnhancedTokenBucketRateLimiter(API_RATE_LIMIT)
retry_queue = PriorityRetryQueue()

# --- 工具函数：从文件名中提取样式代码 ---
def extract_style_code(filename):
    """从文件名中提取样式代码，例如从 SDBS2401K_analysis_results_20250415_222906.json 提取 SDBS2401K"""
    basename = os.path.basename(filename)
    parts = basename.split('_analysis_results_')
    if len(parts) > 0:
        return parts[0]
    return "Unknown"  # 如果无法提取，返回 Unknown

# --- Function Definitions ---
def extract_descriptions(json_filepath, field_key, sub_key='Description'):
    """
    Extracts specific descriptions from the 'Pros' section of the JSON file.
    """
    print(f"ℹ️ Extracting '{field_key}' -> '{sub_key}' from file: {os.path.basename(json_filepath)}")
    all_descriptions = []
    try:
        with open(json_filepath, 'r', encoding='utf-8') as f:
            analysis_results = json.load(f)
        print(f"✅ Successfully read JSON file with {len(analysis_results)} records.")
    except FileNotFoundError:
        print(f"❌ ERROR: File not found '{json_filepath}'.")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ ERROR: Failed to parse JSON file '{json_filepath}': {e}")
        return None
    except Exception as e:
        print(f"❌ ERROR: Unexpected error reading file '{json_filepath}': {e}")
        return None

    processed_count = 0
    skipped_no_success = 0
    skipped_no_pro = 0
    skipped_no_field = 0
    skipped_no_subkey = 0
    extraction_errors = 0

    for idx, result_package in enumerate(analysis_results):
        try:
            # Check if analysis was successful and basic structure exists
            if not (isinstance(result_package, dict) and
                    'processing_result' in result_package and
                    isinstance(result_package['processing_result'], dict) and
                    result_package['processing_result'].get('analysis_status') == 'success' and
                    'analysis_data' in result_package['processing_result'] and
                    isinstance(result_package['processing_result']['analysis_data'], dict)):
                skipped_no_success += 1
                continue # Skip records not successfully processed

            analysis_data = result_package['processing_result']['analysis_data']

            if 'Pros' not in analysis_data or not isinstance(analysis_data['Pros'], list):
                skipped_no_pro += 1
                continue # Skip if 'Pros' is missing or not a list

            processed_count += 1 # Count records with valid 'Pros' list
            pros_list = analysis_data['Pros']

            for pro_item in pros_list:
                # Check if the pro_item is a dict and contains the field_key
                if not (isinstance(pro_item, dict) and field_key in pro_item):
                    skipped_no_field += 1
                    continue

                field_data = pro_item[field_key]

                # Check if the field_data is a dict and contains the sub_key
                if not (isinstance(field_data, dict) and sub_key in field_data):
                    skipped_no_subkey += 1
                    continue

                description = field_data[sub_key]

                # Check if the description is a non-empty string
                if isinstance(description, str) and description.strip():
                    all_descriptions.append(description.strip())

        except Exception as e:
            print(f"⚠️ Error processing record index {idx} for '{field_key}': {e}")
            extraction_errors += 1
            continue # Continue to the next record

    print(f"📊 Extraction for '{field_key}' complete:")
    print(f"   - Records with 'Pros' list processed: {processed_count}")
    print(f"   - Records skipped (no success/data): {skipped_no_success}")
    print(f"   - Records skipped (no 'Pros' list): {skipped_no_pro}")
    print(f"   - 'Pro' items skipped (missing '{field_key}'): {skipped_no_field}")
    print(f"   - '{field_key}' items skipped (missing '{sub_key}'): {skipped_no_subkey}")
    print(f"   - Errors during record processing: {extraction_errors}")
    print(f"   => Total '{field_key}' descriptions extracted: {len(all_descriptions)}")
    return all_descriptions


def parse_llm_response(response_content):
    """解析LLM响应，处理可能的各种格式问题"""
    print("   解析LLM响应中...")
    # 清理代码块标记
    if response_content.startswith("```python"):
        response_content = response_content[len("```python"):].strip()
    if response_content.endswith("```"):
        response_content = response_content[:-len("```")].strip()
    
    # 处理双大括号问题 - 这是当前错误的主要原因
    if response_content.startswith("{{") and response_content.endswith("}}"):
        response_content = response_content[2:-2].strip()
    
    # 确保使用单大括号包裹内容
    if not response_content.startswith("{"):
        response_content = "{" + response_content
    if not response_content.endswith("}"):
        response_content = response_content + "}"
    
    try:
        # 尝试解析
        parsed_dict = ast.literal_eval(response_content)
        if isinstance(parsed_dict, dict):
            return parsed_dict
        else:
            print(f"   ❌ 解析错误: 预期得到字典，实际得到 {type(parsed_dict)}。")
            return None
    except (SyntaxError, ValueError) as e:
        print(f"   ❌ 解析错误: {e}. 尝试使用JSON解析...")
        
        # 尝试JSON解析作为备选方法
        try:
            # 将单引号替换为双引号，使其成为有效的JSON
            json_compatible = re.sub(r"'([^']*)'", r'"\1"', response_content)
            return json.loads(json_compatible)
        except Exception as e2:
            print(f"   ❌ JSON解析也失败了: {e2}")
            return None
    except Exception as e:
        print(f"   ❌ 未预期的解析错误: {e}")
        return None


def call_llm_for_clustering(unique_descriptions, prompt_template, file_name, field_key, current_retry_count=0):
    """使用基于指数退避的重试机制调用LLM API进行聚类，并收集token使用信息"""
    if not unique_descriptions:
        print("ℹ️ No unique descriptions provided for clustering.")
        return None, None, {'total_attempts': 0, 'failed_attempts': 0, 'failure_rate': 0.0, 'error_types': {}, 'token_usage': None}

    file_basename = os.path.basename(file_name)
    print(f"\n🤖 [{file_basename}] Preparing to call LLM for clustering {len(unique_descriptions)} unique '{field_key}' items...")

    # 准备API统计
    api_stats = {
        'total_attempts': 0,
        'failed_attempts': 0,
        'failure_rate': 0.0,
        'error_types': {},  # 记录不同类型的错误
        'token_usage': None  # 用于存储token使用信息
    }

    # 错误类型分类和对应的退避策略
    error_strategies = {
        'rate_limit': {'base_delay': 2.0, 'max_delay': 120, 'backoff_factor': 2.0},
        'timeout': {'base_delay': 1.0, 'max_delay': 30, 'backoff_factor': 1.5},
        'server_error': {'base_delay': 5.0, 'max_delay': 60, 'backoff_factor': 2.0},
        'network_error': {'base_delay': 1.0, 'max_delay': 30, 'backoff_factor': 1.8},
        'client_error': {'base_delay': 0.5, 'max_delay': 10, 'backoff_factor': 1.2},
        'unknown': {'base_delay': 2.0, 'max_delay': 60, 'backoff_factor': 2.0}
    }

    def classify_error(exception, status_code=None):
        """分类错误类型"""
        if status_code == 429:
            return 'rate_limit'
        elif status_code and 500 <= status_code < 600:
            return 'server_error'
        elif status_code and 400 <= status_code < 500:
            return 'client_error'
        elif isinstance(exception, requests.exceptions.Timeout):
            return 'timeout'
        elif isinstance(exception, (requests.exceptions.ConnectionError,
                                   requests.exceptions.NetworkTimeout)):
            return 'network_error'
        else:
            return 'unknown'

    def calculate_backoff_delay(error_type, attempt, base_delay_override=None):
        """计算退避延迟时间"""
        strategy = error_strategies.get(error_type, error_strategies['unknown'])
        base_delay = base_delay_override or strategy['base_delay']
        max_delay = strategy['max_delay']
        backoff_factor = strategy['backoff_factor']

        # 指数退避 + 随机抖动
        delay = min(max_delay, base_delay * (backoff_factor ** attempt))
        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter

    # 准备提示
    item_list_str = repr(unique_descriptions)
    try:
        final_prompt = prompt_template.replace("{item_list_string}", item_list_str)
    except Exception as e:
        error_msg = f"Unexpected error during prompt formatting: {e}"
        print(f"❌ CRITICAL ERROR: {error_msg}.")
        api_stats['error_types']['prompt_formatting'] = 1
        return None, error_msg, api_stats

    messages = [
        {"role": "user", "content": final_prompt}
    ]
    headers = {
        "Authorization": f"Bearer {API_TOKEN}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": DEFAULT_MODEL,
        "messages": messages,
        "stream": False,
        "temperature": 0.5,
        "max_tokens": 4000
    }

    llm_response_content = None
    last_error = None
    actual_retry_count = current_retry_count  # 实际重试次数，初始化为传入的值

    for attempt in range(MAX_RETRIES):
        # 增加API调用尝试计数
        api_stats['total_attempts'] += 1

        # 等待API速率限制，如果需要的话，并获取请求开始时间
        request_start_time = api_limiter.wait_if_needed()

        print(f"   [{file_basename}] Attempting API call for '{field_key}' ({attempt + 1}/{MAX_RETRIES})...")

        success = False
        error_type = 'unknown'
        status_code = None

        try:
            response = requests.post(API_URL, headers=headers, json=payload, timeout=240)  # 4 min timeout
            status_code = response.status_code
            response.raise_for_status()
            llm_response = response.json()

            # 保存token使用信息
            if "usage" in llm_response:
                api_stats['token_usage'] = llm_response["usage"]
                # 将token使用信息添加到全局统计
                all_token_usages.append(llm_response["usage"])

            if "choices" in llm_response and len(llm_response["choices"]) > 0 and "message" in llm_response["choices"][0] and "content" in llm_response["choices"][0]["message"]:
                llm_response_content = llm_response["choices"][0]["message"]["content"].strip()
                print(f"   [{file_basename}] ✅ API call successful for '{field_key}', response received.")
                success = True
                # 报告成功
                api_limiter.report_success()
                api_limiter.record_request_result(request_start_time, True)
                break
            else:
                # API调用失败 - 无效响应结构
                api_stats['failed_attempts'] += 1
                error_detail = f"Invalid response structure: {llm_response}"
                error_type = "invalid_response_structure"
                if error_type not in api_stats['error_types']:
                    api_stats['error_types'][error_type] = 0
                api_stats['error_types'][error_type] += 1

                print(f"   [{file_basename}] ⚠️ API response structure error for '{field_key}': {error_detail[:500]}...")
                last_error = f"Invalid response structure after {attempt+1} attempts"

                # 记录失败
                api_limiter.record_request_result(request_start_time, False, error_type)

                if attempt < MAX_RETRIES - 1:
                    # 使用智能退避策略
                    wait_time = calculate_backoff_delay(error_type, attempt)
                    print(f"   [{file_basename}] Retrying in {wait_time:.2f}s...")
                    time.sleep(wait_time)

        except requests.exceptions.HTTPError as http_err:
            # API调用失败 - HTTP错误
            api_stats['failed_attempts'] += 1
            actual_retry_count += 1  # 增加实际重试次数
            status_code = http_err.response.status_code
            error_type = classify_error(http_err, status_code)

            if status_code == 429:
                # 处理速率限制错误
                print(f"   [{file_basename}] ⚠️ Rate limit error (HTTP 429) for '{field_key}'.")
                api_limiter.report_rate_limit_hit()

                # 检查Retry-After头
                retry_after = http_err.response.headers.get('Retry-After')
                base_delay = None

                if retry_after:
                    try:
                        # 如果Retry-After是秒数
                        base_delay = int(retry_after)
                        print(f"   API suggests waiting {base_delay}s (Retry-After header).")
                    except ValueError:
                        try:
                            # 如果Retry-After是日期时间字符串
                            retry_after_dt = datetime.strptime(retry_after, '%a, %d %b %Y %H:%M:%S GMT')
                            retry_after_dt_aware = retry_after_dt.replace(tzinfo=timezone.utc)
                            now_utc = datetime.now(timezone.utc)
                            wait_time_from_date = max(0, (retry_after_dt_aware - now_utc).total_seconds())
                            base_delay = wait_time_from_date
                            print(f"   API suggests waiting until {retry_after}, approx {base_delay:.1f}s.")
                        except ValueError:
                            print(f"   Cannot parse Retry-After date format ('{retry_after}'). Using smart backoff.")
            else:
                # 其他HTTP错误
                print(f"   [{file_basename}] ❌ HTTP error {status_code} for '{field_key}'.")

            if error_type not in api_stats['error_types']:
                api_stats['error_types'][error_type] = 0
            api_stats['error_types'][error_type] += 1

            last_error = f"HTTP error {status_code} after {attempt+1} attempts"

            # 记录失败
            api_limiter.record_request_result(request_start_time, False, error_type)

            if attempt < MAX_RETRIES - 1:
                wait_time = calculate_backoff_delay(error_type, attempt, base_delay)
                print(f"   [{file_basename}] Retrying in {wait_time:.2f}s...")
                time.sleep(wait_time)
            elif status_code == 429:
                # 速率限制错误达到最大重试次数，添加到优先级队列
                retry_queue.add_task(field_key, unique_descriptions, prompt_template, file_name, actual_retry_count)
                print(f"   [{file_basename}] Added task to priority retry queue (retry count: {actual_retry_count}).")
        
        except requests.exceptions.Timeout as timeout_err:
            # API调用失败 - 超时
            api_stats['failed_attempts'] += 1
            actual_retry_count += 1  # 增加实际重试次数
            error_type = classify_error(timeout_err)
            if error_type not in api_stats['error_types']:
                api_stats['error_types'][error_type] = 0
            api_stats['error_types'][error_type] += 1

            print(f"   [{file_basename}] ⚠️ API call timed out for '{field_key}'.")
            last_error = f"API timeout after {attempt+1} attempts"

            # 记录失败
            api_limiter.record_request_result(request_start_time, False, error_type)

            if attempt < MAX_RETRIES - 1:
                wait_time = calculate_backoff_delay(error_type, attempt)
                print(f"   [{file_basename}] Retrying in {wait_time:.2f}s...")
                time.sleep(wait_time)
            else:
                # 添加到优先级队列
                retry_queue.add_task(field_key, unique_descriptions, prompt_template, file_name, actual_retry_count)
                print(f"   [{file_basename}] Added task to priority retry queue (retry count: {actual_retry_count}).")

        except requests.exceptions.RequestException as req_err:
            # API调用失败 - 请求异常
            api_stats['failed_attempts'] += 1
            actual_retry_count += 1  # 增加实际重试次数

            # 确定错误类型
            error_type = classify_error(req_err)

            if error_type not in api_stats['error_types']:
                api_stats['error_types'][error_type] = 0
            api_stats['error_types'][error_type] += 1

            error_msg = f"API call failed: {req_err}"
            print(f"   [{file_basename}] ❌ {error_msg} for '{field_key}'")
            last_error = f"Request exception: {error_msg[:200]}"

            # 记录失败
            api_limiter.record_request_result(request_start_time, False, error_type)

            # 计算重试延迟时间
            if attempt < MAX_RETRIES - 1:
                wait_time = calculate_backoff_delay(error_type, attempt)
                print(f"   [{file_basename}] Retrying in {wait_time:.2f}s...")
                time.sleep(wait_time)
            else:
                # 添加到优先级队列
                retry_queue.add_task(field_key, unique_descriptions, prompt_template, file_name, actual_retry_count)
                print(f"   [{file_basename}] Added task to priority retry queue (retry count: {actual_retry_count}).")

        except Exception as general_err:
            # API调用失败 - 未预期的错误
            api_stats['failed_attempts'] += 1
            error_type = classify_error(general_err)
            if error_type not in api_stats['error_types']:
                api_stats['error_types'][error_type] = 0
            api_stats['error_types'][error_type] += 1

            error_msg = f"Unexpected error during API call: {general_err}"
            print(f"   [{file_basename}] ❌ {error_msg} for '{field_key}'")
            last_error = error_msg

            # 记录失败
            api_limiter.record_request_result(request_start_time, False, error_type)

            if attempt < MAX_RETRIES - 1:
                wait_time = calculate_backoff_delay(error_type, attempt)
                print(f"   [{file_basename}] Retrying in {wait_time:.2f}s...")
                time.sleep(wait_time)
    
    # 计算API失败率
    if api_stats['total_attempts'] > 0:
        api_stats['failure_rate'] = (api_stats['failed_attempts'] / api_stats['total_attempts']) * 100

    # 如果所有重试都失败，返回最后一个错误信息
    if not llm_response_content:
        error_msg = last_error or f"API call failed after {MAX_RETRIES} attempts"
        print(f"   [{file_basename}] ❌ {error_msg} for '{field_key}'")
        return None, error_msg, api_stats

    # 解析LLM响应
    print(f"   [{file_basename}] Parsing LLM cluster response for '{field_key}'...")
    clusters = parse_llm_response(llm_response_content)
    
    if clusters:
        print(f"   [{file_basename}] ✅ Successfully parsed {len(clusters)} clusters for '{field_key}'.")
        # 验证结构（值应该是列表）
        valid_clusters = {}
        for key, value in clusters.items():
            if isinstance(value, list):
                valid_clusters[key] = value
            else:
                print(f"   [{file_basename}] ⚠️ Warning: Cluster '{key}' value is not a list (type: {type(value)}), skipping.")
        return valid_clusters, None, api_stats
    else:
        # 解析失败也算作API调用的一种失败
        api_stats['failed_attempts'] += 1
        api_stats['failure_rate'] = (api_stats['failed_attempts'] / api_stats['total_attempts']) * 100
        error_type = "parse_error"
        if error_type not in api_stats['error_types']:
            api_stats['error_types'][error_type] = 0
        api_stats['error_types'][error_type] += 1
        
        error_msg = "Failed to parse LLM response"
        print(f"   [{file_basename}] ❌ {error_msg} for '{field_key}'")
        return None, error_msg, api_stats


def calculate_cluster_frequencies(clusters, all_original_descriptions, file_name, field_key):
    """计算每个聚类的频率"""
    file_basename = os.path.basename(file_name)
    
    if not clusters:
        print(f"ℹ️ [{file_basename}] No clusters provided for '{field_key}' frequency calculation.")
        return {}
    if not all_original_descriptions:
        print(f"ℹ️ [{file_basename}] Original '{field_key}' descriptions list is empty, cannot calculate frequencies.")
        return {}

    print(f"\n📊 [{file_basename}] Calculating '{field_key}' cluster frequencies...")
    original_counts = Counter(all_original_descriptions)
    cluster_frequencies = defaultdict(lambda: {'frequency': 0, 'original_descriptions': []})

    llm_items_in_clusters = set()
    processed_original_items = set()

    for cluster_label, items_in_cluster in clusters.items():
        cluster_total_freq = 0
        original_desc_list_for_cluster = []
        if not isinstance(items_in_cluster, list): continue

        for item in items_in_cluster:
            if isinstance(item, str):
                llm_items_in_clusters.add(item)
                item_freq = original_counts.get(item, 0)
                if item_freq > 0:
                    cluster_total_freq += item_freq
                    original_desc_list_for_cluster.append(f"{item} ({item_freq})")
                    processed_original_items.add(item)
                else:
                    original_desc_list_for_cluster.append(f"{item} (Original Not Found)")

        cluster_frequencies[cluster_label]['frequency'] = cluster_total_freq
        cluster_frequencies[cluster_label]['original_descriptions'] = "; ".join(sorted(original_desc_list_for_cluster))
        print(f"   [{file_basename}] Cluster '{cluster_label}': Frequency = {cluster_total_freq}")

    # 检查差异
    original_unique_items = set(original_counts.keys())
    unclustered_original = original_unique_items - processed_original_items
    llm_unmatched = llm_items_in_clusters - original_unique_items

    if unclustered_original:
        print(f"\n   [{file_basename}] ⚠️ Found {len(unclustered_original)} original unique '{field_key}' item(s) not assigned by LLM.")
        unclustered_freq = sum(original_counts[item] for item in unclustered_original)
        unclustered_list = [f"{item} ({original_counts[item]})" for item in sorted(list(unclustered_original))]
        cluster_frequencies["_Unclustered_"]["frequency"] = unclustered_freq
        cluster_frequencies["_Unclustered_"]["original_descriptions"] = "; ".join(unclustered_list)
        print(f"   [{file_basename}] -> Created '_Unclustered_' category for '{field_key}', Freq={unclustered_freq}")

    if llm_unmatched:
        print(f"\n   [{file_basename}] ⚠️ Found {len(llm_unmatched)} '{field_key}' item(s) in LLM output not exactly matching originals.")
        unmatched_list_str = "; ".join(sorted(list(llm_unmatched)))
        cluster_frequencies["_LLM_Unmatched_"]["frequency"] = 0
        cluster_frequencies["_LLM_Unmatched_"]["original_descriptions"] = unmatched_list_str
        print(f"   [{file_basename}] -> Created '_LLM_Unmatched_' category for '{field_key}'.")

    return dict(cluster_frequencies)


def save_clusters_to_excel(cluster_data, output_dir, field_name_slug, base_filename_prefix="llm_cluster_analysis", style_code=""):
    """保存聚类数据到Excel文件，增加样式代码列"""
    if not cluster_data:
        print(f"ℹ️ No cluster data for '{field_name_slug}' to save.")
        return

    # 排序数据：频率降序，然后标签升序
    sorted_clusters = sorted(
        cluster_data.items(),
        key=lambda item: (-item[1].get('frequency', 0), item[0])
    )

    output_list = [{
        "Style Code": style_code,
        "Cluster Label": label,
        "Total Frequency": data.get('frequency', 0),
        "Original Items (Count/Status)": data.get('original_descriptions', '')
    } for label, data in sorted_clusters]

    df = pd.DataFrame(output_list)

    # 构建文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"{base_filename_prefix}_{field_name_slug}_{timestamp}.xlsx"
    excel_filepath = os.path.join(output_dir, excel_filename)

    print(f"\n💾 Saving '{field_name_slug}' clusters to Excel: {excel_filepath}")

    try:
        os.makedirs(output_dir, exist_ok=True)
        df.to_excel(excel_filepath, index=False, engine='openpyxl')
        print(f"✅ Successfully wrote {len(output_list)} cluster rows to {excel_filepath}")
    except PermissionError:
        print(f"❌ ERROR: Permission denied writing to '{output_dir}'.")
    except Exception as e:
        print(f"❌ ERROR: Failed to save Excel file '{excel_filepath}': {e}")


def save_combined_clusters_to_excel(summary_data, scenario_data, functionality_data, output_dir, base_filename_prefix="llm_combined_cluster_analysis", style_code=""):
    """保存所有三个字段的聚类到单个Excel文件，增加样式代码列"""
    if not (summary_data or scenario_data or functionality_data):
        print("ℹ️ No cluster data to save to combined table.")
        return None

    # 排序每个数据集（按频率降序）
    sorted_summary = sorted(
        summary_data.items(),
        key=lambda item: (-item[1].get('frequency', 0))
    ) if summary_data else []
    
    sorted_scenario = sorted(
        scenario_data.items(),
        key=lambda item: (-item[1].get('frequency', 0))
    ) if scenario_data else []
    
    sorted_functionality = sorted(
        functionality_data.items(),
        key=lambda item: (-item[1].get('frequency', 0))
    ) if functionality_data else []
    
    # 确定需要的最大行数
    max_rows = max(len(sorted_summary), len(sorted_scenario), len(sorted_functionality))
    
    # 创建具有七列的DataFrame（增加样式代码列）
    output_data = []
    for i in range(max_rows):
        row = {}
        # 样式代码列（每行相同）
        row["Style Code"] = style_code
        
        # 添加Summary数据
        if i < len(sorted_summary):
            row["Summary Keyword"] = sorted_summary[i][0]
            row["Summary Frequency"] = sorted_summary[i][1].get('frequency', 0)
        else:
            row["Summary Keyword"] = ""
            row["Summary Frequency"] = ""
        
        # 添加Scenario数据
        if i < len(sorted_scenario):
            row["Scenario Keyword"] = sorted_scenario[i][0]
            row["Scenario Frequency"] = sorted_scenario[i][1].get('frequency', 0)
        else:
            row["Scenario Keyword"] = ""
            row["Scenario Frequency"] = ""
        
        # 添加Functionality数据
        if i < len(sorted_functionality):
            row["Functionality Keyword"] = sorted_functionality[i][0]
            row["Functionality Frequency"] = sorted_functionality[i][1].get('frequency', 0)
        else:
            row["Functionality Keyword"] = ""
            row["Functionality Frequency"] = ""
        
        output_data.append(row)
    
    df = pd.DataFrame(output_data)
    
    # 构建文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    excel_filename = f"{base_filename_prefix}_combined_analysis_{timestamp}.xlsx"
    excel_filepath = os.path.join(output_dir, excel_filename)
    
    print(f"\n💾 Saving combined clusters to Excel: {excel_filepath}")
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        df.to_excel(excel_filepath, index=False, engine='openpyxl')
        print(f"✅ Successfully wrote {len(output_data)} rows to combined table with 7 columns")
    except PermissionError:
        print(f"❌ ERROR: Permission denied writing to '{output_dir}'.")
    except Exception as e:
        print(f"❌ ERROR: Failed to save Excel file '{excel_filepath}': {e}")
    
    # 返回创建的DataFrame和文件路径
    return df, excel_filepath


def process_retry_queue():
    """
    处理优先级重试队列中的失败任务
    
    :return: 重试任务的处理结果列表，格式为(field_key, cluster_data, field_status)元组列表
    """
    if retry_queue.is_empty():
        return []
    
    retry_results = []
    queue_size = retry_queue.size()
    print(f"\n{'='*20} 开始处理优先级重试队列中的 {queue_size} 个任务 {'='*20}")
    
    # 获取所有任务，已按优先级排序
    prioritized_tasks = retry_queue.get_all_tasks_sorted()
    print(f"🔄 任务已按重试次数优先级排序，重试次数最多的将优先处理")
    
    # 更保守的重试参数
    retry_max_retries = MAX_RETRIES + 5  # 为重试队列提供更多的重试机会
    
    # 顺序处理重试队列，避免并发
    for idx, (field_key, unique_descriptions, prompt_template, file_name, retry_count) in enumerate(prioritized_tasks):
        file_basename = os.path.basename(file_name)
        print(f"🔄 处理重试队列中的任务 {idx+1}/{len(prioritized_tasks)}: {field_key} 在 {file_basename} (重试次数: {retry_count})")
        
        # 更长的初始等待时间
        time.sleep(2.0)  
        
        # 使用更保守的参数重新处理，传入当前重试次数
        clusters, error_msg, api_stats = call_llm_for_clustering(
            unique_descriptions, prompt_template, file_name, field_key, current_retry_count=retry_count
        )
        
        # 构建与process_field返回结果相同格式的结果
        field_status = {
            'status': 'success' if clusters else 'failure',
            'error_message': error_msg,
            'processing_time': 0,  # 不计算单独的时间
            'api_stats': api_stats
        }
        
        retry_results.append((field_key, clusters or {}, field_status))
        
        # 每个任务之间的间隔更长，避免触发速率限制
        time.sleep(3.0)
    
    # 任务可能又被添加到重试队列，检查并处理
    if not retry_queue.is_empty():
        print(f"⚠️ 检测到重试队列中还有 {retry_queue.size()} 个任务，继续处理...")
        additional_results = process_retry_queue()
        retry_results.extend(additional_results)
    
    success_count = sum(1 for _, clusters, status in retry_results if status['status'] == 'success' and clusters)
    print(f"✅ 重试队列处理完成，成功: {success_count}/{len(retry_results)}")
    return retry_results


def process_field(json_filepath, field_key, sub_key, field_slug):
    """处理单个字段，可以被并发调用，返回处理结果和状态"""
    file_basename = os.path.basename(json_filepath)
    print(f"\n{'-'*20} Processing Field: {field_key} in {file_basename} {'-'*20}")
    field_start_time = time.perf_counter()
    
    # 提取样式代码，用于token统计
    style_code = extract_style_code(file_basename)
    
    # 结果状态字典，添加API统计
    result_status = {
        'status': 'failure',  # 默认为失败，处理成功后更改
        'error_message': None,
        'processing_time': 0,
        'api_stats': {
            'total_attempts': 0,
            'failed_attempts': 0,
            'failure_rate': 0.0,
            'error_types': {},
            'token_usage': None
        }
    }
    
    try:
        # 1. 提取当前字段的描述
        all_descriptions = extract_descriptions(json_filepath, field_key, sub_key)
        
        if all_descriptions is None:  # 文件错误
            error_msg = "File read/parse error"
            print(f"❌ [{file_basename}] Skipping '{field_key}' due to {error_msg}.")
            result_status['error_message'] = error_msg
            return field_key, None, result_status
            
        if not all_descriptions:
            msg = "No descriptions found"
            print(f"ℹ️ [{file_basename}] {msg} for '{field_key}', skipping clustering.")
            result_status['status'] = 'success'  # 这不是错误，只是没有数据
            result_status['error_message'] = msg
            return field_key, {}, result_status
        
        # 更新样式数据计数（记录数据量）
        if style_code not in style_data_counts:
            style_data_counts[style_code] = {}
        if field_key not in style_data_counts[style_code]:
            style_data_counts[style_code][field_key] = len(all_descriptions)
        
        # 2. 获取唯一描述
        unique_descriptions = sorted(list(set(all_descriptions)))
        print(f"🔍 [{file_basename}] Found {len(unique_descriptions)} unique '{field_key}' descriptions.")
        if not unique_descriptions:
            msg = "No unique descriptions"
            print(f"ℹ️ [{file_basename}] {msg} for '{field_key}', skipping LLM call.")
            result_status['status'] = 'success'
            result_status['error_message'] = msg
            return field_key, {}, result_status
        
        # 3. 选择提示并调用LLM进行聚类
        prompt_template = PROMPT_MAP.get(field_key)
        if not prompt_template:
            error_msg = "No prompt template found"
            print(f"⚠️ [{file_basename}] Warning: {error_msg} for field '{field_key}', skipping LLM call.")
            result_status['error_message'] = error_msg
            return field_key, {}, result_status
        
        # 调用LLM API，包含API统计
        llm_clusters, error_msg, api_stats = call_llm_for_clustering(unique_descriptions, prompt_template, json_filepath, field_key)
        
        # 保存API统计
        result_status['api_stats'] = api_stats
        
        # 保存token使用信息
        if api_stats.get('token_usage'):
            # 更新style的token统计
            if style_code not in style_token_stats:
                style_token_stats[style_code] = {}
            if field_key not in style_token_stats[style_code]:
                style_token_stats[style_code][field_key] = []
            style_token_stats[style_code][field_key].append(api_stats['token_usage'])
        
        if not llm_clusters:  # 处理API错误或空/无效响应
            print(f"❌ [{file_basename}] Failed to get valid clusters from LLM for '{field_key}'.")
            result_status['error_message'] = error_msg or "Unknown error in LLM API call"
            return field_key, {}, result_status
        
        # 4. 计算聚类频率
        cluster_frequencies_data = calculate_cluster_frequencies(llm_clusters, all_descriptions, json_filepath, field_key)
        
        # 处理成功
        result_status['status'] = 'success'
        
    except Exception as e:
        # 捕获处理过程中的任何异常
        error_msg = f"Exception: {str(e)}"
        print(f"❌ [{file_basename}] {error_msg} during processing '{field_key}'")
        result_status['error_message'] = error_msg
        return field_key, {}, result_status
    
    field_end_time = time.perf_counter()
    processing_time = field_end_time - field_start_time
    result_status['processing_time'] = processing_time
    print(f"⏱️ [{file_basename}] Finished processing '{field_key}' in {processing_time:.2f} seconds.")
    
    # 返回字段键、频率数据和处理状态
    return field_key, cluster_frequencies_data, result_status


def process_single_file(json_filepath):
    """处理单个JSON文件，支持字段级并发，返回处理结果和状态"""
    file_basename = os.path.basename(json_filepath)
    print(f"\n{'='*20} Processing File: {file_basename} {'='*20}")
    file_start_time = time.perf_counter()
    
    # 提取样式代码
    style_code = extract_style_code(file_basename)
    print(f"ℹ️ Extracted style code: {style_code} from {file_basename}")
    
    # 确定输出文件前缀
    filename_prefix_parts = file_basename.split('_analysis_results_')
    output_file_prefix = filename_prefix_parts[0] if filename_prefix_parts else os.path.splitext(file_basename)[0]
    print(f"ℹ️ Using output prefix: {output_file_prefix} for {file_basename}")
    
    # 定义要处理的字段
    fields_to_process = [
        ("Summary", "Description", "summary"),
        ("Usage Scenario", "Description", "usage_scenario"),
        ("Functionality", "Description", "functionality"),
    ]
    
    # 准备并发处理结果存储
    results = {}
    
    # 准备状态统计，添加API统计
    file_stats = {
        'file_name': file_basename,
        'style_code': style_code,
        'fields': {},
        'total_fields': len(fields_to_process),
        'successful_fields': 0,
        'failed_fields': 0,
        'success_rate': 0.0,
        'processing_time': 0,
        'api_stats': {
            'total_attempts': 0,
            'failed_attempts': 0, 
            'failure_rate': 0.0,
            'error_types': {}
        },
        'token_stats': {
            'prompt_tokens': 0,
            'completion_tokens': 0,
            'total_tokens': 0,
            'api_calls': 0
        }
    }
    
    # 获取当前最佳的并发线程数
    current_workers = api_limiter.get_current_workers()
    print(f"ℹ️ 使用动态并发数: {current_workers} 个工作线程")

    # 动态调整并发数
    api_limiter.concurrency_controller.adjust_concurrency()
    current_workers = api_limiter.get_current_workers()

    # 使用线程池并发处理每个字段
    with concurrent.futures.ThreadPoolExecutor(max_workers=current_workers) as executor:
        # 创建字段处理任务并收集Future对象
        future_to_field = {
            executor.submit(process_field, json_filepath, field_key, sub_key, field_slug): (field_key, field_slug)
            for field_key, sub_key, field_slug in fields_to_process
        }
        
        # 处理完成的Future
        for future in concurrent.futures.as_completed(future_to_field):
            field_key, field_slug = future_to_field[future]
            try:
                result_field_key, cluster_data, field_status = future.result()
                results[field_slug] = cluster_data
                
                # 记录字段处理状态
                file_stats['fields'][field_key] = field_status
                
                # 根据状态更新统计
                if field_status['status'] == 'success':
                    file_stats['successful_fields'] += 1
                else:
                    file_stats['failed_fields'] += 1
                
                # 累计API统计
                field_api_stats = field_status.get('api_stats', {})
                file_stats['api_stats']['total_attempts'] += field_api_stats.get('total_attempts', 0)
                file_stats['api_stats']['failed_attempts'] += field_api_stats.get('failed_attempts', 0)
                
                # 合并错误类型统计
                for error_type, count in field_api_stats.get('error_types', {}).items():
                    if error_type not in file_stats['api_stats']['error_types']:
                        file_stats['api_stats']['error_types'][error_type] = 0
                    file_stats['api_stats']['error_types'][error_type] += count
                
                # 累计token统计
                if field_api_stats.get('token_usage'):
                    token_usage = field_api_stats['token_usage']
                    file_stats['token_stats']['prompt_tokens'] += token_usage.get('prompt_tokens', 0)
                    file_stats['token_stats']['completion_tokens'] += token_usage.get('completion_tokens', 0)
                    file_stats['token_stats']['total_tokens'] += token_usage.get('total_tokens', 0)
                    file_stats['token_stats']['api_calls'] += 1
                
                # 如果有集群数据，保存到Excel
                if cluster_data:
                    save_clusters_to_excel(cluster_data, OUTPUT_DIRECTORY, field_slug, 
                                          base_filename_prefix=output_file_prefix, style_code=style_code)
                else:
                    print(f"ℹ️ [{file_basename}] No frequency data calculated for '{field_key}', Excel file not generated.")
            except Exception as exc:
                error_msg = f"Exception: {str(exc)}"
                print(f"❌ [{file_basename}] Field processing generated an exception: {exc}")
                # 记录异常
                file_stats['fields'][field_key] = {
                    'status': 'failure',
                    'error_message': error_msg,
                    'processing_time': 0,
                    'api_stats': {'total_attempts': 0, 'failed_attempts': 0, 'failure_rate': 0.0, 'error_types': {}}
                }
                file_stats['failed_fields'] += 1
    
    # 处理重试队列中的失败任务
    if not retry_queue.is_empty():
        print(f"\n⚠️ 检测到有 {retry_queue.size()} 个任务在重试队列中，开始处理...")
        retry_results = process_retry_queue()
        
        # 处理重试结果
        for field_key, cluster_data, field_status in retry_results:
            # 查找对应的field_slug
            field_slug = next((slug for key, _, slug in fields_to_process if key == field_key), None)
            if field_slug:
                # 保存原来的字段状态，用于判断是否需要更新计数
                original_field_status = file_stats['fields'].get(field_key, {})
                original_was_successful = original_field_status.get('status') == 'success'
                
                # 获取原来的API统计，避免重复累计
                original_api_stats = original_field_status.get('api_stats', {})
                original_token_usage = original_api_stats.get('token_usage')
                
                # 更新结果
                results[field_slug] = cluster_data
                
                # 更新字段处理状态
                file_stats['fields'][field_key] = field_status
                
                # 如果重试成功且原来是失败的，更新成功/失败计数
                current_is_successful = field_status['status'] == 'success'
                if current_is_successful and not original_was_successful:
                    file_stats['successful_fields'] += 1
                    file_stats['failed_fields'] = max(0, file_stats['failed_fields'] - 1)  # 防止负数
                    print(f"   ✅ [{file_basename}] Field '{field_key}' 重试成功，更新统计计数")
                
                # 获取重试的API统计
                field_api_stats = field_status.get('api_stats', {})
                
                # 只累计新增的API调用统计（重试的），避免重复计算原来的统计
                new_attempts = field_api_stats.get('total_attempts', 0) - original_api_stats.get('total_attempts', 0)
                new_failures = field_api_stats.get('failed_attempts', 0) - original_api_stats.get('failed_attempts', 0)
                
                # 累计新的API统计
                file_stats['api_stats']['total_attempts'] += max(0, new_attempts)
                file_stats['api_stats']['failed_attempts'] += max(0, new_failures)
                
                # 合并错误类型统计（只计算新增的）
                new_error_types = field_api_stats.get('error_types', {})
                original_error_types = original_api_stats.get('error_types', {})
                
                for error_type, count in new_error_types.items():
                    original_count = original_error_types.get(error_type, 0)
                    new_count = count - original_count
                    if new_count > 0:
                        if error_type not in file_stats['api_stats']['error_types']:
                            file_stats['api_stats']['error_types'][error_type] = 0
                        file_stats['api_stats']['error_types'][error_type] += new_count
                
                # 累计token统计（只计算新的token使用，避免重复）
                current_token_usage = field_api_stats.get('token_usage')
                if current_token_usage and current_token_usage != original_token_usage:
                    file_stats['token_stats']['prompt_tokens'] += current_token_usage.get('prompt_tokens', 0)
                    file_stats['token_stats']['completion_tokens'] += current_token_usage.get('completion_tokens', 0)
                    file_stats['token_stats']['total_tokens'] += current_token_usage.get('total_tokens', 0)
                    file_stats['token_stats']['api_calls'] += 1
                
                # 如果有集群数据，保存到Excel
                if cluster_data:
                    save_clusters_to_excel(cluster_data, OUTPUT_DIRECTORY, field_slug, 
                                          base_filename_prefix=f"{output_file_prefix}_retry", style_code=style_code)
    
    # 计算成功率
    if file_stats['total_fields'] > 0:
        file_stats['success_rate'] = (file_stats['successful_fields'] / file_stats['total_fields']) * 100
    
    # 计算API失败率
    if file_stats['api_stats']['total_attempts'] > 0:
        file_stats['api_stats']['failure_rate'] = (file_stats['api_stats']['failed_attempts'] / file_stats['api_stats']['total_attempts']) * 100
    
    # 保存组合输出
    combined_result = save_combined_clusters_to_excel(
        results.get("summary"), 
        results.get("usage_scenario"), 
        results.get("functionality"),
        OUTPUT_DIRECTORY, 
        base_filename_prefix=output_file_prefix,
        style_code=style_code
    )
    
    file_end_time = time.perf_counter()
    processing_time = file_end_time - file_start_time
    file_stats['processing_time'] = processing_time
    print(f"⏱️ Finished processing file '{file_basename}' in {processing_time:.2f} seconds.")
    
    # 将处理状态添加到全局统计中
    style_processing_stats[style_code] = file_stats
    
    # 返回处理结果和统计
    return {
        'file_name': file_basename,
        'style_code': style_code,
        'combined_data': combined_result[0] if combined_result else None,
        'stats': file_stats
    }

# --- 计算token统计的辅助函数 ---
def calculate_token_statistics():
    """计算全局和每个style的token使用统计"""
    # 全局token统计
    global_token_stats = {
        "prompt_tokens_avg": 0,
        "prompt_tokens_var": 0,
        "completion_tokens_avg": 0,
        "completion_tokens_var": 0,
        "total_tokens_avg": 0,
        "total_tokens_var": 0,
        "api_calls": 0
    }
    
    # 计算全局平均值
    if all_token_usages:
        prompt_tokens = [usage.get('prompt_tokens', 0) for usage in all_token_usages]
        completion_tokens = [usage.get('completion_tokens', 0) for usage in all_token_usages]
        total_tokens = [usage.get('total_tokens', 0) for usage in all_token_usages]
        
        global_token_stats = {
            "prompt_tokens_avg": np.mean(prompt_tokens) if prompt_tokens else 0,
            "prompt_tokens_var": np.var(prompt_tokens) if prompt_tokens else 0,
            "completion_tokens_avg": np.mean(completion_tokens) if completion_tokens else 0,
            "completion_tokens_var": np.var(completion_tokens) if completion_tokens else 0,
            "total_tokens_avg": np.mean(total_tokens) if total_tokens else 0,
            "total_tokens_var": np.var(total_tokens) if total_tokens else 0,
            "api_calls": len(all_token_usages)
        }
    
    # 计算每个style的token统计
    style_stats = {}
    for style_code, fields_data in style_token_stats.items():
        style_stats[style_code] = {
            "fields": {},
            "overall": {
                "prompt_tokens_avg": 0,
                "prompt_tokens_var": 0,
                "completion_tokens_avg": 0,
                "completion_tokens_var": 0,
                "total_tokens_avg": 0,
                "total_tokens_var": 0,
                "api_calls": 0
            }
        }
        
        # 所有字段的合并token列表
        all_prompt_tokens = []
        all_completion_tokens = []
        all_total_tokens = []
        
        # 计算每个字段的统计
        for field_key, usages in fields_data.items():
            if usages:
                prompt_tokens = [usage.get('prompt_tokens', 0) for usage in usages]
                completion_tokens = [usage.get('completion_tokens', 0) for usage in usages]
                total_tokens = [usage.get('total_tokens', 0) for usage in usages]
                
                all_prompt_tokens.extend(prompt_tokens)
                all_completion_tokens.extend(completion_tokens)
                all_total_tokens.extend(total_tokens)
                
                style_stats[style_code]["fields"][field_key] = {
                    "prompt_tokens_avg": np.mean(prompt_tokens) if prompt_tokens else 0,
                    "prompt_tokens_var": np.var(prompt_tokens) if prompt_tokens else 0,
                    "completion_tokens_avg": np.mean(completion_tokens) if completion_tokens else 0,
                    "completion_tokens_var": np.var(completion_tokens) if completion_tokens else 0,
                    "total_tokens_avg": np.mean(total_tokens) if total_tokens else 0,
                    "total_tokens_var": np.var(total_tokens) if total_tokens else 0,
                    "api_calls": len(usages)
                }
        
        # 计算style整体统计
        if all_prompt_tokens:
            style_stats[style_code]["overall"] = {
                "prompt_tokens_avg": np.mean(all_prompt_tokens),
                "prompt_tokens_var": np.var(all_prompt_tokens),
                "completion_tokens_avg": np.mean(all_completion_tokens),
                "completion_tokens_var": np.var(all_completion_tokens),
                "total_tokens_avg": np.mean(all_total_tokens),
                "total_tokens_var": np.var(all_total_tokens),
                "api_calls": len(all_prompt_tokens)
            }
    
    return global_token_stats, style_stats

# --- 打印Token统计报告 ---
def print_token_statistics_report(global_stats, style_stats):
    """打印token使用统计报告"""
    print("\n" + "="*20 + " Token 使用统计 " + "="*20)
    
    # 打印全局统计
    if global_stats["api_calls"] > 0:
        print("\n全局Token统计:")
        print(f"  - 成功API调用总数: {global_stats['api_calls']}")
        print(f"  - 提示tokens平均值: {global_stats['prompt_tokens_avg']:.2f}, 方差: {global_stats['prompt_tokens_var']:.2f}")
        print(f"  - 完成tokens平均值: {global_stats['completion_tokens_avg']:.2f}, 方差: {global_stats['completion_tokens_var']:.2f}")
        print(f"  - 总tokens平均值: {global_stats['total_tokens_avg']:.2f}, 方差: {global_stats['total_tokens_var']:.2f}")
    
    # 打印每个style的统计
    print("\n各Style Token统计:")
    for style_code, stats in style_stats.items():
        data_count = sum(style_data_counts.get(style_code, {}).values())
        api_calls = stats["overall"]["api_calls"]
        print(f"\n  Style '{style_code}':")
        print(f"    - 数据数量: {data_count}, 成功API调用: {api_calls}")
        print(f"    - 提示tokens平均值: {stats['overall']['prompt_tokens_avg']:.2f}, 方差: {stats['overall']['prompt_tokens_var']:.2f}")
        print(f"    - 完成tokens平均值: {stats['overall']['completion_tokens_avg']:.2f}, 方差: {stats['overall']['completion_tokens_var']:.2f}")
        print(f"    - 总tokens平均值: {stats['overall']['total_tokens_avg']:.2f}, 方差: {stats['overall']['total_tokens_var']:.2f}")
        
        # 打印每个字段的统计
        for field_key, field_stats in stats["fields"].items():
            print(f"      > {field_key} (调用: {field_stats['api_calls']}):")
            print(f"        - 提示tokens平均值: {field_stats['prompt_tokens_avg']:.2f}, 方差: {field_stats['prompt_tokens_var']:.2f}")
            print(f"        - 完成tokens平均值: {field_stats['completion_tokens_avg']:.2f}, 方差: {field_stats['completion_tokens_var']:.2f}")
            print(f"        - 总tokens平均值: {field_stats['total_tokens_avg']:.2f}, 方差: {field_stats['total_tokens_var']:.2f}")
    
    # 计算每个style平均数据数量
    if style_data_counts:
        total_data_count = sum(sum(fields.values()) for fields in style_data_counts.values())
        style_count = len(style_data_counts)
        avg_data_count = total_data_count / style_count if style_count > 0 else 0
        print(f"\n每个Style平均数据数量: {avg_data_count:.2f}")

# --- Main Logic ---
def main():
    global all_file_results, style_processing_stats, all_token_usages, style_token_stats, style_data_counts
    
    # 重置全局变量
    all_file_results = []
    style_processing_stats = {}  # 记录每个style的处理统计
    all_token_usages = []  # 所有API调用的token使用情况
    style_token_stats = {}  # 每个style的token统计信息
    style_data_counts = {}  # 每个style的数据数量
    
    print("--- Starting LLM Semantic Clustering Script (Multi-Field, Multi-File, Concurrent) ---")
    overall_start_time = time.perf_counter()

    # --- 获取输入文件夹 ---
    input_dir = INPUT_DIRECTORY
    # 检查文件夹是否存在
    if not os.path.isdir(input_dir):
        print(f"❌ ERROR: Input directory '{input_dir}' does not exist.")
        sys.exit(1)
    
    # 查找所有JSON文件
    json_files = glob.glob(os.path.join(input_dir, "*.json"))
    if not json_files:
        print(f"❌ ERROR: No JSON files found in '{input_dir}'.")
        sys.exit(1)
    
    print(f"ℹ️ Found {len(json_files)} JSON files to process.")
    
    # 使用线程池并发处理文件
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_FILE_WORKERS) as executor:
        # 提交所有文件处理任务
        future_to_file = {executor.submit(process_single_file, json_file): json_file for json_file in json_files}
        
        # 使用tqdm显示进度条
        for future in tqdm(concurrent.futures.as_completed(future_to_file), total=len(json_files), desc="Processing files"):
            json_file = future_to_file[future]
            try:
                result = future.result()
                all_file_results.append(result)
                print(f"✅ Collected results for file: {os.path.basename(json_file)}")
            except Exception as exc:
                print(f"❌ File {os.path.basename(json_file)} generated an exception: {exc}")
    
    # 创建汇总报表
    if all_file_results:
        # 生成正常的数据汇总报表
        master_report_path = create_master_combined_report(all_file_results, OUTPUT_DIRECTORY)
        if master_report_path:
            print(f"🎉 Master data report successfully created at: {master_report_path}")
        
        # 生成处理状态报告，包含API调用失败率
        processing_report = generate_processing_status_report(all_file_results, OUTPUT_DIRECTORY)
        if processing_report is not None:
            print(f"🎉 Processing status report successfully created")
    
    overall_end_time = time.perf_counter()
    total_time = overall_end_time - overall_start_time
    
    # 计算并打印token统计
    global_token_stats, style_token_stats_calc = calculate_token_statistics()
    print_token_statistics_report(global_token_stats, style_token_stats_calc)
    
    # 打印动态调度统计信息
    print(f"\n{'='*20} 动态调度统计 {'='*20}")
    limiter_stats = api_limiter.get_stats()

    # 速率限制统计
    print(f"\n🪣 速率限制统计:")
    print(f"  - 初始速率: {limiter_stats['initial_tokens_per_minute']:.1f} 令牌/分钟")
    print(f"  - 最终速率: {limiter_stats['tokens_per_minute']:.1f} 令牌/分钟")
    print(f"  - 速率调整次数: {limiter_stats['rate_adjustments']}")
    print(f"  - 遇到429错误次数: {limiter_stats['rate_limit_hit_count']}")
    print(f"  - 连续成功次数: {limiter_stats['consecutive_successes']}")
    print(f"  - 令牌桶容量: {limiter_stats['bucket_capacity']}")
    print(f"  - 当前令牌数: {limiter_stats['current_tokens']:.2f}")
    print(f"  - 平均等待时间: {limiter_stats['avg_wait_time']:.2f}秒")
    print(f"  - 总等待时间: {limiter_stats['total_wait_time']:.2f}秒")
    print(f"  - 等待次数: {limiter_stats['wait_count']}")

    # 并发控制统计
    concurrency_stats = limiter_stats.get('concurrency_stats', {})
    print(f"\n🎛️ 并发控制统计:")
    print(f"  - 初始并发数: {MAX_FIELD_WORKERS}")
    print(f"  - 最终并发数: {concurrency_stats.get('current_concurrency', 'N/A')}")
    print(f"  - 并发调整次数: {concurrency_stats.get('adjustment_count', 0)}")
    print(f"  - 并发范围: [{concurrency_stats.get('min_concurrency', 'N/A')}, {concurrency_stats.get('max_concurrency', 'N/A')}]")

    # 性能指标统计
    performance = limiter_stats.get('performance', {})
    print(f"\n📊 性能指标统计:")
    print(f"  - 平均响应时间: {performance.get('avg_response_time', 0):.2f}秒")
    print(f"  - P95响应时间: {performance.get('p95_response_time', 0):.2f}秒")
    print(f"  - 成功率: {performance.get('success_rate', 0):.2%}")
    print(f"  - 实际QPS: {performance.get('current_qps', 0):.2f}")
    print(f"  - 总请求数: {performance.get('total_requests', 0)}")

    # 最近的调整历史
    recent_rate_adjustments = limiter_stats.get('recent_rate_adjustments', [])
    if recent_rate_adjustments:
        print(f"\n🎯 最近的速率调整:")
        for adj in recent_rate_adjustments:
            timestamp = datetime.fromtimestamp(adj['timestamp']).strftime('%H:%M:%S')
            print(f"  - {timestamp}: {adj['old_rate']:.1f} -> {adj['new_rate']:.1f} ({adj['reason']})")

    recent_concurrency_adjustments = concurrency_stats.get('recent_adjustments', [])
    if recent_concurrency_adjustments:
        print(f"\n🎛️ 最近的并发调整:")
        for adj in recent_concurrency_adjustments:
            timestamp = datetime.fromtimestamp(adj['timestamp']).strftime('%H:%M:%S')
            print(f"  - {timestamp}: {adj['old_concurrency']} -> {adj['new_concurrency']} ({adj['reason']})")
    
    # 打印最终的运行总结
    print(f"\n{'='*50}")
    print(f"--- 脚本执行完成，总耗时: {total_time:.2f} 秒 ---")
    print(f"{'='*50}")
    
    # 输出详细的运行报告
    print("\n📋 运行报告摘要:")
    print(f"{'='*50}")
    
    # 统计整体成功/失败情况
    total_styles = len(style_processing_stats)
    fully_successful_styles = sum(1 for stats in style_processing_stats.values() 
                                if stats.get('success_rate', 0) == 100)
    partially_successful_styles = sum(1 for stats in style_processing_stats.values() 
                                   if 0 < stats.get('success_rate', 0) < 100)
    failed_styles = sum(1 for stats in style_processing_stats.values() 
                      if stats.get('success_rate', 0) == 0)
    
    # 按成功率分类
    print(f"总共处理样式(style)数量: {total_styles}")
    print(f"✅ 完全成功处理的样式: {fully_successful_styles} ({fully_successful_styles/total_styles*100:.1f}%)")
    print(f"⚠️ 部分成功处理的样式: {partially_successful_styles} ({partially_successful_styles/total_styles*100:.1f}%)")
    print(f"❌ 完全失败的样式: {failed_styles} ({failed_styles/total_styles*100:.1f}%)")
    
    # 输出API调用情况
    total_api_attempts = sum(stats.get('api_stats', {}).get('total_attempts', 0) for stats in style_processing_stats.values())
    total_api_failures = sum(stats.get('api_stats', {}).get('failed_attempts', 0) for stats in style_processing_stats.values())
    overall_api_failure_rate = (total_api_failures / total_api_attempts * 100) if total_api_attempts > 0 else 0
    
    print(f"\n📡 DeepSeek API调用情况:")
    print(f"总API调用次数: {total_api_attempts}")
    print(f"API调用失败次数: {total_api_failures}")
    print(f"API调用失败率: {overall_api_failure_rate:.2f}%")
    
    # 输出API失败率高的样式
    print(f"\n⚠️ API调用失败率超过50%的样式:")
    print(f"{'-'*50}")
    print(f"{'Style Code':<15} | {'总调用':<8} | {'失败数':<8} | {'失败率':<10} | {'主要错误类型'}")
    print(f"{'-'*50}")
    
    high_api_failure_styles = {}
    for style_code, stats in style_processing_stats.items():
        api_stats = stats.get('api_stats', {})
        if api_stats.get('total_attempts', 0) > 0 and api_stats.get('failure_rate', 0) > 50:
            high_api_failure_styles[style_code] = api_stats
    
    if high_api_failure_styles:
        for style_code, api_stats in sorted(high_api_failure_styles.items(), 
                                           key=lambda x: x[1].get('failure_rate', 0), reverse=True):
            total_attempts = api_stats.get('total_attempts', 0)
            failed_attempts = api_stats.get('failed_attempts', 0)
            failure_rate = api_stats.get('failure_rate', 0)
            
            # 获取最常见的错误类型
            error_types = api_stats.get('error_types', {})
            most_common_error = max(error_types.items(), key=lambda x: x[1])[0] if error_types else 'N/A'
            
            print(f"{style_code:<15} | {total_attempts:<8} | {failed_attempts:<8} | {failure_rate:<10.1f}% | {most_common_error}")
    else:
        print("没有API调用失败率超过50%的样式")
    
    # 输出详细失败率超过50%的样式
    print(f"\n⚠️ 整体失败率超过50%的样式明细:")
    print(f"{'-'*50}")
    print(f"{'Style Code':<15} | {'成功数':<8} | {'失败数':<8} | {'成功率':<10} | {'状态'}")
    print(f"{'-'*50}")
    
    high_failure_styles = {k: v for k, v in style_processing_stats.items() 
                         if v.get('success_rate', 0) < 50}
    
    if high_failure_styles:
        for style_code, stats in sorted(high_failure_styles.items(), 
                                       key=lambda x: x[1].get('success_rate', 0)):
            success_rate = stats.get('success_rate', 0)
            status = "❌ 完全失败" if success_rate == 0 else "⚠️ 大部分失败"
            print(f"{style_code:<15} | {stats.get('successful_fields', 0):<8} | " 
                  f"{stats.get('failed_fields', 0):<8} | {success_rate:<10.1f}% | {status}")
    else:
        print("没有失败率超过50%的样式")
    
    print(f"\n{'='*50}")
    print(f"🔍 详细的处理结果已保存到: {OUTPUT_DIRECTORY}")
    print(f"   - 处理状态报告: processing_status_report_*.xlsx")
    print(f"   - API调用失败率报告: api_failure_report_*.xlsx")
    print(f"   - API错误类型报告: api_error_types_report_*.xlsx")
    print(f"   - 详细失败原因报告: processing_detailed_failures_*.xlsx")
    print(f"   - Token使用统计已打印到控制台")
    print(f"{'='*50}")


# --- Run Main ---
if __name__ == "__main__":
    # Dependency check
    try:
        import pandas
        import openpyxl
        import requests
        # tqdm 用于进度显示
    except ImportError as e:
        print(f"ERROR: Missing required library ({e.name}). Please install.")
        print("Run: pip install pandas openpyxl requests tqdm")
        sys.exit(1)
    main()

def create_master_combined_report(all_results, output_dir):
    """创建汇总所有文件结果的主报表"""
    if not all_results:
        print("❌ No results available to create master report.")
        return
    
    print(f"\n{'='*20} Creating Master Combined Report {'='*20}")
    
    # 准备合并所有数据
    all_data_frames = []
    for result in all_results:
        if 'combined_data' in result and result['combined_data'] is not None:
            all_data_frames.append(result['combined_data'])
    
    if not all_data_frames:
        print("❌ No valid data frames found to create master report.")
        return
    
    # 合并所有DataFrame
    master_df = pd.concat(all_data_frames, ignore_index=True)
    
    # 按样式代码和关键词频率排序
    master_df = master_df.sort_values(by=['Style Code', 'Summary Frequency', 'Scenario Frequency', 'Functionality Frequency'], 
                                      ascending=[True, False, False, False])
    
    # 构建主报表文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    master_filename = f"master_all_styles_combined_report_{timestamp}.xlsx"
    master_filepath = os.path.join(output_dir, master_filename)
    
    print(f"💾 Saving master combined report to: {master_filepath}")
    try:
        os.makedirs(output_dir, exist_ok=True)
        master_df.to_excel(master_filepath, index=False, engine='openpyxl')
        print(f"✅ Successfully created master report with {len(master_df)} rows for {len(all_results)} files")
    except Exception as e:
        print(f"❌ ERROR: Failed to save master report: {e}")
        return
    
    return master_filepath


def generate_processing_status_report(all_results, output_dir):
    """生成处理状态汇总报告，显示每个style的成功/失败率和API调用失败率"""
    if not all_results:
        print("❌ No results available to create status report.")
        return
    
    print(f"\n{'='*20} Generating Processing Status Report {'='*20}")
    
    # 准备报告数据
    report_data = []
    for result in all_results:
        if 'stats' in result:
            stats = result['stats']
            # 添加API统计
            api_stats = stats.get('api_stats', {})
            api_failures = api_stats.get('failed_attempts', 0)
            api_total = api_stats.get('total_attempts', 0)
            api_failure_rate = api_stats.get('failure_rate', 0.0)
            
            report_data.append({
                'Style Code': stats.get('style_code', 'Unknown'),
                'File Name': stats.get('file_name', 'Unknown'),
                'Total Fields': stats.get('total_fields', 0),
                'Successful Fields': stats.get('successful_fields', 0),
                'Failed Fields': stats.get('failed_fields', 0),
                'Success Rate (%)': round(stats.get('success_rate', 0), 2),
                'API Total Attempts': api_total,
                'API Failed Attempts': api_failures,
                'API Failure Rate (%)': round(api_failure_rate, 2),
                'Processing Time (s)': round(stats.get('processing_time', 0), 2)
            })
    
    # 创建DataFrame
    report_df = pd.DataFrame(report_data)
    
    # 排序：先按成功率升序（失败的在前面），然后按样式代码升序
    report_df = report_df.sort_values(by=['Success Rate (%)', 'Style Code'], ascending=[True, True])
    
    # 计算总体统计
    total_files = len(report_data)
    total_fields = sum(row['Total Fields'] for row in report_data)
    total_successful = sum(row['Successful Fields'] for row in report_data)
    total_failed = sum(row['Failed Fields'] for row in report_data)
    overall_success_rate = (total_successful / total_fields * 100) if total_fields > 0 else 0
    
    # API统计
    total_api_attempts = sum(row['API Total Attempts'] for row in report_data)
    total_api_failures = sum(row['API Failed Attempts'] for row in report_data)
    overall_api_failure_rate = (total_api_failures / total_api_attempts * 100) if total_api_attempts > 0 else 0
    
    # 打印控制台汇总
    print("\n📊 Processing Status Summary:")
    print(f"Total Files Processed: {total_files}")
    print(f"Total Fields Processed: {total_fields}")
    print(f"Successfully Processed Fields: {total_successful} ({overall_success_rate:.2f}%)")
    print(f"Failed Fields: {total_failed} ({100-overall_success_rate:.2f}%)")
    print(f"Total API Attempts: {total_api_attempts}")
    print(f"Failed API Attempts: {total_api_failures} ({overall_api_failure_rate:.2f}%)")
    
    # 打印每个style的状态
    print("\nStyle-Level Success Rates:")
    for idx, row in report_df.iterrows():
        status_emoji = "✅" if row['Success Rate (%)'] == 100 else "⚠️" if row['Success Rate (%)'] > 0 else "❌"
        print(f"{status_emoji} {row['Style Code']}: {row['Success Rate (%)']}% ({row['Successful Fields']}/{row['Total Fields']} fields)")
    
    # 打印API失败率高的style
    api_high_failure_df = report_df[report_df['API Failure Rate (%)'] > 50].sort_values(by='API Failure Rate (%)', ascending=False)
    if not api_high_failure_df.empty:
        print("\nStyles with High API Failure Rates (>50%):")
        for idx, row in api_high_failure_df.iterrows():
            if row['API Total Attempts'] > 0:  # 只显示有API调用的
                print(f"⚠️ {row['Style Code']}: {row['API Failure Rate (%)']}% ({row['API Failed Attempts']}/{row['API Total Attempts']} attempts)")
    
    # 保存到Excel
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"processing_status_report_{timestamp}.xlsx"
    report_filepath = os.path.join(output_dir, report_filename)
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        report_df.to_excel(report_filepath, index=False, engine='openpyxl')
        print(f"\n💾 Status report saved to: {report_filepath}")
    except Exception as e:
        print(f"❌ ERROR: Failed to save status report: {e}")
    
    # 创建专门的API失败率报告
    api_report_filename = f"api_failure_report_{timestamp}.xlsx"
    api_report_filepath = os.path.join(output_dir, api_report_filename)
    
    # 创建API统计报告DataFrame
    api_df = pd.DataFrame([{
        'Style Code': row['Style Code'],
        'File Name': row['File Name'],
        'Total API Attempts': row['API Total Attempts'],
        'Failed API Attempts': row['API Failed Attempts'],
        'API Failure Rate (%)': row['API Failure Rate (%)'],
    } for row in report_data if row['API Total Attempts'] > 0])
    
    # 按API失败率降序排序
    api_df = api_df.sort_values(by='API Failure Rate (%)', ascending=False)
    
    try:
        api_df.to_excel(api_report_filepath, index=False, engine='openpyxl')
        print(f"💾 API failure report saved to: {api_report_filepath}")
    except Exception as e:
        print(f"❌ ERROR: Failed to save API failure report: {e}")
    
    # 生成失败详情报告
    detailed_report_filename = f"processing_detailed_failures_{timestamp}.xlsx"
    detailed_report_filepath = os.path.join(output_dir, detailed_report_filename)
    
    # 收集每个style的每个字段的失败原因
    detailed_failure_data = []
    for result in all_results:
        if 'stats' in result:
            stats = result['stats']
            style_code = stats.get('style_code', 'Unknown')
            file_name = stats.get('file_name', 'Unknown')
            
            # 检查每个字段
            for field_name, field_status in stats.get('fields', {}).items():
                if field_status.get('status') == 'failure':
                    field_api_stats = field_status.get('api_stats', {})
                    detailed_failure_data.append({
                        'Style Code': style_code,
                        'File Name': file_name,
                        'Field': field_name,
                        'Error Message': field_status.get('error_message', 'Unknown error'),
                        'API Total Attempts': field_api_stats.get('total_attempts', 0),
                        'API Failed Attempts': field_api_stats.get('failed_attempts', 0),
                        'API Failure Rate (%)': round(field_api_stats.get('failure_rate', 0), 2),
                        'Most Common Error Type': max(field_api_stats.get('error_types', {}).items(), key=lambda x: x[1])[0] if field_api_stats.get('error_types') else 'N/A',
                        'Processing Time (s)': round(field_status.get('processing_time', 0), 2)
                    })
    
    # 创建详细失败报告
    if detailed_failure_data:
        detail_df = pd.DataFrame(detailed_failure_data)
        try:
            detail_df.to_excel(detailed_report_filepath, index=False, engine='openpyxl')
            print(f"💾 Detailed failure report saved to: {detailed_report_filepath}")
        except Exception as e:
            print(f"❌ ERROR: Failed to save detailed failure report: {e}")
    else:
        print("ℹ️ No failures detected, detailed failure report not created.")
    
    # 创建API错误类型统计报告
    error_types_report_filename = f"api_error_types_report_{timestamp}.xlsx"
    error_types_report_filepath = os.path.join(output_dir, error_types_report_filename)
    
    # 汇总所有API错误类型
    all_error_types = {}
    for result in all_results:
        if 'stats' in result:
            stats = result['stats']
            style_code = stats.get('style_code', 'Unknown')
            
            # 统计每个style的每个错误类型数量
            style_error_types = defaultdict(int)
            
            # 从所有字段收集错误类型
            for field_name, field_status in stats.get('fields', {}).items():
                field_api_stats = field_status.get('api_stats', {})
                for error_type, count in field_api_stats.get('error_types', {}).items():
                    style_error_types[error_type] += count
            
            # 添加到全局统计
            if style_error_types:
                all_error_types[style_code] = dict(style_error_types)
    
    # 创建错误类型DataFrame
    if all_error_types:
        # 获取所有可能的错误类型
        error_type_columns = sorted(set().union(*[set(errors.keys()) for errors in all_error_types.values()]))
        
        # 创建数据行
        error_type_rows = []
        for style_code, error_counts in all_error_types.items():
            row = {'Style Code': style_code}
            for error_type in error_type_columns:
                row[error_type] = error_counts.get(error_type, 0)
            row['Total Errors'] = sum(error_counts.values())
            error_type_rows.append(row)
        
        error_type_df = pd.DataFrame(error_type_rows).sort_values(by='Total Errors', ascending=False)
        
        try:
            error_type_df.to_excel(error_types_report_filepath, index=False, engine='openpyxl')
            print(f"💾 API error types report saved to: {error_types_report_filepath}")
        except Exception as e:
            print(f"❌ ERROR: Failed to save API error types report: {e}")
    else:
        print("ℹ️ No API errors detected, error types report not created.")
    
    return report_df


# --- Main Logic ---