import time
import os
from google import genai
from google.genai import types
from google.genai.errors import ClientError

GOOGLE_API_KEY = 'AIzaSyBmnec3Q3xUXAxdQgTWW1Zb2Hc8X7EAFw0'

client = genai.Client(api_key=GOOGLE_API_KEY)

def process_video_with_retry(video_file_path, max_retries=3, base_delay=12):
    """
    带重试机制的视频处理函数
    """
    for attempt in range(max_retries):
        try:
            print(f"尝试第 {attempt + 1} 次处理视频...")
            
            # 检查文件是否存在
            if not os.path.exists(video_file_path):
                print(f"错误：视频文件不存在: {video_file_path}")
                return None
            
            # 检查文件大小（Gemini API 对视频文件有大小限制）
            file_size = os.path.getsize(video_file_path)
            print(f"视频文件大小: {file_size / (1024*1024):.2f} MB")
            
            if file_size > 20 * 1024 * 1024:  # 20MB 限制
                print("警告：视频文件可能超过20MB限制")
            
            # 读取视频文件
            with open(video_file_path, 'rb') as f:
                video_bytes = f.read()
            
            # 发送请求
            response = client.models.generate_content(
                model="models/gemini-2.5-pro",
                contents=types.Content(
                    parts=[
                        types.Part(
                            inline_data=types.Blob(data=video_bytes, mime_type='video/mp4')
                        ),
                        types.Part(text="简要总结这个视频的内容。")
                    ]
                )
            )
            
            return response.text
            
        except ClientError as e:
            if e.status_code == 429:  # RESOURCE_EXHAUSTED
                retry_delay = base_delay * (2 ** attempt)  # 指数退避
                print(f"配额限制错误，等待 {retry_delay} 秒后重试...")
                
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    print("已达到最大重试次数，请稍后再试或考虑升级到付费计划")
                    print("错误详情:", str(e))
                    return None
            else:
                print(f"API错误 (状态码: {e.status_code}): {str(e)}")
                return None
        except Exception as e:
            print(f"其他错误: {str(e)}")
            return None
    
    return None

def main():
    video_file_name = "/data2/jevon/7_24_video_tag/data/video/20241030-BM-Q4-Men’s Oxfords-SBOX2326M/scene_003.mp4"
    
    print("开始处理视频...")
    result = process_video_with_retry(video_file_name)
    
    if result:
        print("\n=== 视频分析结果 ===")
        print(result)
    else:
        print("\n处理失败")

if __name__ == "__main__":
    main() 