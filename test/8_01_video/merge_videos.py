#!/usr/bin/env python3
"""
视频合并脚本
按照指定顺序合并视频文件
"""

import os
import subprocess
import sys
from pathlib import Path

def find_video_file(base_path, relative_path):
    """
    查找视频文件，处理路径匹配问题
    """
    full_path = os.path.join(base_path, relative_path)
    if os.path.exists(full_path):
        return full_path

    # 如果直接路径不存在，尝试模糊匹配
    parts = relative_path.split('/')
    if len(parts) == 2:
        folder_name, file_name = parts

        # 在base_path中查找包含folder_name的文件夹
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                # 尝试精确匹配
                if item == folder_name:
                    potential_file = os.path.join(item_path, file_name)
                    if os.path.exists(potential_file):
                        return potential_file
                # 尝试包含匹配
                elif folder_name in item:
                    potential_file = os.path.join(item_path, file_name)
                    if os.path.exists(potential_file):
                        return potential_file

    return None

def create_file_list(video_base_path, video_order, output_file):
    """
    创建FFmpeg需要的文件列表
    """
    found_files = []
    missing_files = []
    
    for video_path in video_order:
        full_path = find_video_file(video_base_path, video_path)
        if full_path:
            found_files.append(full_path)
            print(f"✓ 找到: {video_path} -> {full_path}")
        else:
            missing_files.append(video_path)
            print(f"✗ 未找到: {video_path}")
    
    if missing_files:
        print(f"\n警告: 有 {len(missing_files)} 个文件未找到:")
        for missing in missing_files:
            print(f"  - {missing}")
        
        response = input("\n是否继续处理找到的文件? (y/n): ")
        if response.lower() != 'y':
            return False
    
    # 创建FFmpeg文件列表
    with open(output_file, 'w', encoding='utf-8') as f:
        for file_path in found_files:
            # FFmpeg需要转义特殊字符
            escaped_path = file_path.replace("'", "'\"'\"'")
            f.write(f"file '{escaped_path}'\n")
    
    print(f"\n文件列表已创建: {output_file}")
    print(f"将合并 {len(found_files)} 个视频文件")
    
    return True

def merge_videos(file_list_path, output_video_path):
    """
    使用FFmpeg合并视频
    """
    cmd = [
        'ffmpeg',
        '-f', 'concat',
        '-safe', '0',
        '-i', file_list_path,
        '-c', 'copy',
        '-y',  # 覆盖输出文件
        output_video_path
    ]
    
    print(f"\n开始合并视频...")
    print(f"输出文件: {output_video_path}")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 视频合并成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 视频合并失败:")
        print(f"错误代码: {e.returncode}")
        print(f"错误信息: {e.stderr}")
        return False
    except FileNotFoundError:
        print("✗ 错误: 未找到ffmpeg命令，请确保已安装FFmpeg")
        return False

def main():
    # 视频文件夹路径
    video_base_path = "/data2/jevon/test/8_01_video/video"
    
    # 按照指定顺序的视频列表 - 第三批
    video_order = [
        "SBOX2326M-0425-03/scene_003.mp4",
        "SBOX2326M-0425-03/scene_004.mp4",
        "SBOX2326M-0425-KOL/scene_001.mp4",
        "20240425-BM-Q2-SBOX2326M&9-16/scene_003.mp4",
        "BM_Urgent-488_0418_V1/scene_010.mp4",
        "20241030-BM-Q4-Men's Oxfords-SBOX2326M/scene_007.mp4",
        "SBOX2326M-0425-KOL/scene_005.mp4",
        "SBOX2326M--KOL/scene_003.mp4"
    ]
    
    # 输出文件路径
    output_dir = "/data2/jevon/test/8_01_video"
    file_list_path = os.path.join(output_dir, "video_list.txt")
    output_video_path = os.path.join(output_dir, "merged_video_batch3.mp4")
    
    print("视频合并脚本")
    print("=" * 50)
    print(f"视频源目录: {video_base_path}")
    print(f"输出目录: {output_dir}")
    print(f"要合并的视频数量: {len(video_order)}")
    print()
    
    # 检查视频源目录是否存在
    if not os.path.exists(video_base_path):
        print(f"错误: 视频源目录不存在: {video_base_path}")
        return 1
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建文件列表
    if not create_file_list(video_base_path, video_order, file_list_path):
        return 1
    
    # 合并视频
    if merge_videos(file_list_path, output_video_path):
        print(f"\n🎉 视频合并完成!")
        print(f"输出文件: {output_video_path}")
        
        # 显示输出文件信息
        if os.path.exists(output_video_path):
            file_size = os.path.getsize(output_video_path) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.2f} MB")
        
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
