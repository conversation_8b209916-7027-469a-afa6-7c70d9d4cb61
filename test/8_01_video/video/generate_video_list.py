#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频文件夹扫描脚本
扫描指定目录下的视频文件夹，按文件夹名称排序，生成xlsx文件
格式: "视频名称/具体分镜.mp4"
"""

import os
import pandas as pd
from pathlib import Path
import argparse

def scan_video_folders(video_dir):
    """
    扫描视频文件夹，返回排序后的视频文件列表
    
    Args:
        video_dir (str): 视频文件夹路径
        
    Returns:
        list: 包含视频路径的列表，格式为 "文件夹名/视频文件名"
    """
    video_list = []
    
    # 检查目录是否存在
    if not os.path.exists(video_dir):
        print(f"错误: 目录 {video_dir} 不存在")
        return video_list
    
    # 获取所有子文件夹并排序
    folders = []
    for item in os.listdir(video_dir):
        item_path = os.path.join(video_dir, item)
        if os.path.isdir(item_path):
            folders.append(item)
    
    # 按文件夹名称排序（由小到大）
    folders.sort()
    
    print(f"找到 {len(folders)} 个文件夹，开始扫描...")
    
    # 遍历每个文件夹
    for folder_name in folders:
        folder_path = os.path.join(video_dir, folder_name)
        print(f"正在扫描文件夹: {folder_name}")
        
        # 获取文件夹中的所有文件
        try:
            files = os.listdir(folder_path)
            video_files = []
            
            # 筛选视频文件（.mp4, .avi, .mov, .mkv等）
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
            for file in files:
                file_lower = file.lower()
                if any(file_lower.endswith(ext) for ext in video_extensions):
                    video_files.append(file)
            
            # 对视频文件排序
            video_files.sort()
            
            # 添加到结果列表
            for video_file in video_files:
                video_path = f"{folder_name}/{video_file}"
                video_list.append(video_path)
                
            print(f"  找到 {len(video_files)} 个视频文件")
            
        except PermissionError:
            print(f"  警告: 无权限访问文件夹 {folder_name}")
        except Exception as e:
            print(f"  错误: 扫描文件夹 {folder_name} 时出错: {e}")
    
    return video_list

def save_to_xlsx(video_list, output_file):
    """
    将视频列表保存为xlsx文件
    
    Args:
        video_list (list): 视频路径列表
        output_file (str): 输出文件路径
    """
    # 创建DataFrame
    df = pd.DataFrame({
        '视频路径': video_list
    })
    
    # 添加序号列
    df.insert(0, '序号', range(1, len(df) + 1))
    
    # 保存为xlsx文件
    try:
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"成功保存到文件: {output_file}")
        print(f"总共找到 {len(video_list)} 个视频文件")
    except Exception as e:
        print(f"保存文件时出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='扫描视频文件夹并生成xlsx文件')
    parser.add_argument('--input', '-i', 
                       default='.',
                       help='视频文件夹路径 (默认: 当前目录)')
    parser.add_argument('--output', '-o',
                       default='video_list.xlsx',
                       help='输出xlsx文件名 (默认: video_list.xlsx)')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("视频文件夹扫描工具")
    print("=" * 50)
    print(f"输入目录: {args.input}")
    print(f"输出文件: {args.output}")
    print("-" * 50)
    
    # 扫描视频文件夹
    video_list = scan_video_folders(args.input)
    
    if video_list:
        # 保存到xlsx文件
        save_to_xlsx(video_list, args.output)
        
        # 显示前几个示例
        print("\n前10个视频文件示例:")
        for i, video_path in enumerate(video_list[:10], 1):
            print(f"{i:2d}. {video_path}")
        
        if len(video_list) > 10:
            print(f"... 还有 {len(video_list) - 10} 个文件")
    else:
        print("未找到任何视频文件")
    
    print("\n扫描完成!")

if __name__ == "__main__":
    main()
