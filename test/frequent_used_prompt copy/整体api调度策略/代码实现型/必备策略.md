# API调度必备策略

## 🎯 一、令牌桶限流算法（核心必备）

### 1. 基础令牌桶实现
```python
import threading
import time
import random

class TokenBucketLimiter:
    """通用令牌桶限流器 - 所有API调度的基础"""
    
    def __init__(self, tokens_per_minute=60, bucket_capacity=None):
        self.tokens_per_minute = tokens_per_minute
        self.tokens_per_second = tokens_per_minute / 60.0
        self.bucket_capacity = bucket_capacity or int(tokens_per_minute * 1.5)
        
        self.lock = threading.Lock()
        self.current_tokens = self.bucket_capacity
        self.last_refill_time = time.time()
        
        # 统计信息
        self.total_requests = 0
        self.total_wait_time = 0
        self.rate_limit_hits = 0
    
    def _refill_tokens(self):
        """令牌补充算法"""
        now = time.time()
        time_passed = now - self.last_refill_time
        new_tokens = time_passed * self.tokens_per_second
        
        if new_tokens > 0:
            self.current_tokens = min(self.bucket_capacity, self.current_tokens + new_tokens)
            self.last_refill_time = now
    
    def get_token(self, timeout=None):
        """获取令牌"""
        with self.lock:
            self._refill_tokens()
            
            if self.current_tokens >= 1:
                self.current_tokens -= 1
                self.total_requests += 1
                return True
            
            # 计算等待时间
            wait_time = (1 - self.current_tokens) / self.tokens_per_second
            
            if timeout is not None and wait_time > timeout:
                return False
            
        # 释放锁后等待
        jitter = random.uniform(0, 0.1 * wait_time)
        time.sleep(wait_time + jitter)
        self.total_wait_time += wait_time + jitter
        
        return self.get_token(timeout)
    
    def wait_for_token(self):
        """等待直到获取到令牌"""
        while not self.get_token():
            time.sleep(0.1)
    
    def report_rate_limit_hit(self):
        """报告遇到限流"""
        with self.lock:
            self.rate_limit_hits += 1
    
    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            return {
                'total_requests': self.total_requests,
                'total_wait_time': self.total_wait_time,
                'rate_limit_hits': self.rate_limit_hits,
                'current_tokens': self.current_tokens,
                'avg_wait_time': self.total_wait_time / max(1, self.total_requests)
            }
```

### 2. 不同媒体类型的限流配置
```python
# 限流配置表
RATE_LIMIT_CONFIGS = {
    "text": {
        "tokens_per_minute": 60,
        "bucket_capacity": 90,
        "max_concurrent": 5
    },
    "image": {
        "tokens_per_minute": 20,
        "bucket_capacity": 30,
        "max_concurrent": 3
    },
    "video": {
        "tokens_per_minute": 10,
        "bucket_capacity": 15,
        "max_concurrent": 1
    }
}

def create_limiter(media_type):
    """根据媒体类型创建限流器"""
    config = RATE_LIMIT_CONFIGS.get(media_type, RATE_LIMIT_CONFIGS["text"])
    return TokenBucketLimiter(
        tokens_per_minute=config["tokens_per_minute"],
        bucket_capacity=config["bucket_capacity"]
    )
```

## 🔄 二、智能重试机制（核心必备）

### 1. 错误分类和重试策略
```python
import requests
from enum import Enum

class ErrorType(Enum):
    RATE_LIMIT = "rate_limit"
    SERVER_ERROR = "server_error"
    TIMEOUT = "timeout"
    CLIENT_ERROR = "client_error"
    NETWORK_ERROR = "network_error"
    UNKNOWN = "unknown"

class RetryStrategy:
    """智能重试策略"""
    
    # 不同错误类型的重试配置
    RETRY_CONFIGS = {
        ErrorType.RATE_LIMIT: {
            "max_retries": 5,
            "base_delay": 2.0,
            "max_delay": 120,
            "backoff_factor": 2.0
        },
        ErrorType.SERVER_ERROR: {
            "max_retries": 3,
            "base_delay": 5.0,
            "max_delay": 60,
            "backoff_factor": 2.0
        },
        ErrorType.TIMEOUT: {
            "max_retries": 3,
            "base_delay": 1.0,
            "max_delay": 30,
            "backoff_factor": 1.5
        },
        ErrorType.NETWORK_ERROR: {
            "max_retries": 4,
            "base_delay": 1.0,
            "max_delay": 30,
            "backoff_factor": 1.8
        },
        ErrorType.CLIENT_ERROR: {
            "max_retries": 1,
            "base_delay": 0.5,
            "max_delay": 5,
            "backoff_factor": 1.2
        }
    }
    
    @staticmethod
    def classify_error(exception, status_code=None):
        """分类错误类型"""
        if status_code == 429:
            return ErrorType.RATE_LIMIT
        elif status_code and 500 <= status_code < 600:
            return ErrorType.SERVER_ERROR
        elif status_code and 400 <= status_code < 500:
            return ErrorType.CLIENT_ERROR
        elif isinstance(exception, requests.exceptions.Timeout):
            return ErrorType.TIMEOUT
        elif isinstance(exception, (requests.exceptions.ConnectionError,
                                   requests.exceptions.NetworkTimeout)):
            return ErrorType.NETWORK_ERROR
        else:
            return ErrorType.UNKNOWN
    
    @staticmethod
    def calculate_delay(error_type, attempt, base_delay_override=None):
        """计算退避延迟时间"""
        config = RetryStrategy.RETRY_CONFIGS.get(error_type, 
                                               RetryStrategy.RETRY_CONFIGS[ErrorType.UNKNOWN])
        
        base_delay = base_delay_override or config["base_delay"]
        max_delay = config["max_delay"]
        backoff_factor = config["backoff_factor"]
        
        # 指数退避 + 随机抖动
        delay = min(max_delay, base_delay * (backoff_factor ** attempt))
        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter
    
    @staticmethod
    def should_retry(error_type, attempt):
        """判断是否应该重试"""
        config = RetryStrategy.RETRY_CONFIGS.get(error_type, 
                                               RetryStrategy.RETRY_CONFIGS[ErrorType.UNKNOWN])
        return attempt < config["max_retries"]

def smart_retry(func, *args, **kwargs):
    """智能重试装饰器"""
    attempt = 0
    last_error = None
    
    while True:
        try:
            return func(*args, **kwargs)
            
        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code
            error_type = RetryStrategy.classify_error(e, status_code)
            
            if not RetryStrategy.should_retry(error_type, attempt):
                raise
            
            # 处理Retry-After头
            base_delay_override = None
            if status_code == 429:
                retry_after = e.response.headers.get('Retry-After')
                if retry_after:
                    try:
                        base_delay_override = int(retry_after)
                    except ValueError:
                        pass
            
            delay = RetryStrategy.calculate_delay(error_type, attempt, base_delay_override)
            print(f"⚠️ {error_type.value} 错误，等待 {delay:.1f}s 后重试...")
            time.sleep(delay)
            
            attempt += 1
            last_error = e
            
        except Exception as e:
            error_type = RetryStrategy.classify_error(e)
            
            if not RetryStrategy.should_retry(error_type, attempt):
                raise
            
            delay = RetryStrategy.calculate_delay(error_type, attempt)
            print(f"⚠️ {error_type.value} 错误，等待 {delay:.1f}s 后重试...")
            time.sleep(delay)
            
            attempt += 1
            last_error = e
    
    raise last_error
```

## 📊 三、性能监控（核心必备）

### 1. 基础性能指标收集
```python
import time
from collections import defaultdict, deque
import threading

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size=100):
        self.window_size = window_size
        self.lock = threading.Lock()
        
        # 响应时间指标
        self.response_times = deque(maxlen=window_size)
        self.request_timestamps = deque(maxlen=window_size)
        
        # 成功率指标
        self.success_count = 0
        self.total_count = 0
        self.recent_results = deque(maxlen=window_size)
        
        # 错误统计
        self.error_counts = defaultdict(int)
        self.recent_errors = deque(maxlen=window_size)
        
        # 吞吐量指标
        self.completed_requests = deque(maxlen=window_size)
    
    def record_request_start(self):
        """记录请求开始"""
        return time.time()
    
    def record_request_complete(self, start_time, success=True, error_type=None):
        """记录请求完成"""
        with self.lock:
            end_time = time.time()
            response_time = end_time - start_time
            
            # 记录响应时间
            self.response_times.append(response_time)
            self.request_timestamps.append(end_time)
            
            # 记录成功率
            self.total_count += 1
            if success:
                self.success_count += 1
                self.recent_results.append(True)
            else:
                self.recent_results.append(False)
                if error_type:
                    self.error_counts[error_type] += 1
                    self.recent_errors.append((end_time, error_type))
            
            # 记录完成时间
            self.completed_requests.append(end_time)
    
    def get_metrics(self):
        """获取性能指标"""
        with self.lock:
            if not self.response_times:
                return {
                    'avg_response_time': 0,
                    'p95_response_time': 0,
                    'success_rate': 1.0,
                    'current_qps': 0,
                    'total_requests': 0,
                    'error_counts': {},
                    'error_rate': 0
                }
            
            # 计算响应时间指标
            response_times_list = list(self.response_times)
            avg_response_time = sum(response_times_list) / len(response_times_list)
            p95_response_time = sorted(response_times_list)[int(len(response_times_list) * 0.95)]
            
            # 计算成功率
            recent_success = sum(1 for r in self.recent_results if r)
            success_rate = recent_success / max(1, len(self.recent_results))
            
            # 计算QPS
            current_time = time.time()
            recent_requests = [t for t in self.completed_requests 
                             if current_time - t <= 60]
            current_qps = len(recent_requests) / 60.0
            
            # 计算错误率
            recent_error_count = len([t for t, _ in self.recent_errors 
                                    if current_time - t <= 300])
            recent_total = len([t for t in self.request_timestamps 
                              if current_time - t <= 300])
            error_rate = recent_error_count / max(1, recent_total)
            
            return {
                'avg_response_time': avg_response_time,
                'p95_response_time': p95_response_time,
                'success_rate': success_rate,
                'current_qps': current_qps,
                'total_requests': len(self.response_times),
                'error_counts': dict(self.error_counts),
                'error_rate': error_rate
            }
```

## 🔧 四、统一API调用接口（核心必备）

### 1. 通用API调用器
```python
class UniversalAPIClient:
    """通用API调用客户端"""
    
    def __init__(self, limiter, monitor):
        self.limiter = limiter
        self.monitor = monitor
        self.session = requests.Session()
    
    def call_api(self, url, headers, payload, timeout=300):
        """统一的API调用方法"""
        # 限流
        self.limiter.wait_for_token()
        
        # 开始监控
        start_time = self.monitor.record_request_start()
        
        try:
            # 执行请求
            response = smart_retry(
                self.session.post,
                url=url,
                headers=headers,
                json=payload,
                timeout=timeout
            )
            
            response.raise_for_status()
            
            # 记录成功
            self.monitor.record_request_complete(start_time, success=True)
            
            return response.json()
            
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                self.limiter.report_rate_limit_hit()
            
            error_type = RetryStrategy.classify_error(e, e.response.status_code).value
            self.monitor.record_request_complete(start_time, success=False, error_type=error_type)
            raise
            
        except Exception as e:
            error_type = RetryStrategy.classify_error(e).value
            self.monitor.record_request_complete(start_time, success=False, error_type=error_type)
            raise
    
    def get_performance_stats(self):
        """获取性能统计"""
        limiter_stats = self.limiter.get_stats()
        monitor_stats = self.monitor.get_metrics()
        
        return {
            'limiter': limiter_stats,
            'performance': monitor_stats
        }
```

## 📋 五、配置管理（核心必备）

### 1. 统一配置管理
```python
import os
from dataclasses import dataclass
from typing import Dict, Any

@dataclass
class APIConfig:
    """API配置数据类"""
    name: str
    url: str
    api_key: str
    default_model: str
    timeout: int = 300
    rate_limit: int = 60
    max_concurrent: int = 3

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.configs = {}
        self._load_default_configs()
    
    def _load_default_configs(self):
        """加载默认配置"""
        self.configs = {
            "siliconflow": APIConfig(
                name="SiliconFlow",
                url="https://api.siliconflow.cn/v1/chat/completions",
                api_key=os.environ.get("SILICONFLOW_API_TOKEN", ""),
                default_model="deepseek-ai/DeepSeek-V3",
                rate_limit=60
            ),
            "openrouter": APIConfig(
                name="OpenRouter",
                url="https://openrouter.ai/api/v1/chat/completions",
                api_key=os.environ.get("OPENROUTER_API_KEY", ""),
                default_model="google/gemini-2.5-pro",
                rate_limit=20
            ),
            "google": APIConfig(
                name="Google Gemini",
                url="",  # 使用SDK
                api_key=os.environ.get("GOOGLE_API_KEY", ""),
                default_model="models/gemini-2.5-pro",
                rate_limit=10
            )
        }
    
    def get_config(self, provider: str) -> APIConfig:
        """获取配置"""
        config = self.configs.get(provider)
        if not config:
            raise ValueError(f"未知的API提供商: {provider}")
        
        if not config.api_key:
            raise ValueError(f"{config.name} API密钥未配置")
        
        return config
    
    def validate_all_configs(self):
        """验证所有配置"""
        issues = []
        for provider, config in self.configs.items():
            if not config.api_key:
                issues.append(f"{provider}: API密钥未配置")
        
        return issues

# 全局配置管理器
config_manager = ConfigManager()
```

## 🎯 六、使用示例

### 完整的API调度示例
```python
def create_api_scheduler(media_type, provider):
    """创建API调度器"""
    # 获取配置
    config = config_manager.get_config(provider)
    
    # 创建限流器
    limiter = create_limiter(media_type)
    
    # 创建监控器
    monitor = PerformanceMonitor()
    
    # 创建API客户端
    client = UniversalAPIClient(limiter, monitor)
    
    return client, config

# 使用示例
def process_content(content, media_type="text", provider="siliconflow"):
    """处理内容的统一接口"""
    client, config = create_api_scheduler(media_type, provider)
    
    # 构建请求
    headers = {
        "Authorization": f"Bearer {config.api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": config.default_model,
        "messages": [{"role": "user", "content": content}]
    }
    
    # 调用API
    try:
        response = client.call_api(config.url, headers, payload, config.timeout)
        return {
            'success': True,
            'response': response,
            'stats': client.get_performance_stats()
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'stats': client.get_performance_stats()
        }

# 使用
result = process_content("你好，世界！", "text", "siliconflow")
if result['success']:
    print("调用成功:", result['response'])
else:
    print("调用失败:", result['error'])

print("性能统计:", result['stats'])
```

## 🎉 必备策略总结

这四个核心策略是所有API调度的基础：

1. **令牌桶限流** - 控制请求频率，避免触发API限制
2. **智能重试** - 根据错误类型采用不同的重试策略
3. **性能监控** - 实时跟踪API性能和健康状况
4. **统一接口** - 提供一致的API调用体验

这些策略适用于所有类型的API调度，是构建稳定可靠API服务的基石。

## 🔍 七、健康检查和熔断机制（必备扩展）

### 1. API健康检查
```python
class HealthChecker:
    """API健康检查器"""

    def __init__(self, failure_threshold=5, recovery_threshold=3, check_interval=60):
        self.failure_threshold = failure_threshold
        self.recovery_threshold = recovery_threshold
        self.check_interval = check_interval

        self.consecutive_failures = 0
        self.consecutive_successes = 0
        self.is_healthy = True
        self.last_check_time = 0

        self.lock = threading.Lock()

    def record_result(self, success):
        """记录API调用结果"""
        with self.lock:
            if success:
                self.consecutive_successes += 1
                self.consecutive_failures = 0

                # 恢复健康状态
                if (not self.is_healthy and
                    self.consecutive_successes >= self.recovery_threshold):
                    self.is_healthy = True
                    print("✅ API服务已恢复健康")
            else:
                self.consecutive_failures += 1
                self.consecutive_successes = 0

                # 标记为不健康
                if (self.is_healthy and
                    self.consecutive_failures >= self.failure_threshold):
                    self.is_healthy = False
                    print("❌ API服务标记为不健康")

    def is_service_healthy(self):
        """检查服务是否健康"""
        return self.is_healthy

    def get_health_status(self):
        """获取健康状态详情"""
        with self.lock:
            return {
                'is_healthy': self.is_healthy,
                'consecutive_failures': self.consecutive_failures,
                'consecutive_successes': self.consecutive_successes,
                'failure_threshold': self.failure_threshold,
                'recovery_threshold': self.recovery_threshold
            }

### 2. 熔断器模式
```python
from enum import Enum
import time

class CircuitState(Enum):
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态

class CircuitBreaker:
    """熔断器"""

    def __init__(self, failure_threshold=10, recovery_timeout=60, success_threshold=3):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold

        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED

        self.lock = threading.Lock()

    def call(self, func, *args, **kwargs):
        """通过熔断器调用函数"""
        with self.lock:
            if self.state == CircuitState.OPEN:
                # 检查是否可以进入半开状态
                if (self.last_failure_time and
                    time.time() - self.last_failure_time >= self.recovery_timeout):
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    print("🔄 熔断器进入半开状态")
                else:
                    raise Exception("熔断器开启，拒绝请求")

        try:
            result = func(*args, **kwargs)
            self._record_success()
            return result
        except Exception as e:
            self._record_failure()
            raise

    def _record_success(self):
        """记录成功"""
        with self.lock:
            self.failure_count = 0

            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.success_threshold:
                    self.state = CircuitState.CLOSED
                    print("✅ 熔断器关闭，服务恢复正常")

    def _record_failure(self):
        """记录失败"""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
                print(f"🚨 熔断器开启，失败次数: {self.failure_count}")

    def get_state(self):
        """获取熔断器状态"""
        return {
            'state': self.state.value,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'last_failure_time': self.last_failure_time
        }
```
