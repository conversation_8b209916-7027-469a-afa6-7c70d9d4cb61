# OpenRouter 图片API调度

## 🎯 一、基础配置

### API基本信息
- **服务商**: OpenRouter
- **API地址**: `https://openrouter.ai/api/v1/chat/completions`
- **认证方式**: Bear<PERSON>
- **主要模型**: `google/gemini-2.5-pro`, `openai/gpt-4-vision-preview`
- **特色**: 多模态处理、图片理解

### 配置参数
```python
OPENROUTER_CONFIG = {
    "api_url": "https://openrouter.ai/api/v1/chat/completions",
    "api_key": "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",
    "models": {
        "gemini_pro": "google/gemini-2.5-pro",
        "gpt4_vision": "openai/gpt-4-vision-preview",
        "claude_vision": "anthropic/claude-3-vision-beta"
    },
    "default_params": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "timeout": 300
    },
    "image_limits": {
        "max_size_mb": 10,
        "max_resolution": (2048, 2048),
        "supported_formats": ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    },
    "rate_limits": {
        "requests_per_minute": 15,
        "max_concurrent": 3
    }
}
```

## 🚀 二、核心实现

### 1. OpenRouter图片API类
```python
import requests
import base64
import os
from PIL import Image
import io

class OpenRouterImageAPI:
    def __init__(self, api_key=None, model=None):
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY", 
                                                OPENROUTER_CONFIG["api_key"])
        self.base_url = OPENROUTER_CONFIG["api_url"]
        self.default_model = model or OPENROUTER_CONFIG["models"]["gemini_pro"]
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def encode_image_to_base64(self, image_path):
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except FileNotFoundError:
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        except Exception as e:
            raise Exception(f"图片编码失败: {e}")
    
    def get_image_info(self, image_path):
        """获取图片基本信息"""
        try:
            with Image.open(image_path) as img:
                file_size = os.path.getsize(image_path)
                return {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height,
                    'file_size_mb': file_size / (1024 * 1024)
                }
        except Exception as e:
            return {'error': str(e)}
    
    def validate_image(self, image_path):
        """验证图片是否符合要求"""
        if not os.path.exists(image_path):
            return False, "图片文件不存在"
        
        # 检查文件扩展名
        ext = os.path.splitext(image_path)[1].lower()
        if ext not in OPENROUTER_CONFIG["image_limits"]["supported_formats"]:
            return False, f"不支持的图片格式: {ext}"
        
        # 检查文件大小
        file_size_mb = os.path.getsize(image_path) / (1024 * 1024)
        max_size = OPENROUTER_CONFIG["image_limits"]["max_size_mb"]
        if file_size_mb > max_size:
            return False, f"图片文件过大: {file_size_mb:.2f}MB > {max_size}MB"
        
        return True, "图片文件符合要求"
    
    def process_image(self, image_path, prompt_text, model=None, **kwargs):
        """处理单张图片"""
        # 验证图片
        is_valid, message = self.validate_image(image_path)
        if not is_valid:
            raise ValueError(message)
        
        # 编码图片
        base64_image = self.encode_image_to_base64(image_path)
        
        # 检测图片格式
        image_format = self._detect_image_format(image_path)
        data_url = f"data:image/{image_format};base64,{base64_image}"
        
        # 构建请求
        model = model or self.default_model
        params = OPENROUTER_CONFIG["default_params"].copy()
        params.update(kwargs)
        
        payload = {
            "model": model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt_text},
                    {"type": "image_url", "image_url": {"url": data_url}}
                ]
            }],
            **params
        }
        
        response = self.session.post(
            self.base_url,
            json=payload,
            timeout=params.get("timeout", 300)
        )
        response.raise_for_status()
        return response.json()
    
    def _detect_image_format(self, image_path):
        """检测图片格式"""
        ext = os.path.splitext(image_path)[1].lower()
        format_map = {
            '.jpg': 'jpeg', '.jpeg': 'jpeg',
            '.png': 'png', '.gif': 'gif',
            '.bmp': 'bmp', '.webp': 'webp'
        }
        return format_map.get(ext, 'jpeg')
    
    def extract_content(self, response):
        """提取响应内容"""
        try:
            return response["choices"][0]["message"]["content"].strip()
        except (KeyError, IndexError):
            return None
    
    def get_usage_info(self, response):
        """获取使用信息"""
        return response.get("usage", {})
```

### 2. 图片预处理工具
```python
from PIL import Image, ImageOps
import io

class ImagePreprocessor:
    @staticmethod
    def resize_image(image_path, max_size=(1024, 1024), quality=85):
        """调整图片大小并压缩"""
        with Image.open(image_path) as img:
            # 转换为RGB
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 保持宽高比缩放
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 保存到内存
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            output.seek(0)
            
            return output.getvalue()
    
    @staticmethod
    def auto_orient_image(image_path):
        """自动旋转图片（基于EXIF信息）"""
        try:
            with Image.open(image_path) as img:
                # 应用EXIF旋转信息
                img = ImageOps.exif_transpose(img)
                
                # 保存处理后的图片
                output = io.BytesIO()
                img.save(output, format='JPEG', quality=90)
                output.seek(0)
                
                return output.getvalue()
        except Exception as e:
            print(f"图片旋转失败: {e}")
            return None
    
    @staticmethod
    def create_thumbnail(image_path, size=(256, 256)):
        """创建缩略图"""
        with Image.open(image_path) as img:
            img.thumbnail(size, Image.Resampling.LANCZOS)
            
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=80)
            output.seek(0)
            
            return output.getvalue()
    
    @staticmethod
    def compress_image(image_path, target_size_mb=5):
        """压缩图片到指定大小"""
        with Image.open(image_path) as img:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 二分查找最佳质量参数
            min_quality, max_quality = 10, 95
            best_quality = max_quality
            
            while min_quality <= max_quality:
                mid_quality = (min_quality + max_quality) // 2
                
                output = io.BytesIO()
                img.save(output, format='JPEG', quality=mid_quality, optimize=True)
                size_mb = len(output.getvalue()) / (1024 * 1024)
                
                if size_mb <= target_size_mb:
                    best_quality = mid_quality
                    min_quality = mid_quality + 1
                else:
                    max_quality = mid_quality - 1
            
            # 使用最佳质量重新压缩
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=best_quality, optimize=True)
            output.seek(0)
            
            return output.getvalue()
```

### 3. 专用功能方法
```python
class OpenRouterSpecializedAPI(OpenRouterImageAPI):
    def describe_image(self, image_path, detail_level="detailed"):
        """图片描述生成"""
        prompts = {
            "brief": "简要描述这张图片的内容",
            "detailed": "详细描述这张图片中的所有元素、场景、人物、物体和活动",
            "artistic": "从艺术角度分析这张图片的构图、色彩、风格和表现手法"
        }
        
        prompt = prompts.get(detail_level, prompts["detailed"])
        return self.process_image(image_path, prompt)
    
    def extract_text_from_image(self, image_path):
        """OCR文字识别"""
        prompt = "请识别并提取图片中的所有文字内容，保持原有的格式和布局"
        return self.process_image(image_path, prompt)
    
    def analyze_image_content(self, image_path, analysis_type="objects"):
        """图片内容分析"""
        prompts = {
            "objects": "识别图片中的所有物体和对象",
            "people": "描述图片中的人物，包括数量、外观、动作和表情",
            "scene": "分析图片的场景和环境",
            "colors": "分析图片的主要颜色和色彩搭配",
            "mood": "分析图片传达的情感和氛围"
        }
        
        prompt = prompts.get(analysis_type, prompts["objects"])
        return self.process_image(image_path, prompt)
    
    def generate_image_caption(self, image_path, style="descriptive"):
        """生成图片标题"""
        prompts = {
            "descriptive": "为这张图片生成一个描述性的标题",
            "creative": "为这张图片创作一个富有创意的标题",
            "seo": "为这张图片生成一个适合SEO的标题和alt文本",
            "social": "为这张图片生成适合社交媒体的吸引人标题"
        }
        
        prompt = prompts.get(style, prompts["descriptive"])
        return self.process_image(image_path, prompt)
    
    def compare_images(self, image_path1, image_path2):
        """比较两张图片"""
        # 注意：这需要模型支持多图片输入
        prompt = "比较这两张图片的相似点和不同点"
        
        # 编码两张图片
        base64_image1 = self.encode_image_to_base64(image_path1)
        base64_image2 = self.encode_image_to_base64(image_path2)
        
        format1 = self._detect_image_format(image_path1)
        format2 = self._detect_image_format(image_path2)
        
        payload = {
            "model": self.default_model,
            "messages": [{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/{format1};base64,{base64_image1}"}},
                    {"type": "image_url", "image_url": {"url": f"data:image/{format2};base64,{base64_image2}"}}
                ]
            }]
        }
        
        response = self.session.post(self.base_url, json=payload, timeout=300)
        response.raise_for_status()
        return response.json()
    
    def answer_image_question(self, image_path, question):
        """图片问答"""
        prompt = f"请根据图片回答以下问题：{question}"
        return self.process_image(image_path, prompt)
    
    def generate_image_tags(self, image_path, max_tags=10):
        """生成图片标签"""
        prompt = f"为这张图片生成最多{max_tags}个相关标签，用逗号分隔"
        return self.process_image(image_path, prompt)
```

## 📊 三、批量处理

### 批量图片处理器
```python
import concurrent.futures
from tqdm import tqdm
import glob

class OpenRouterBatchProcessor:
    def __init__(self, api_instance, max_workers=2):  # 图片处理并发数较低
        self.api = api_instance
        self.max_workers = max_workers
        self.preprocessor = ImagePreprocessor()
    
    def process_single_image(self, image_path, prompt, preprocess=True):
        """处理单张图片"""
        try:
            # 获取图片信息
            image_info = self.api.get_image_info(image_path)
            if 'error' in image_info:
                raise Exception(f"无法读取图片: {image_info['error']}")
            
            # 预处理（如果需要）
            processed_path = image_path
            if preprocess:
                size_mb = image_info.get('file_size_mb', 0)
                max_size = OPENROUTER_CONFIG["image_limits"]["max_size_mb"]
                
                if size_mb > max_size:
                    print(f"🔄 压缩图片: {os.path.basename(image_path)}")
                    compressed_data = self.preprocessor.compress_image(image_path, max_size * 0.8)
                    
                    # 保存临时文件
                    temp_path = f"{image_path}_temp.jpg"
                    with open(temp_path, 'wb') as f:
                        f.write(compressed_data)
                    processed_path = temp_path
            
            # 调用API
            response = self.api.process_image(processed_path, prompt)
            
            # 清理临时文件
            if processed_path != image_path:
                try:
                    os.remove(processed_path)
                except:
                    pass
            
            return {
                'image_path': image_path,
                'image_info': image_info,
                'response': response,
                'content': self.api.extract_content(response),
                'success': True
            }
            
        except Exception as e:
            return {
                'image_path': image_path,
                'error': str(e),
                'success': False
            }
    
    def process_directory(self, directory_path, prompt, image_extensions=None):
        """处理目录中的所有图片"""
        if image_extensions is None:
            image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif', '*.webp']
        
        # 收集所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(directory_path, ext)))
            image_files.extend(glob.glob(os.path.join(directory_path, ext.upper())))
        
        print(f"📁 找到 {len(image_files)} 张图片")
        
        return self.process_batch(image_files, prompt)
    
    def process_batch(self, image_paths, prompt):
        """批量处理图片"""
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_image = {
                executor.submit(self.process_single_image, image_path, prompt): image_path
                for image_path in image_paths
            }
            
            # 处理完成的任务
            for future in tqdm(concurrent.futures.as_completed(future_to_image),
                             total=len(future_to_image), desc="处理图片"):
                image_path = future_to_image[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    if result['success']:
                        print(f"✅ {os.path.basename(image_path)} 处理成功")
                    else:
                        print(f"❌ {os.path.basename(image_path)} 处理失败: {result['error']}")
                        
                except Exception as e:
                    print(f"❌ {os.path.basename(image_path)} 处理异常: {e}")
                    results.append({
                        'image_path': image_path,
                        'error': str(e),
                        'success': False
                    })
        
        return results
```

## 🔧 四、使用示例

### 基础使用
```python
# 初始化API
api = OpenRouterImageAPI()
specialized_api = OpenRouterSpecializedAPI()

# 图片描述
image_path = "/path/to/your/image.jpg"
description = specialized_api.describe_image(image_path, "detailed")
print("图片描述:", api.extract_content(description))

# OCR文字识别
text_result = specialized_api.extract_text_from_image(image_path)
print("识别的文字:", api.extract_content(text_result))

# 图片问答
qa_result = specialized_api.answer_image_question(
    image_path, 
    "这张图片中有多少个人？他们在做什么？"
)
print("问答结果:", api.extract_content(qa_result))
```

### 批量处理
```python
# 批量处理
processor = OpenRouterBatchProcessor(specialized_api, max_workers=2)

# 处理目录中的所有图片
results = processor.process_directory(
    "/path/to/images/",
    "为这张图片生成一个吸引人的标题"
)

# 统计结果
successful = sum(1 for r in results if r['success'])
print(f"处理完成: {successful}/{len(results)} 成功")

# 保存结果
import json
with open("image_analysis_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
```

## 📋 五、最佳实践

### 1. 图片优化策略
- 压缩大图片到10MB以下
- 使用JPEG格式以获得更好的压缩比
- 保持合理的分辨率(1024x1024以下)
- 自动旋转图片以正确显示

### 2. 提示词优化
- 使用具体、清晰的描述
- 指定期望的输出格式
- 提供上下文信息
- 避免模糊的指令

### 3. 错误处理
- 验证图片格式和大小
- 处理网络超时
- 实现重试机制
- 记录失败原因

### 4. 性能优化
- 使用适当的并发数(2-3)
- 预处理图片以减少传输时间
- 缓存处理结果
- 监控API使用量

OpenRouter图片API提供了强大的多模态处理能力，适合各种图片理解和分析任务。
