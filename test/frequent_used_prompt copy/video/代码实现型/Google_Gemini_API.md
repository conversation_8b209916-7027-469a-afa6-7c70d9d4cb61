# Google Gemini 视频API调度

## 🎯 一、基础配置

### API基本信息
- **服务商**: Google
- **SDK**: `google-genai`
- **认证方式**: API Key
- **主要模型**: `models/gemini-2.5-pro`
- **特色**: 视频理解、多模态处理

### 配置参数
```python
GEMINI_CONFIG = {
    "api_key": "AIzaSyBmnec3Q3xUXAxdQgTWW1Zb2Hc8X7EAFw0",
    "models": {
        "pro": "models/gemini-2.5-pro",
        "flash": "models/gemini-2.5-flash"
    },
    "video_limits": {
        "max_file_size_mb": 20,
        "max_duration_minutes": 10,
        "supported_formats": ['.mp4', '.avi', '.mov', '.mkv', '.webm']
    },
    "rate_limits": {
        "requests_per_minute": 8,
        "max_concurrent": 1
    },
    "retry_config": {
        "max_retries": 3,
        "base_delay": 12,
        "max_delay": 300
    }
}
```

## 🚀 二、核心实现

### 1. Gemini视频API类
```python
import time
import os
import json
import subprocess
from google import genai
from google.genai import types
from google.genai.errors import ClientError

class GeminiVideoAPI:
    def __init__(self, api_key=None, model=None):
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY", 
                                                GEMINI_CONFIG["api_key"])
        self.client = genai.Client(api_key=self.api_key)
        self.default_model = model or GEMINI_CONFIG["models"]["pro"]
        
        # 视频处理限制
        self.max_file_size_mb = GEMINI_CONFIG["video_limits"]["max_file_size_mb"]
        self.supported_formats = set(GEMINI_CONFIG["video_limits"]["supported_formats"])
    
    def get_video_info(self, video_path):
        """获取视频基本信息"""
        try:
            # 使用ffprobe获取详细信息
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                info = json.loads(result.stdout)
                
                # 提取关键信息
                format_info = info.get('format', {})
                video_stream = next((s for s in info.get('streams', []) 
                                   if s.get('codec_type') == 'video'), {})
                
                duration = float(format_info.get('duration', 0))
                file_size = float(format_info.get('size', 0))
                
                return {
                    'duration_seconds': duration,
                    'duration_minutes': duration / 60,
                    'size_bytes': int(file_size),
                    'size_mb': file_size / (1024 * 1024),
                    'width': int(video_stream.get('width', 0)),
                    'height': int(video_stream.get('height', 0)),
                    'fps': self._parse_fps(video_stream.get('r_frame_rate', '0/1')),
                    'codec': video_stream.get('codec_name', 'unknown'),
                    'bitrate': int(format_info.get('bit_rate', 0)),
                    'format': format_info.get('format_name', 'unknown')
                }
            else:
                # 如果ffprobe不可用，使用基本文件信息
                file_size = os.path.getsize(video_path)
                return {
                    'size_bytes': file_size,
                    'size_mb': file_size / (1024 * 1024),
                    'duration_seconds': 0,
                    'duration_minutes': 0,
                    'width': 0,
                    'height': 0,
                    'fps': 0,
                    'codec': 'unknown',
                    'bitrate': 0,
                    'format': 'unknown'
                }
        except Exception as e:
            print(f"⚠️ 获取视频信息失败: {e}")
            return {'error': str(e)}
    
    def _parse_fps(self, fps_str):
        """解析帧率字符串"""
        try:
            if '/' in fps_str:
                num, den = fps_str.split('/')
                return float(num) / float(den) if float(den) != 0 else 0
            return float(fps_str)
        except:
            return 0
    
    def validate_video(self, video_path):
        """验证视频是否符合API要求"""
        if not os.path.exists(video_path):
            return False, "视频文件不存在"
        
        # 检查文件扩展名
        ext = os.path.splitext(video_path)[1].lower()
        if ext not in self.supported_formats:
            return False, f"不支持的视频格式: {ext}"
        
        # 检查文件大小
        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
        if file_size_mb > self.max_file_size_mb:
            return False, f"视频文件过大: {file_size_mb:.2f}MB > {self.max_file_size_mb}MB"
        
        # 检查时长（如果可以获取）
        video_info = self.get_video_info(video_path)
        if 'error' not in video_info:
            duration_minutes = video_info.get('duration_minutes', 0)
            max_duration = GEMINI_CONFIG["video_limits"]["max_duration_minutes"]
            if duration_minutes > max_duration:
                return False, f"视频时长过长: {duration_minutes:.1f}分钟 > {max_duration}分钟"
        
        return True, "视频文件符合要求"
    
    def process_video(self, video_path, prompt="简要总结这个视频的内容。", model=None):
        """处理单个视频文件"""
        # 验证视频
        is_valid, message = self.validate_video(video_path)
        if not is_valid:
            raise ValueError(message)
        
        # 获取视频信息
        video_info = self.get_video_info(video_path)
        print(f"📹 处理视频: {os.path.basename(video_path)}")
        if 'error' not in video_info:
            print(f"   大小: {video_info.get('size_mb', 0):.2f}MB")
            print(f"   时长: {video_info.get('duration_minutes', 0):.1f}分钟")
            print(f"   分辨率: {video_info.get('width', 0)}x{video_info.get('height', 0)}")
        
        # 读取视频文件
        with open(video_path, 'rb') as f:
            video_bytes = f.read()
        
        # 构建请求
        model = model or self.default_model
        response = self.client.models.generate_content(
            model=model,
            contents=types.Content(
                parts=[
                    types.Part(
                        inline_data=types.Blob(data=video_bytes, mime_type='video/mp4')
                    ),
                    types.Part(text=prompt)
                ]
            )
        )
        
        return {
            'video_path': video_path,
            'video_info': video_info,
            'prompt': prompt,
            'response': response.text,
            'model': model,
            'success': True
        }
    
    def process_video_with_retry(self, video_path, prompt="简要总结这个视频的内容。", 
                                max_retries=None, base_delay=None):
        """带重试机制的视频处理"""
        max_retries = max_retries or GEMINI_CONFIG["retry_config"]["max_retries"]
        base_delay = base_delay or GEMINI_CONFIG["retry_config"]["base_delay"]
        
        last_error = None
        
        for attempt in range(max_retries):
            try:
                print(f"🎬 尝试第 {attempt + 1} 次处理视频...")
                return self.process_video(video_path, prompt)
                
            except ClientError as e:
                if e.status_code == 429:  # RESOURCE_EXHAUSTED
                    retry_delay = base_delay * (2 ** attempt)
                    max_delay = GEMINI_CONFIG["retry_config"]["max_delay"]
                    retry_delay = min(retry_delay, max_delay)
                    
                    print(f"⚠️ 配额限制错误，等待 {retry_delay}s 后重试...")
                    
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        print("❌ 已达到最大重试次数，配额可能已耗尽")
                        raise Exception("视频API配额耗尽")
                
                elif e.status_code == 400:
                    print(f"❌ 视频格式或内容问题: {e}")
                    raise Exception(f"视频处理失败: {e}")
                
                else:
                    print(f"❌ 视频API错误 (状态码: {e.status_code}): {e}")
                    raise Exception(f"视频API调用失败: {e}")
                
                last_error = e
                
            except Exception as e:
                if "timeout" in str(e).lower():
                    wait_time = base_delay * (1.5 ** attempt)
                    print(f"⚠️ 视频处理超时，等待 {wait_time:.1f}s...")
                    time.sleep(wait_time)
                    last_error = e
                else:
                    print(f"❌ 视频处理异常: {e}")
                    raise
        
        raise last_error or Exception(f"视频API调用失败，已重试 {max_retries} 次")
```

### 2. 视频预处理工具
```python
import subprocess
import tempfile

class VideoPreprocessor:
    @staticmethod
    def compress_video(input_path, output_path=None, target_size_mb=15):
        """压缩视频到指定大小"""
        if output_path is None:
            name, ext = os.path.splitext(input_path)
            output_path = f"{name}_compressed{ext}"
        
        try:
            # 获取原视频信息
            info_cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', input_path
            ]
            result = subprocess.run(info_cmd, capture_output=True, text=True)
            info = json.loads(result.stdout)
            
            duration = float(info['format']['duration'])
            original_size = float(info['format']['size'])
            
            # 计算目标比特率
            target_bitrate = int((target_size_mb * 8 * 1024 * 1024) / duration * 0.9)
            
            # 压缩命令
            compress_cmd = [
                'ffmpeg', '-i', input_path,
                '-c:v', 'libx264',
                '-b:v', f'{target_bitrate}',
                '-c:a', 'aac',
                '-b:a', '128k',
                '-movflags', '+faststart',  # 优化流媒体播放
                '-y',
                output_path
            ]
            
            print(f"🔄 压缩视频: 目标比特率 {target_bitrate} bps")
            result = subprocess.run(compress_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                new_size = os.path.getsize(output_path) / (1024 * 1024)
                original_size_mb = original_size / (1024 * 1024)
                print(f"✅ 压缩完成: {original_size_mb:.2f}MB -> {new_size:.2f}MB")
                return output_path
            else:
                print(f"❌ 压缩失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 压缩异常: {e}")
            return None
    
    @staticmethod
    def extract_frames(video_path, output_dir, fps=1, max_frames=10):
        """提取视频关键帧"""
        os.makedirs(output_dir, exist_ok=True)
        
        cmd = [
            'ffmpeg', '-i', video_path,
            '-vf', f'fps={fps}',
            '-frames:v', str(max_frames),
            '-y',
            os.path.join(output_dir, 'frame_%04d.jpg')
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                frames = [f for f in os.listdir(output_dir) if f.startswith('frame_')]
                print(f"✅ 提取了 {len(frames)} 帧")
                return [os.path.join(output_dir, f) for f in sorted(frames)]
            else:
                print(f"❌ 帧提取失败: {result.stderr}")
                return []
        except Exception as e:
            print(f"❌ 帧提取异常: {e}")
            return []
    
    @staticmethod
    def get_video_thumbnail(video_path, time_offset="00:00:01"):
        """获取视频缩略图"""
        output_path = os.path.splitext(video_path)[0] + "_thumbnail.jpg"
        
        cmd = [
            'ffmpeg', '-i', video_path,
            '-ss', time_offset,
            '-vframes', '1',
            '-q:v', '2',  # 高质量
            '-y',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                return output_path
            else:
                print(f"❌ 缩略图生成失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"❌ 缩略图生成异常: {e}")
            return None
    
    @staticmethod
    def convert_to_mp4(input_path, output_path=None):
        """转换视频格式为MP4"""
        if output_path is None:
            name = os.path.splitext(input_path)[0]
            output_path = f"{name}_converted.mp4"
        
        cmd = [
            'ffmpeg', '-i', input_path,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-movflags', '+faststart',
            '-y',
            output_path
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ 格式转换完成: {output_path}")
                return output_path
            else:
                print(f"❌ 格式转换失败: {result.stderr}")
                return None
        except Exception as e:
            print(f"❌ 格式转换异常: {e}")
            return None
```

### 3. 专用功能方法
```python
class GeminiSpecializedAPI(GeminiVideoAPI):
    def summarize_video(self, video_path, length="brief"):
        """视频摘要生成"""
        prompts = {
            "brief": "用1-2句话简要总结这个视频的主要内容",
            "detailed": "详细总结这个视频的内容，包括主要场景、人物、活动和关键信息",
            "structured": """
请按以下结构总结视频内容：
1. 主题概述
2. 主要场景
3. 关键人物
4. 重要事件
5. 结论要点
"""
        }
        
        prompt = prompts.get(length, prompts["brief"])
        return self.process_video_with_retry(video_path, prompt)
    
    def analyze_video_content(self, video_path, analysis_type="general"):
        """视频内容分析"""
        prompts = {
            "general": "分析这个视频的整体内容和主题",
            "objects": "识别视频中出现的主要物体和对象",
            "people": "描述视频中的人物，包括数量、外观、动作和互动",
            "activities": "详细描述视频中发生的所有活动和事件",
            "emotions": "分析视频中表达的情感和氛围",
            "technical": "从技术角度分析视频的拍摄手法、构图和制作质量"
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        return self.process_video_with_retry(video_path, prompt)
    
    def extract_video_transcript(self, video_path):
        """提取视频中的语音内容"""
        prompt = """
如果这个视频中有语音或对话，请尽可能准确地转录出来。
如果没有语音，请说明"视频中没有检测到语音内容"。
请按时间顺序整理转录内容。
"""
        return self.process_video_with_retry(video_path, prompt)
    
    def answer_video_question(self, video_path, question):
        """视频问答"""
        prompt = f"请根据视频内容回答以下问题：{question}"
        return self.process_video_with_retry(video_path, prompt)
    
    def generate_video_tags(self, video_path, max_tags=10):
        """生成视频标签"""
        prompt = f"为这个视频生成最多{max_tags}个相关标签，用逗号分隔"
        return self.process_video_with_retry(video_path, prompt)
    
    def create_video_chapters(self, video_path):
        """创建视频章节"""
        prompt = """
分析这个视频的内容结构，创建章节划分。
请提供：
1. 章节标题
2. 大致的时间段
3. 每个章节的主要内容
"""
        return self.process_video_with_retry(video_path, prompt)
    
    def assess_video_quality(self, video_path):
        """评估视频质量"""
        prompt = """
从以下方面评估这个视频的质量：
1. 画面清晰度
2. 音频质量
3. 拍摄稳定性
4. 光线条件
5. 整体制作水平
请给出具体的评价和改进建议。
"""
        return self.process_video_with_retry(video_path, prompt)
```

## 📊 三、批量处理

### 批量视频处理器
```python
import glob
from tqdm import tqdm

class GeminiBatchProcessor:
    def __init__(self, api_instance, max_workers=1):  # 视频处理通常单线程
        self.api = api_instance
        self.max_workers = max_workers
        self.preprocessor = VideoPreprocessor()
    
    def process_single_video(self, video_path, prompt, auto_compress=True):
        """处理单个视频"""
        start_time = time.time()
        
        try:
            # 获取视频信息
            video_info = self.api.get_video_info(video_path)
            if 'error' in video_info:
                raise Exception(f"无法读取视频: {video_info['error']}")
            
            # 检查是否需要压缩
            processed_video_path = video_path
            if auto_compress and video_info.get('size_mb', 0) > 15:
                print(f"🔄 视频文件较大({video_info['size_mb']:.2f}MB)，开始压缩...")
                compressed_path = self.preprocessor.compress_video(video_path)
                if compressed_path:
                    processed_video_path = compressed_path
                    # 更新视频信息
                    video_info = self.api.get_video_info(processed_video_path)
            
            # 处理视频
            result = self.api.process_video_with_retry(processed_video_path, prompt)
            
            # 清理临时文件
            if processed_video_path != video_path:
                try:
                    os.remove(processed_video_path)
                except:
                    pass
            
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                'video_path': video_path,
                'error': str(e),
                'processing_time': processing_time,
                'success': False
            }
    
    def process_directory(self, directory_path, prompt, video_extensions=None):
        """处理目录中的所有视频"""
        if video_extensions is None:
            video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.webm']
        
        # 收集所有视频文件
        video_files = []
        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(directory_path, ext)))
            video_files.extend(glob.glob(os.path.join(directory_path, ext.upper())))
        
        print(f"📁 找到 {len(video_files)} 个视频文件")
        
        return self.process_batch(video_files, prompt)
    
    def process_batch(self, video_paths, prompt):
        """批量处理视频（单线程处理）"""
        results = []
        
        # 视频处理通常使用单线程，避免资源竞争
        for i, video_path in enumerate(video_paths):
            print(f"\n📹 处理视频 {i+1}/{len(video_paths)}: {os.path.basename(video_path)}")
            
            try:
                result = self.process_single_video(video_path, prompt)
                results.append(result)
                
                if result['success']:
                    print(f"✅ 处理成功，用时 {result['processing_time']:.1f}s")
                else:
                    print(f"❌ 处理失败: {result['error']}")
                
                # 视频处理间隔更长，避免配额问题
                if i < len(video_paths) - 1:  # 不是最后一个
                    time.sleep(5)
                
            except Exception as e:
                print(f"❌ 处理异常: {e}")
                results.append({
                    'video_path': video_path,
                    'error': str(e),
                    'success': False
                })
        
        return results
```

## 🔧 四、使用示例

### 基础使用
```python
# 初始化API
api = GeminiVideoAPI()
specialized_api = GeminiSpecializedAPI()

# 视频摘要
video_path = "/path/to/your/video.mp4"
summary = specialized_api.summarize_video(video_path, "detailed")
print("视频摘要:", summary['response'])

# 视频问答
qa_result = specialized_api.answer_video_question(
    video_path,
    "这个视频的主要内容是什么？有哪些关键信息？"
)
print("问答结果:", qa_result['response'])

# 提取转录
transcript = specialized_api.extract_video_transcript(video_path)
print("语音转录:", transcript['response'])
```

### 批量处理
```python
# 批量处理
processor = GeminiBatchProcessor(specialized_api, max_workers=1)

# 处理目录中的所有视频
results = processor.process_directory(
    "/path/to/videos/",
    "为这个视频生成详细的内容摘要"
)

# 统计结果
successful = sum(1 for r in results if r['success'])
total_time = sum(r.get('processing_time', 0) for r in results)

print(f"\n批量处理完成:")
print(f"成功: {successful}/{len(results)}")
print(f"总用时: {total_time:.1f}s")
print(f"平均用时: {total_time/len(results):.1f}s/视频")

# 保存结果
import json
with open("video_analysis_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
```

## 📋 五、最佳实践

### 1. 视频优化策略
- 压缩大视频到20MB以下
- 转换为MP4格式以获得最佳兼容性
- 控制视频时长在10分钟以内
- 使用合适的分辨率和比特率

### 2. 配额管理
- 严格控制请求频率(8次/分钟)
- 单线程处理避免并发冲突
- 实现长时间重试机制
- 监控API使用量

### 3. 错误处理
- 验证视频格式和大小
- 处理配额耗尽情况
- 实现智能重试
- 记录处理失败的原因

### 4. 性能优化
- 预处理视频以减少传输时间
- 缓存处理结果
- 使用合适的提示词
- 避免重复处理相同视频

Google Gemini视频API提供了强大的视频理解能力，但需要谨慎管理配额和处理频率。
