# DeepSeek 文本API调度

## 🎯 一、基础配置

### API基本信息
- **服务商**: DeepSeek
- **API地址**: `https://api.deepseek.com/v1/chat/completions`
- **认证方式**: Bearer Token
- **主要模型**: `deepseek-chat`, `deepseek-coder`
- **特色**: 代码生成、推理能力强

### 配置参数
```python
DEEPSEEK_CONFIG = {
    "api_url": "https://api.deepseek.com/v1/chat/completions",
    "api_key": "your_deepseek_api_key",
    "models": {
        "chat": "deepseek-chat",
        "coder": "deepseek-coder",
        "reasoner": "deepseek-reasoner"
    },
    "default_params": {
        "temperature": 0.7,
        "max_tokens": 4096,
        "timeout": 300
    },
    "rate_limits": {
        "requests_per_minute": 60,
        "max_concurrent": 10
    }
}
```

## 🚀 二、核心实现

### 1. DeepSeek API类
```python
import requests
import os
import time
import json

class DeepSeekAPI:
    def __init__(self, api_key=None, model=None):
        self.api_key = api_key or os.environ.get("DEEPSEEK_API_KEY", 
                                                DEEPSEEK_CONFIG["api_key"])
        self.base_url = DEEPSEEK_CONFIG["api_url"]
        self.default_model = model or DEEPSEEK_CONFIG["models"]["chat"]
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def call_api(self, messages, model=None, **kwargs):
        """标准API调用"""
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        
        model = model or self.default_model
        params = DEEPSEEK_CONFIG["default_params"].copy()
        params.update(kwargs)
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": False,
            **params
        }
        
        response = self.session.post(
            self.base_url,
            json=payload,
            timeout=params.get("timeout", 300)
        )
        response.raise_for_status()
        return response.json()
    
    def extract_content(self, response):
        """提取响应内容"""
        try:
            return response["choices"][0]["message"]["content"].strip()
        except (KeyError, IndexError):
            return None
    
    def get_usage_info(self, response):
        """获取使用信息"""
        return response.get("usage", {})
```

### 2. DeepSeek专用功能
```python
class DeepSeekSpecializedAPI(DeepSeekAPI):
    def generate_code(self, description, language="python"):
        """代码生成"""
        prompt = f"请用{language}编写代码实现以下功能：\n{description}\n\n请提供完整的、可运行的代码："
        
        return self.call_api(
            prompt,
            model=DEEPSEEK_CONFIG["models"]["coder"],
            temperature=0.2
        )
    
    def explain_code(self, code, language="python"):
        """代码解释"""
        prompt = f"请详细解释以下{language}代码的功能和实现原理：\n\n```{language}\n{code}\n```"
        
        return self.call_api(prompt, temperature=0.3)
    
    def debug_code(self, code, error_message=""):
        """代码调试"""
        prompt = f"""
请帮助调试以下代码：

代码：
```
{code}
```

错误信息：
{error_message}

请分析问题并提供修复建议：
"""
        return self.call_api(
            prompt,
            model=DEEPSEEK_CONFIG["models"]["coder"],
            temperature=0.1
        )
    
    def optimize_code(self, code, language="python"):
        """代码优化"""
        prompt = f"""
请优化以下{language}代码，提高性能和可读性：

```{language}
{code}
```

请提供优化后的代码和改进说明：
"""
        return self.call_api(
            prompt,
            model=DEEPSEEK_CONFIG["models"]["coder"],
            temperature=0.3
        )
    
    def reasoning_task(self, problem):
        """推理任务"""
        prompt = f"""
请仔细分析并解决以下问题，展示你的推理过程：

问题：{problem}

请按以下格式回答：
1. 问题分析
2. 推理步骤
3. 最终答案
"""
        return self.call_api(
            prompt,
            model=DEEPSEEK_CONFIG["models"]["reasoner"],
            temperature=0.1
        )
    
    def math_problem(self, problem):
        """数学问题求解"""
        prompt = f"""
请解决以下数学问题，显示详细的解题步骤：

{problem}

请提供：
1. 解题思路
2. 详细步骤
3. 最终答案
"""
        return self.call_api(prompt, temperature=0.1)
    
    def code_review(self, code, language="python"):
        """代码审查"""
        prompt = f"""
请对以下{language}代码进行全面审查：

```{language}
{code}
```

请从以下方面进行评估：
1. 代码质量
2. 性能问题
3. 安全隐患
4. 改进建议
"""
        return self.call_api(prompt, temperature=0.2)
```

## 📊 三、多轮对话支持

### 对话管理器
```python
class DeepSeekConversation:
    def __init__(self, api_instance, system_prompt=None):
        self.api = api_instance
        self.messages = []
        
        if system_prompt:
            self.messages.append({"role": "system", "content": system_prompt})
    
    def add_user_message(self, content):
        """添加用户消息"""
        self.messages.append({"role": "user", "content": content})
    
    def add_assistant_message(self, content):
        """添加助手消息"""
        self.messages.append({"role": "assistant", "content": content})
    
    def chat(self, user_input, **kwargs):
        """进行对话"""
        self.add_user_message(user_input)
        
        response = self.api.call_api(self.messages, **kwargs)
        assistant_reply = self.api.extract_content(response)
        
        if assistant_reply:
            self.add_assistant_message(assistant_reply)
        
        return response
    
    def get_conversation_history(self):
        """获取对话历史"""
        return self.messages.copy()
    
    def clear_history(self, keep_system=True):
        """清空对话历史"""
        if keep_system and self.messages and self.messages[0]["role"] == "system":
            self.messages = [self.messages[0]]
        else:
            self.messages = []
    
    def save_conversation(self, filepath):
        """保存对话"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.messages, f, ensure_ascii=False, indent=2)
    
    def load_conversation(self, filepath):
        """加载对话"""
        with open(filepath, 'r', encoding='utf-8') as f:
            self.messages = json.load(f)
```

## 🔧 四、使用示例

### 基础使用
```python
# 初始化API
api = DeepSeekAPI()
specialized_api = DeepSeekSpecializedAPI()

# 代码生成
code_response = specialized_api.generate_code(
    "实现一个快速排序算法",
    language="python"
)
print("生成的代码:", api.extract_content(code_response))

# 代码解释
explanation = specialized_api.explain_code("""
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
""")
print("代码解释:", api.extract_content(explanation))

# 推理任务
reasoning_result = specialized_api.reasoning_task(
    "如果所有的猫都是动物，而所有的动物都需要食物，那么所有的猫都需要食物吗？"
)
print("推理结果:", api.extract_content(reasoning_result))
```

### 多轮对话
```python
# 创建对话
conversation = DeepSeekConversation(
    api,
    system_prompt="你是一个专业的Python编程助手，请帮助用户解决编程问题。"
)

# 进行对话
response1 = conversation.chat("我想学习Python的装饰器，请给我一个简单的例子")
print("回复1:", api.extract_content(response1))

response2 = conversation.chat("能否解释一下这个装饰器的工作原理？")
print("回复2:", api.extract_content(response2))

response3 = conversation.chat("如何创建一个带参数的装饰器？")
print("回复3:", api.extract_content(response3))

# 保存对话
conversation.save_conversation("python_decorator_chat.json")
```

### 代码开发助手
```python
class CodeDevelopmentAssistant:
    def __init__(self):
        self.api = DeepSeekSpecializedAPI()
    
    def develop_feature(self, feature_description, language="python"):
        """完整的功能开发流程"""
        print("🚀 开始功能开发...")
        
        # 1. 生成代码
        print("1. 生成代码...")
        code_response = self.api.generate_code(feature_description, language)
        code = self.api.extract_content(code_response)
        
        # 2. 代码审查
        print("2. 代码审查...")
        review_response = self.api.code_review(code, language)
        review = self.api.extract_content(review_response)
        
        # 3. 代码优化
        print("3. 代码优化...")
        optimize_response = self.api.optimize_code(code, language)
        optimized_code = self.api.extract_content(optimize_response)
        
        return {
            'original_code': code,
            'code_review': review,
            'optimized_code': optimized_code
        }

# 使用助手
assistant = CodeDevelopmentAssistant()
result = assistant.develop_feature("实现一个LRU缓存")

print("原始代码:", result['original_code'])
print("代码审查:", result['code_review'])
print("优化代码:", result['optimized_code'])
```

## 📋 五、最佳实践

### 1. 模型选择策略
```python
def choose_model(task_type):
    """根据任务类型选择最适合的模型"""
    model_mapping = {
        "code_generation": DEEPSEEK_CONFIG["models"]["coder"],
        "code_review": DEEPSEEK_CONFIG["models"]["coder"],
        "debugging": DEEPSEEK_CONFIG["models"]["coder"],
        "reasoning": DEEPSEEK_CONFIG["models"]["reasoner"],
        "math": DEEPSEEK_CONFIG["models"]["reasoner"],
        "general_chat": DEEPSEEK_CONFIG["models"]["chat"]
    }
    return model_mapping.get(task_type, DEEPSEEK_CONFIG["models"]["chat"])
```

### 2. 温度参数优化
```python
TEMPERATURE_SETTINGS = {
    "code_generation": 0.2,      # 代码生成需要准确性
    "creative_writing": 0.8,     # 创意写作需要多样性
    "factual_qa": 0.1,          # 事实问答需要准确性
    "reasoning": 0.1,           # 推理任务需要逻辑性
    "general_chat": 0.7         # 一般对话平衡准确性和多样性
}
```

### 3. 错误处理和重试
```python
def robust_api_call(api_instance, messages, max_retries=3):
    """健壮的API调用"""
    for attempt in range(max_retries):
        try:
            response = api_instance.call_api(messages)
            return response
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                wait_time = (2 ** attempt) * 2
                print(f"⚠️ 限流，等待 {wait_time}s...")
                time.sleep(wait_time)
            else:
                raise
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            print(f"⚠️ 请求失败，重试中... ({attempt + 1}/{max_retries})")
            time.sleep(2 ** attempt)
    
    raise Exception("API调用失败，已达到最大重试次数")
```

## 🎯 六、特殊功能

### 1. 代码执行验证
```python
def validate_generated_code(code, test_cases=None):
    """验证生成的代码"""
    try:
        # 语法检查
        compile(code, '<string>', 'exec')
        
        # 如果有测试用例，执行测试
        if test_cases:
            exec_globals = {}
            exec(code, exec_globals)
            
            for test_case in test_cases:
                # 执行测试用例
                result = eval(test_case['expression'], exec_globals)
                expected = test_case['expected']
                
                if result != expected:
                    return False, f"测试失败: {test_case['expression']} 期望 {expected}, 实际 {result}"
        
        return True, "代码验证通过"
    
    except SyntaxError as e:
        return False, f"语法错误: {e}"
    except Exception as e:
        return False, f"执行错误: {e}"
```

### 2. 智能提示生成
```python
def generate_smart_prompt(task_description, context=None):
    """生成智能提示"""
    base_prompt = f"任务：{task_description}\n"
    
    if context:
        base_prompt += f"上下文：{context}\n"
    
    # 根据任务类型添加特定指导
    if "代码" in task_description or "编程" in task_description:
        base_prompt += """
请提供：
1. 完整可运行的代码
2. 详细的注释说明
3. 使用示例
4. 可能的改进建议
"""
    elif "解释" in task_description or "分析" in task_description:
        base_prompt += """
请提供：
1. 清晰的解释
2. 具体的例子
3. 相关的背景知识
4. 实际应用场景
"""
    
    return base_prompt
```

DeepSeek API特别适合代码相关任务和需要深度推理的场景，是开发者的得力助手。
