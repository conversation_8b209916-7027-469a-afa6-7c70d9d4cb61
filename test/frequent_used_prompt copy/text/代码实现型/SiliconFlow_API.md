# SiliconFlow 文本API调度

## 🎯 一、基础配置

### API基本信息
- **服务商**: SiliconFlow
- **API地址**: `https://api.siliconflow.cn/v1/chat/completions`
- **认证方式**: Bearer <PERSON>
- **默认模型**: `deepseek-ai/DeepSeek-V3`
- **备选模型**: `deepseek-ai/DeepSeek-R1`

### 配置参数
```python
SILICONFLOW_CONFIG = {
    "api_url": "https://api.siliconflow.cn/v1/chat/completions",
    "api_key": "sk-iinwlldkziyjemchdrzmwuppvmjcdjemdxhjlompizouxbnl",
    "models": {
        "deepseek_v3": "deepseek-ai/DeepSeek-V3",
        "deepseek_r1": "deepseek-ai/DeepSeek-R1"
    },
    "default_params": {
        "temperature": 0.5,
        "max_tokens": 4000,
        "timeout": 240
    },
    "rate_limits": {
        "requests_per_minute": 30,
        "max_concurrent": 5
    }
}
```

## 🚀 二、核心实现

### 1. SiliconFlow API类
```python
import requests
import os
import time
import json

class SiliconFlowAPI:
    def __init__(self, api_key=None, model=None):
        self.api_key = api_key or os.environ.get("SILICONFLOW_API_TOKEN", 
                                                SILICONFLOW_CONFIG["api_key"])
        self.base_url = SILICONFLOW_CONFIG["api_url"]
        self.default_model = model or SILICONFLOW_CONFIG["models"]["deepseek_v3"]
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        })
    
    def call_api(self, content, model=None, **kwargs):
        """标准API调用"""
        model = model or self.default_model
        
        # 合并默认参数
        params = SILICONFLOW_CONFIG["default_params"].copy()
        params.update(kwargs)
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": content}],
            "stream": False,
            **params
        }
        
        response = self.session.post(
            self.base_url, 
            json=payload, 
            timeout=params.get("timeout", 240)
        )
        response.raise_for_status()
        return response.json()
    
    def extract_content(self, response):
        """提取响应内容"""
        try:
            return response["choices"][0]["message"]["content"].strip()
        except (KeyError, IndexError):
            return None
    
    def get_token_usage(self, response):
        """获取token使用情况"""
        return response.get("usage", {})
    
    def call_with_retry(self, content, max_retries=3, **kwargs):
        """带重试的API调用"""
        last_error = None
        
        for attempt in range(max_retries):
            try:
                response = self.call_api(content, **kwargs)
                return response
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:
                    wait_time = (2 ** attempt) + 1
                    print(f"⚠️ 限流，等待 {wait_time}s...")
                    time.sleep(wait_time)
                    last_error = e
                else:
                    raise
            except requests.exceptions.RequestException as e:
                wait_time = 2 ** attempt
                print(f"⚠️ 请求异常，等待 {wait_time}s...")
                time.sleep(wait_time)
                last_error = e
        
        raise last_error
```

### 2. 专用功能方法
```python
class SiliconFlowSpecializedAPI(SiliconFlowAPI):
    def generate_text(self, prompt, max_tokens=2000, temperature=0.7):
        """文本生成"""
        return self.call_api(
            prompt, 
            max_tokens=max_tokens, 
            temperature=temperature
        )
    
    def analyze_text(self, text, analysis_type="sentiment"):
        """文本分析"""
        prompts = {
            "sentiment": f"分析以下文本的情感倾向：\n{text}",
            "summary": f"总结以下文本的主要内容：\n{text}",
            "keywords": f"提取以下文本的关键词：\n{text}",
            "classification": f"对以下文本进行分类：\n{text}"
        }
        
        prompt = prompts.get(analysis_type, f"分析以下文本：\n{text}")
        return self.call_api(prompt, temperature=0.3)
    
    def translate_text(self, text, target_lang="英文"):
        """文本翻译"""
        prompt = f"将以下文本翻译成{target_lang}：\n{text}"
        return self.call_api(prompt, temperature=0.2)
    
    def rewrite_text(self, text, style="正式"):
        """文本改写"""
        prompt = f"将以下文本改写为{style}风格：\n{text}"
        return self.call_api(prompt, temperature=0.5)
    
    def extract_json(self, text, schema_description):
        """结构化数据提取"""
        prompt = f"""
从以下文本中提取信息，按照指定格式返回JSON：

格式要求：{schema_description}

文本内容：
{text}

请返回标准JSON格式：
"""
        return self.call_api(prompt, temperature=0.1)
```

## 📊 三、批量处理

### 批量文本处理器
```python
import concurrent.futures
from tqdm import tqdm

class SiliconFlowBatchProcessor:
    def __init__(self, api_instance, max_workers=3):
        self.api = api_instance
        self.max_workers = max_workers
    
    def process_batch(self, texts, process_func, **kwargs):
        """批量处理文本"""
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_text = {
                executor.submit(process_func, text, **kwargs): text 
                for text in texts
            }
            
            # 收集结果
            for future in tqdm(concurrent.futures.as_completed(future_to_text), 
                             total=len(future_to_text), desc="处理文本"):
                text = future_to_text[future]
                try:
                    result = future.result()
                    results.append({
                        'input': text,
                        'output': result,
                        'success': True
                    })
                except Exception as e:
                    results.append({
                        'input': text,
                        'error': str(e),
                        'success': False
                    })
        
        return results
    
    def batch_generate(self, prompts, **kwargs):
        """批量生成"""
        return self.process_batch(prompts, self.api.generate_text, **kwargs)
    
    def batch_analyze(self, texts, analysis_type="sentiment"):
        """批量分析"""
        return self.process_batch(
            texts, 
            lambda text: self.api.analyze_text(text, analysis_type)
        )
    
    def batch_translate(self, texts, target_lang="英文"):
        """批量翻译"""
        return self.process_batch(
            texts,
            lambda text: self.api.translate_text(text, target_lang)
        )
```

## 🔧 四、使用示例

### 基础使用
```python
# 初始化API
api = SiliconFlowAPI()
specialized_api = SiliconFlowSpecializedAPI()

# 基础调用
response = api.call_api("你好，请介绍一下人工智能")
content = api.extract_content(response)
token_usage = api.get_token_usage(response)

print(f"回复: {content}")
print(f"Token使用: {token_usage}")

# 专用功能
sentiment_result = specialized_api.analyze_text("这个产品真的很棒！", "sentiment")
translation_result = specialized_api.translate_text("Hello world", "中文")
summary_result = specialized_api.analyze_text("很长的文本内容...", "summary")
```

### 批量处理
```python
# 批量处理
processor = SiliconFlowBatchProcessor(specialized_api, max_workers=3)

# 批量情感分析
texts = ["这个很好", "不太满意", "还可以"]
results = processor.batch_analyze(texts, "sentiment")

# 批量翻译
english_texts = ["Hello", "Good morning", "Thank you"]
translations = processor.batch_translate(english_texts, "中文")

# 统计结果
successful = sum(1 for r in results if r['success'])
print(f"处理成功: {successful}/{len(results)}")
```

### 高级用法
```python
# JSON提取
text = "张三，男，25岁，软件工程师，住在北京"
schema = "姓名、性别、年龄、职业、居住地"
json_result = specialized_api.extract_json(text, schema)

# 自定义参数调用
creative_response = api.call_api(
    "写一首关于春天的诗",
    temperature=0.9,
    max_tokens=500
)

# 使用不同模型
r1_response = api.call_api(
    "解决这个数学问题：2x + 3 = 7",
    model=SILICONFLOW_CONFIG["models"]["deepseek_r1"]
)
```

## 📋 五、最佳实践

### 1. 性能优化
- 使用Session复用连接
- 合理设置并发数(3-5)
- 批量处理相似任务
- 缓存常用结果

### 2. 错误处理
- 实现重试机制
- 区分不同错误类型
- 记录失败请求
- 优雅降级

### 3. 成本控制
- 控制max_tokens参数
- 使用合适的temperature
- 避免重复请求
- 监控token使用量

### 4. 安全考虑
- 使用环境变量存储API密钥
- 验证输入内容
- 过滤敏感信息
- 实现访问控制

## 🎯 六、特殊场景

### 1. 长文本处理
```python
def process_long_text(text, max_chunk_size=3000):
    """处理超长文本"""
    if len(text) <= max_chunk_size:
        return api.call_api(text)
    
    # 分块处理
    chunks = [text[i:i+max_chunk_size] for i in range(0, len(text), max_chunk_size)]
    results = []
    
    for chunk in chunks:
        result = api.call_api(f"继续处理以下文本片段：\n{chunk}")
        results.append(api.extract_content(result))
    
    return results
```

### 2. 流式处理
```python
def stream_process(texts):
    """流式处理文本"""
    for text in texts:
        try:
            result = api.call_with_retry(text)
            yield {
                'input': text,
                'output': api.extract_content(result),
                'success': True
            }
        except Exception as e:
            yield {
                'input': text,
                'error': str(e),
                'success': False
            }
```

SiliconFlow API是文本处理的主力工具，具有高性能、低延迟的特点，适合大规模文本生成和分析任务。
