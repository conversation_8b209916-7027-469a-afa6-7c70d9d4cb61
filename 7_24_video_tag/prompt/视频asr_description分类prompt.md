## Task Description
You are an expert in classifying e-commerce shoe video content based on visual descriptions, audio transcripts (ASR). Your task is to analyze multi-modal input and categorize it according to the primary focus.

## Input Dictionary Structure
Each input will be a dict with three fields:
- description: [List of visual descriptions from video frames]
- asr: [List of audio transcript segments, may be in foreign languages]

## Category System
### 1. Pain Point Description
Key differentiator: Shows product shortcomings, such as Comfort Issues ,Performance Failures and Purchase Anxiety. 
- Visual: Foot discomfort evidence (blisters, redness), Product failure demonstrations (leaking, sole detachment), Comparative negatives (side-by-side with competitor)
- Audio: Discomfort references ("rubbing", "uncomfortable", "too stiff"), Problem descriptions ("water gets in", "traction issues")

### 2. Usage Scenario
Key differentiator: Neutral environment showcase, such as Office environments, Lifestyle, Activity-Specific.  
- Visual: Environmental shots >50% duration (urban, trail, office), Natural movement without technical focus
- Audio: Setting descriptions ("walking downtown", "at the gym"), Activity narration ("commuting in", "weekend wear")

### 3. Product Selling Points
Key differentiator: feature highlights, such as Technical Features, Design Excellence,Functional Benefits.
- Visual: close-ups (stitching, sole tech), Function tests (water droplets, flex demonstrations)
- Audio: Comfortable craftsmanship ("Soft sole"), Performance claims ("breathable", "shock absorbing")

### 4. User Reviews
Key differentiator: First-person perspective content
- Visual: Handheld POV footage, Unboxing sequences, Mirror selfie angles
- Audio: Personal feedback ("I love these shoes", "They fit perfectly"), User experiences ("I wear them daily")

### 5. Call to Action
Key differentiator: Clear conversion intent*
- Visual:  CTAs covering >20% frame, Time-sensitive graphics ("FLASH SALE")
- Audio: Urgency cues ("limited stock", "today only"), Direct prompts ("click link", "order now")

### 6. Other
Default for non-standard content:
- Visual: Conceptual imagery (floating shoes), Pure branding (logos without product)
- Audio: Non-product narration (brand history)

## Classification Instructions
Please classify the following dict:
Target dict: {dict}

## Output Format
Please output the classification result in the following JSON format:
{
  "primary_category": "Primary Category Name",
  "secondary_categories": ["Secondary Category Name 1", "Secondary Category Name 2"],
  "specific_tags": ["Specific Tag 1", "Specific Tag 2", "Specific Tag 3"],
  "confidence_score": 0.95,
  "reasoning": "Explanation of classification basis and keyword matching"
}

## Classification Requirements
- Accuracy first: Classify based on the actual meaning of the text, avoid over-interpretation,Clear category should ≥2 matching indicators
- Keyword matching: Prioritize matching specific keywords and descriptions in the category system
- Context understanding: Consider the overall context and intent of the text
- Open handling: For content not fully matching existing categories, classify as "Other" and explain specific features in reasoning
- Label system evolution: If important new content patterns are found, mark "suggest adding new label type" in reasoning