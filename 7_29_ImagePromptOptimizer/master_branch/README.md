# 文本润色服务

1.Python基础镜像
registry.cn-shenzhen.aliyuncs.com/docker-mmt/aigc-nvidia-cuda-12.4.0-devel-ubuntu22.04-tools-python3.11:v1.1

pip install -i https://mirrors.aliyun.com/pypi/simple -r requirements.txt

2.同步
20250521部署说明
- https://codeup.aliyun.com/61ee8bb874c3f3b55073ffd0/AIGC/ImagePromptOptimizer
@Mack 
- 分支：master
- 镜像：registry.cn-shenzhen.aliyuncs.com/docker-mmt/aigc-nvidia-cuda-12.4.0-devel-ubuntu22.04-tools-python3.11:v1.1
- 启动脚本 sh start.sh
- 部署在1台A100的机器的cpu上，起一个服务
- 机器：*************
- 端口为 9899
- 起容器需要network为host