# 视频内容分类系统

## 🚀 快速导航

- **快速开始:** [一键运行](#-快速开始) | **性能指标:** [详细指标](#详细性能指标-precision-recall-f1-score)
- **项目总结:** [PROJECT_SUMMARY.md](PROJECT_SUMMARY.md) ⭐
- **详细分析:** [reports/DETAILED_METRICS_ANALYSIS.md](reports/DETAILED_METRICS_ANALYSIS.md) ⭐
- **综合报告:** [reports/FINAL_COMPREHENSIVE_ANALYSIS.md](reports/FINAL_COMPREHENSIVE_ANALYSIS.md) ⭐

## 📋 项目概述

本项目是一个基于大语言模型的视频内容分类系统，专门用于电商鞋类视频的内容分析和分类。系统使用DeepSeek-V3模型，能够对视频的描述、ASR（语音识别）和OCR（文字识别）内容进行多维度分析，实现高精度的内容分类。

## 🎯 核心特性

### ✨ 高准确率分类
- **主要类别准确率:** 66.7%
- **增强准确率（主要+次要类别）:** **93.3%**
- **准确率提升:** +26.7个百分点
- **评估样本:** 150个人工标注样本

### 🔧 技术特点
- 基于原始提示词的稳定分类逻辑
- 增强的JSON输出格式，包含主要类别、次要类别、标签和置信度
- 支持多标签分类，更符合实际业务需求
- 并发处理，高效批量分类
- 完善的错误处理和重试机制

### 📊 分类体系
支持6个主要类别的分类：
1. **Pain Point Description (痛点描述)** - 产品不足和问题
2. **Usage Scenario (使用场景)** - 环境展示和生活方式
3. **Product Selling Points (产品卖点)** - 技术特性和设计优势
4. **User Reviews (用户评价)** - 第一人称视角内容
5. **Call to Action (行动召唤)** - 转化导向内容
6. **Other (其他)** - 品牌展示等非标准内容

## 📁 文件结构

```
7_31_video_mashup_merge/
├── README.md                          # 项目说明文档
├── run.py                             # 一键运行脚本 ⭐
├── video_classifier.py                # 主分类器脚本 ⭐
├── data/                              # 输入数据目录
│   ├── merged_video_data.json         # 合并的视频数据
│   ├── asr_ocr/                       # ASR和OCR数据
│   └── video_descriptions_mashup/     # 视频描述数据
├── scripts/                           # 分析脚本目录
│   ├── analyze_new_labels.py          # 准确率分析脚本
│   └── detailed_metrics_analyzer.py   # 详细性能指标分析脚本 ⭐
└── reports/                           # 分析报告目录
    ├── video_classification_results_enhanced.json    # 分类结果 ⭐
    ├── DETAILED_METRICS_ANALYSIS.md                 # 详细性能指标分析 ⭐
    ├── FINAL_COMPREHENSIVE_ANALYSIS.md              # 最终综合分析报告 ⭐
    └── detailed_metrics_analysis_report.json        # 详细指标JSON报告
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 必要的Python包：`requests`, `json`, `logging`, `concurrent.futures`

### 一键运行（推荐）

```bash
cd /data2/jevon/7_31_video_mashup_merge
python run.py
```

### 分步运行

#### 1. 运行分类

```bash
python video_classifier.py
```

#### 2. 分析准确率

```bash
python accuracy_analyzer.py
```

## 📊 输出格式

### 分类结果格式
```json
{
  "video_id": "dataset_name/scene_001.mp4",
  "dataset_name": "dataset_name",
  "primary_category": "Product Selling Points",
  "secondary_categories": ["Design Excellence", "Functional Benefits"],
  "specific_tags": ["close-up", "leather shoes", "hybrid design"],
  "confidence_score": 0.95,
  "reasoning": "详细的分类推理说明...",
  "error": null
}
```

### 关键字段说明
- **primary_category**: 主要分类类别
- **secondary_categories**: 次要分类类别（最多2个）
- **specific_tags**: 具体内容标签（3-5个）
- **confidence_score**: 分类置信度（0.0-1.0）
- **reasoning**: 详细的分类推理过程

## 📈 性能指标

### 整体性能
- **处理视频数量:** 234个
- **成功率:** 100%
- **平均置信度:** 0.900
- **高置信度比例:** 100%（≥0.8）

### 分类分布
| 类别 | 数量 | 占比 |
|------|------|------|
| Product Selling Points | 148 | 63.2% |
| Usage Scenario | 62 | 26.5% |
| Other | 13 | 5.6% |
| Pain Point Description | 5 | 2.1% |
| Call to Action | 3 | 1.3% |
| User Reviews | 3 | 1.3% |

### 准确率分析（基于150个新标签样本）
| 类别 | 主要类别准确率 | 增强准确率 | 提升幅度 | 样本数 |
|------|----------------|------------|----------|--------|
| Product Selling Points | 85.7% | 100% | +14.3% | 63 |
| Usage Scenario | 49.3% | 90.7% | +41.3% | 75 |
| Other | 100% | 100% | 0% | 6 |
| User Reviews | 100% | 100% | 0% | 2 |
| Call to Action | 100% | 100% | 0% | 1 |
| Pain Point Description | 0% | 0% | 0% | 3 |

### 详细性能指标 (Precision, Recall, F1-Score)

#### 主要类别指标
| 类别 | 精确率 | 召回率 | F1分数 | 样本数 |
|------|--------|--------|--------|--------|
| Product Selling Points | 0.607 | 0.857 | 0.711 | 63 |
| Usage Scenario | 0.925 | 0.493 | 0.643 | 75 |
| Other | 0.545 | 1.000 | 0.706 | 6 |
| User Reviews | 1.000 | 1.000 | 1.000 | 2 |
| Call to Action | 0.333 | 1.000 | 0.500 | 1 |
| Pain Point Description | 0.000 | 0.000 | 0.000 | 3 |

#### 增强类别指标 (主要+次要)
| 类别 | 精确率 | 召回率 | F1分数 | 样本数 |
|------|--------|--------|--------|--------|
| Product Selling Points | 0.940 | 1.000 | 0.969 | 63 |
| Usage Scenario | 1.000 | 0.907 | 0.951 | 75 |
| Other | 0.600 | 1.000 | 0.750 | 6 |
| User Reviews | 1.000 | 1.000 | 1.000 | 2 |
| Call to Action | 1.000 | 1.000 | 1.000 | 1 |
| Pain Point Description | 0.000 | 0.000 | 0.000 | 3 |

#### 整体性能指标
- **整体准确率:** 93.3%
- **宏平均F1分数:** 0.778
- **微平均F1分数:** 0.933

## 🔧 配置说明

### API配置
系统使用SiliconFlow API调用DeepSeek-V3模型：
- **API地址:** https://api.siliconflow.cn/v1/chat/completions
- **模型:** Pro/deepseek-ai/DeepSeek-V3
- **并发数:** 3个线程
- **重试次数:** 3次
- **超时时间:** 240秒

### 分类逻辑
- 基于原始提示词模板（`/data2/jevon/7_24_video_tag/prompt/视频asr_description分类prompt.md`）
- 增强的输出格式，包含多维度信息
- 支持主要+次要类别的匹配策略

## 📋 使用建议

### 1. 生产环境部署
- **推荐使用增强格式版本**，准确率最高（91.7%）
- 设置合适的并发数，避免API限流
- 建立分类结果的人工审核机制

### 2. 结果解读
- **高置信度结果（≥0.8）**：可直接使用
- **考虑次要类别**：提供更全面的内容理解
- **利用具体标签**：进行细粒度的内容分析

### 3. 持续优化
- 定期收集更多标注数据验证准确率
- 根据业务需求调整类别权重
- 监控分类结果质量，及时调整参数

## 🎯 业务价值

### 内容管理
- **自动化分类**：减少人工标注工作量
- **多维度分析**：主要+次要类别提供全面视角
- **高准确率**：91.7%的准确率满足生产需求

### 营销策略
- **内容分布洞察**：63.2%产品卖点，26.5%使用场景
- **优化建议**：增加用户评价和行动召唤内容
- **标签分析**：识别高频内容特征

## 📞 技术支持

如有问题或需要技术支持，请参考：
1. `output/ENHANCED_FORMAT_FINAL_SUMMARY.md` - 详细技术文档
2. `output/enhanced_accuracy_analysis_report.json` - 完整分析报告
3. 系统日志文件 - 运行时生成的详细日志

## 📝 更新日志

### v1.0.0 (2025-08-01)
- ✅ 实现基于原始提示词的分类逻辑
- ✅ 增强JSON输出格式
- ✅ 支持主要+次要类别匹配
- ✅ 实现91.7%的高准确率
- ✅ 完整的准确率分析功能
- ✅ 并发处理和错误重试机制

---

**系统开发完成日期:** 2025年8月1日  
**推荐使用版本:** 增强格式版本（当前版本）  
**核心优势:** 91.7%准确率，22.2个百分点的显著提升
