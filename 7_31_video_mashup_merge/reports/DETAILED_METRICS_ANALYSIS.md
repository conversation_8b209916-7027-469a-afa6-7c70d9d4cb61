# 视频内容分类性能指标报告

## 📊 评估概述

**评估时间:** 2025年8月1日
**评估样本数:** 150个人工标注样本
**分类策略:** 多标签分类（主要+次要类别）
**数据来源:** `/data2/jevon/7_31_video_mashup/打标数据.xlsx`

## 🎯 性能指标详情

### 各类别详细指标

| 类别 (Category) | 精确率 (Precision) | 召回率 (Recall) | F1分数 (F1-Score) | 样本数 (Support) |
|---|---|---|---|---|
| Product Selling Points | 0.940 | 1.000 | 0.969 | 63 |
| Usage Scenario | 1.000 | 0.907 | 0.951 | 75 |
| User Reviews | 1.000 | 1.000 | 1.000 | 2 |
| Call to Action | 1.000 | 1.000 | 1.000 | 1 |
| Other | 0.600 | 1.000 | 0.750 | 6 |
| Pain Point Description | 0.000 | 0.000 | 0.000 | 3 |

### 整体性能指标

- **整体准确率 (Accuracy):** 93.3%
- **宏平均精确率 (Macro Avg Precision):** 0.757
- **宏平均召回率 (Macro Avg Recall):** 0.818
- **宏平均F1分数 (Macro Avg F1-Score):** 0.778
- **微平均精确率 (Micro Avg Precision):** 0.933
- **微平均召回率 (Micro Avg Recall):** 0.933
- **微平均F1分数 (Micro Avg F1-Score):** 0.933

## 📋 关键发现

### 🎯 最佳表现类别
- **完美分类:** User Reviews, Call to Action (F1: 1.000)
- **优秀表现:** Product Selling Points (F1: 0.969), Usage Scenario (F1: 0.951)
- **良好表现:** Other (F1: 0.750)
- **需要改进:** Pain Point Description (F1: 0.000)

### 📊 类别分析
- **最大类别:** Usage Scenario (75个样本，50%）
- **第二大类别:** Product Selling Points (63个样本，42%）
- **稀少类别:** Pain Point Description (3个), User Reviews (2个), Call to Action (1个)

### 🔍 技术特点
- **多标签策略:** 主要+次要类别提供更全面的内容理解
- **高精确率:** 大部分类别精确率达到90%以上
- **稳定召回率:** 多数类别召回率表现优秀
- **边界处理:** 有效处理类别间的模糊边界

### 📈 系统性能总结
- **评估样本规模:** 150个人工标注样本
- **整体准确率:** 93.3%
- **宏平均F1分数:** 0.778
- **处理成功率:** 100%
- **系统稳定性:** 优秀

### 💡 优化建议
1. **重点改进痛点描述识别** - 当前F1分数为0
2. **增加稀少类别样本** - 提高用户评价和行动召唤的数据量
3. **建立监控机制** - 定期评估分类质量
4. **持续优化** - 根据业务反馈调整分类策略

---
*报告生成时间: 2025年8月1日*
*基于150个样本的全面评估*
