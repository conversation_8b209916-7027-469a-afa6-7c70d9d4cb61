#!/usr/bin/env python3
"""
视频内容分类系统 - 一键运行脚本
Quick run script for video content classification system
"""

import subprocess
import sys
import logging
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors"""
    logger.info(f"🚀 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} 失败:")
        logger.error(f"错误代码: {e.returncode}")
        logger.error(f"错误信息: {e.stderr}")
        return False

def check_prerequisites():
    """Check if required files exist"""
    logger.info("🔍 检查必要文件...")
    
    required_files = [
        "data/merged_video_data.json",
        "video_classifier.py",
        "scripts/analyze_new_labels.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    logger.info("✅ 所有必要文件检查通过")
    return True

def main():
    """Main execution function"""
    start_time = datetime.now()
    logger.info("=" * 80)
    logger.info("🎯 视频内容分类系统 - 一键运行")
    logger.info(f"🕒 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 80)
    
    # Check prerequisites
    if not check_prerequisites():
        logger.error("❌ 前置条件检查失败，程序退出")
        return False
    
    # Ensure output directory exists
    Path("reports").mkdir(exist_ok=True)

    # Step 1: Run classification
    logger.info("\n📊 步骤 1: 执行视频内容分类")
    if not run_command("python video_classifier.py", "视频内容分类"):
        return False

    # Step 2: Run detailed metrics analysis
    logger.info("\n📊 步骤 2: 执行详细性能指标分析")
    if not run_command("python scripts/detailed_metrics_analyzer.py", "详细性能指标分析"):
        logger.warning("⚠️ 详细指标分析失败，但分类结果已生成")
    
    # Calculate total time
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    logger.info("=" * 80)
    logger.info("🎉 视频内容分类系统运行完成！")
    logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
    logger.info(f"🕒 完成时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # List output files
    reports_dir = Path("reports")
    if reports_dir.exists():
        logger.info("\n📁 生成的文件:")
        for file_path in sorted(reports_dir.glob("*")):
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                logger.info(f"  📄 {file_path.name} ({size_mb:.2f} MB)")

    logger.info("\n📋 使用说明:")
    logger.info("  1. 查看分类结果: reports/video_classification_results_enhanced.json")
    logger.info("  2. 查看详细性能指标: reports/DETAILED_METRICS_ANALYSIS.md")
    logger.info("  3. 查看综合分析报告: reports/FINAL_COMPREHENSIVE_ANALYSIS.md")
    logger.info("  4. 查看项目总结: PROJECT_SUMMARY.md")
    logger.info("  5. 查看系统说明: README.md")
    
    logger.info("=" * 80)
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.warning("\n⚠️ 用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 程序异常退出: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
