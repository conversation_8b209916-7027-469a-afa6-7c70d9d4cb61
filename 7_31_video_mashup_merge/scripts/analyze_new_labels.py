#!/usr/bin/env python3
"""
基于新的打标数据重新评估分类准确率
"""

import pandas as pd
import json
import logging
from collections import Counter, defaultdict
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewLabelAccuracyAnalyzer:
    """基于新打标数据的准确率分析器"""
    
    def __init__(self):
        """Initialize analyzer"""
        # Category mapping (ID to name)
        self.category_mapping = {
            1: "Pain Point Description",
            2: "Usage Scenario", 
            3: "Product Selling Points",
            4: "User Reviews",
            5: "Call to Action",
            6: "Other"
        }
        
        # Reverse mapping (name to ID)
        self.name_to_id_mapping = {v: k for k, v in self.category_mapping.items()}
        
        logger.info("✅ 新标签准确率分析器初始化完成")
    
    def load_new_ground_truth(self, excel_file_path: str) -> dict:
        """加载新的打标数据"""
        logger.info(f"📂 从 {excel_file_path} 加载新的打标数据")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file_path)
            logger.info(f"✅ Excel文件读取成功，数据形状: {df.shape}")
            logger.info(f"📋 列名: {list(df.columns)}")
            
            # 显示前几行数据以了解结构
            logger.info("📄 前5行数据:")
            for i, row in df.head().iterrows():
                logger.info(f"  行 {i+1}: {dict(row)}")
            
            # 尝试识别视频路径和类别列
            ground_truth = {}
            
            # 常见的列名模式
            possible_path_columns = ['video_path', 'path', '视频路径', '文件路径', 'file_path', 'video', '视频']
            possible_category_columns = ['category', 'label', '类别', '标签', 'class', '分类']
            
            path_col = None
            category_col = None
            
            # 查找路径列
            for col in df.columns:
                col_lower = str(col).lower()
                if any(pattern in col_lower for pattern in ['path', '路径', 'video', '视频', 'file']):
                    path_col = col
                    break
            
            # 查找类别列
            for col in df.columns:
                col_lower = str(col).lower()
                if any(pattern in col_lower for pattern in ['category', 'label', '类别', '标签', 'class', '分类']):
                    category_col = col
                    break
            
            if path_col is None:
                logger.warning("⚠️ 未找到明确的路径列，尝试使用第一列")
                path_col = df.columns[0]
            
            if category_col is None:
                logger.warning("⚠️ 未找到明确的类别列，尝试使用第二列")
                if len(df.columns) > 1:
                    category_col = df.columns[1]
                else:
                    logger.error("❌ 无法识别类别列")
                    return {}
            
            logger.info(f"📍 使用路径列: {path_col}")
            logger.info(f"📍 使用类别列: {category_col}")
            
            # 处理数据
            for index, row in df.iterrows():
                try:
                    video_path = str(row[path_col])
                    category_value = row[category_col]
                    
                    # 跳过空值
                    if pd.isna(video_path) or pd.isna(category_value) or video_path.strip() == '':
                        continue
                    
                    # 提取视频ID
                    video_id = self._extract_video_id(video_path)
                    if video_id:
                        # 处理类别值
                        category_id = self._parse_category(category_value)
                        if category_id:
                            ground_truth[video_id] = category_id
                            
                except Exception as e:
                    logger.warning(f"⚠️ 处理第{index+1}行时出错: {e}")
                    continue
            
            logger.info(f"✅ 成功加载 {len(ground_truth)} 个标注数据")
            
            # 显示一些示例
            logger.info("📋 标注数据示例:")
            for i, (video_id, category_id) in enumerate(list(ground_truth.items())[:5]):
                category_name = self.category_mapping.get(category_id, f"Category_{category_id}")
                logger.info(f"  {i+1}. {video_id} -> {category_name}")
            
            return ground_truth
            
        except Exception as e:
            logger.error(f"❌ 加载新标签数据失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {}
    
    def _extract_video_id(self, video_path: str) -> str:
        """从视频路径提取视频ID"""
        try:
            # 处理不同的路径格式
            video_path = video_path.strip()
            
            # 如果是完整路径，提取最后两级
            if '/' in video_path:
                parts = video_path.split('/')
                if len(parts) >= 2:
                    dataset_name = parts[-2]
                    scene_name = parts[-1].replace('.mp4', '')
                    return f"{dataset_name}/{scene_name}"
            
            # 如果已经是dataset/scene格式
            if '/' in video_path and video_path.count('/') == 1:
                return video_path.replace('.mp4', '')
            
            # 其他格式的处理
            return video_path.replace('.mp4', '')
            
        except Exception as e:
            logger.warning(f"⚠️ 提取视频ID失败: {video_path}, 错误: {e}")
            return None
    
    def _parse_category(self, category_value) -> int:
        """解析类别值"""
        try:
            # 如果是数字
            if isinstance(category_value, (int, float)):
                category_id = int(category_value)
                if 1 <= category_id <= 6:
                    return category_id

            # 如果是字符串
            if isinstance(category_value, str):
                category_str = category_value.strip()

                # 尝试转换为数字
                try:
                    category_id = int(category_str)
                    if 1 <= category_id <= 6:
                        return category_id
                except ValueError:
                    pass

                # 中文类别名称映射
                chinese_mapping = {
                    "痛点描述": 1,
                    "使用场景": 2,
                    "产品卖点": 3,
                    "用户评价": 4,
                    "行动召唤": 5,
                    "其他": 6
                }

                # 直接匹配中文类别
                if category_str in chinese_mapping:
                    return chinese_mapping[category_str]

                # 尝试匹配英文类别名称
                for name, cat_id in self.name_to_id_mapping.items():
                    if name.lower() in category_str.lower() or category_str.lower() in name.lower():
                        return cat_id

            logger.warning(f"⚠️ 无法解析类别值: {category_value}")
            return None

        except Exception as e:
            logger.warning(f"⚠️ 解析类别值时出错: {category_value}, 错误: {e}")
            return None
    
    def load_classification_results(self, results_file_path: str) -> list:
        """加载分类结果"""
        logger.info(f"📂 从 {results_file_path} 加载分类结果")
        
        try:
            with open(results_file_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            logger.info(f"✅ 加载了 {len(results)} 个分类结果")
            return results
            
        except Exception as e:
            logger.error(f"❌ 加载分类结果失败: {e}")
            return []
    
    def analyze_accuracy_with_new_labels(self, results: list, new_ground_truth: dict) -> dict:
        """基于新标签分析准确率"""
        logger.info("📊 基于新标签分析准确率...")
        
        analysis = {
            'total_videos': len(results),
            'videos_with_new_ground_truth': 0,
            'primary_only_accuracy': {
                'correct': 0,
                'total': 0,
                'accuracy': 0.0
            },
            'primary_or_secondary_accuracy': {
                'correct': 0,
                'total': 0,
                'accuracy': 0.0
            },
            'category_performance': defaultdict(lambda: {
                'primary_correct': 0,
                'primary_or_secondary_correct': 0,
                'total': 0,
                'primary_accuracy': 0.0,
                'enhanced_accuracy': 0.0
            }),
            'detailed_results': [],
            'error_analysis': {
                'no_ground_truth': 0,
                'classification_failed': 0,
                'category_mapping_failed': 0
            }
        }
        
        for result in results:
            video_id = result.get('video_id', '').replace('.mp4', '')
            
            # 检查是否有新的真实标签
            if video_id not in new_ground_truth:
                analysis['error_analysis']['no_ground_truth'] += 1
                continue
            
            true_category_id = new_ground_truth[video_id]
            analysis['videos_with_new_ground_truth'] += 1
            
            # 检查分类是否成功
            if result.get('error') or not result.get('primary_category'):
                analysis['error_analysis']['classification_failed'] += 1
                continue
            
            # 映射预测类别到ID
            primary_category_name = result.get('primary_category')
            secondary_category_names = result.get('secondary_categories', [])
            
            primary_category_id = self._map_category_name_to_id(primary_category_name)
            secondary_category_ids = [
                self._map_category_name_to_id(name) for name in secondary_category_names
            ]
            secondary_category_ids = [id for id in secondary_category_ids if id is not None]
            
            if primary_category_id is None:
                analysis['error_analysis']['category_mapping_failed'] += 1
                continue
            
            # 更新统计
            analysis['primary_only_accuracy']['total'] += 1
            analysis['primary_or_secondary_accuracy']['total'] += 1
            analysis['category_performance'][true_category_id]['total'] += 1
            
            # 检查主要类别匹配
            primary_match = (primary_category_id == true_category_id)
            if primary_match:
                analysis['primary_only_accuracy']['correct'] += 1
                analysis['category_performance'][true_category_id]['primary_correct'] += 1
            
            # 检查主要或次要类别匹配
            enhanced_match = primary_match or (true_category_id in secondary_category_ids)
            if enhanced_match:
                analysis['primary_or_secondary_accuracy']['correct'] += 1
                analysis['category_performance'][true_category_id]['primary_or_secondary_correct'] += 1
            
            # 记录详细结果
            analysis['detailed_results'].append({
                'video_id': video_id,
                'true_category_id': true_category_id,
                'true_category_name': self.category_mapping.get(true_category_id, f"Category_{true_category_id}"),
                'primary_category_id': primary_category_id,
                'primary_category_name': primary_category_name,
                'secondary_category_ids': secondary_category_ids,
                'secondary_category_names': secondary_category_names,
                'primary_match': primary_match,
                'enhanced_match': enhanced_match,
                'confidence_score': result.get('confidence_score', 0.0)
            })
        
        # 计算准确率
        if analysis['primary_only_accuracy']['total'] > 0:
            analysis['primary_only_accuracy']['accuracy'] = (
                analysis['primary_only_accuracy']['correct'] / analysis['primary_only_accuracy']['total']
            )
        
        if analysis['primary_or_secondary_accuracy']['total'] > 0:
            analysis['primary_or_secondary_accuracy']['accuracy'] = (
                analysis['primary_or_secondary_accuracy']['correct'] / analysis['primary_or_secondary_accuracy']['total']
            )
        
        # 计算各类别准确率
        for cat_id, perf in analysis['category_performance'].items():
            if perf['total'] > 0:
                perf['primary_accuracy'] = perf['primary_correct'] / perf['total']
                perf['enhanced_accuracy'] = perf['primary_or_secondary_correct'] / perf['total']
        
        return analysis
    
    def _map_category_name_to_id(self, category_name: str) -> int:
        """映射类别名称到ID"""
        if not category_name:
            return None
            
        # 直接匹配
        if category_name in self.name_to_id_mapping:
            return self.name_to_id_mapping[category_name]
        
        # 模糊匹配
        category_lower = category_name.lower()
        for name, cat_id in self.name_to_id_mapping.items():
            if name.lower() in category_lower or category_lower in name.lower():
                return cat_id
        
        logger.warning(f"⚠️ 无法映射类别名称: {category_name}")
        return None

    def print_analysis_summary(self, analysis: dict):
        """打印分析总结"""
        logger.info("=" * 80)
        logger.info("📊 基于新标签的准确率分析总结")
        logger.info("=" * 80)

        # 整体统计
        total = analysis['total_videos']
        with_gt = analysis['videos_with_new_ground_truth']
        primary_total = analysis['primary_only_accuracy']['total']

        logger.info(f"📈 整体统计:")
        logger.info(f"  总视频数: {total}")
        logger.info(f"  有新标签的视频: {with_gt}")
        logger.info(f"  成功分析的视频: {primary_total}")

        # 准确率对比
        primary_acc = analysis['primary_only_accuracy']['accuracy']
        enhanced_acc = analysis['primary_or_secondary_accuracy']['accuracy']
        improvement = enhanced_acc - primary_acc

        logger.info(f"\n🎯 准确率对比:")
        logger.info(f"  仅主要类别: {primary_acc:.3f} ({analysis['primary_only_accuracy']['correct']}/{primary_total})")
        logger.info(f"  主要或次要类别: {enhanced_acc:.3f} ({analysis['primary_or_secondary_accuracy']['correct']}/{primary_total})")
        logger.info(f"  提升幅度: +{improvement:.3f} ({improvement*100:.1f} 个百分点)")

        # 各类别性能
        logger.info(f"\n📋 各类别性能:")
        logger.info(f"{'类别':<25} {'主要准确率':<12} {'增强准确率':<12} {'提升幅度':<12} {'样本数':<8}")
        logger.info("-" * 75)

        for cat_id, perf in analysis['category_performance'].items():
            cat_name = self.category_mapping.get(cat_id, f"Category_{cat_id}")
            primary_acc = perf['primary_accuracy']
            enhanced_acc = perf['enhanced_accuracy']
            improvement = enhanced_acc - primary_acc
            count = perf['total']

            logger.info(f"{cat_name:<25} {primary_acc:<12.3f} {enhanced_acc:<12.3f} {improvement:+<12.3f} {count:<8}")

        # 错误分析
        errors = analysis['error_analysis']
        if any(errors.values()):
            logger.info(f"\n❌ 错误分析:")
            logger.info(f"  无新标签: {errors['no_ground_truth']}")
            logger.info(f"  分类失败: {errors['classification_failed']}")
            logger.info(f"  类别映射失败: {errors['category_mapping_failed']}")

        logger.info("=" * 80)

    def save_detailed_report(self, analysis: dict, output_path: str):
        """保存详细分析报告"""
        logger.info(f"💾 保存详细报告到 {output_path}")

        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'data_source': 'new_labeling_data',
            'summary': {
                'total_videos': analysis['total_videos'],
                'videos_with_new_ground_truth': analysis['videos_with_new_ground_truth'],
                'primary_only_accuracy': analysis['primary_only_accuracy']['accuracy'],
                'primary_or_secondary_accuracy': analysis['primary_or_secondary_accuracy']['accuracy'],
                'accuracy_improvement': analysis['primary_or_secondary_accuracy']['accuracy'] - analysis['primary_only_accuracy']['accuracy']
            },
            'category_performance': {
                self.category_mapping.get(cat_id, f"Category_{cat_id}"): {
                    'primary_accuracy': perf['primary_accuracy'],
                    'enhanced_accuracy': perf['enhanced_accuracy'],
                    'improvement': perf['enhanced_accuracy'] - perf['primary_accuracy'],
                    'sample_count': perf['total']
                }
                for cat_id, perf in analysis['category_performance'].items()
            },
            'error_analysis': analysis['error_analysis'],
            'detailed_results': analysis['detailed_results']
        }

        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ 详细报告已保存: {output_path}")

def main():
    """主分析函数"""
    logger.info("🔍 开始基于新标签的准确率分析...")

    # 文件路径
    new_labels_file = "/data2/jevon/7_31_video_mashup/打标数据.xlsx"
    results_file = "reports/video_classification_results_enhanced.json"
    report_file = "reports/new_labels_accuracy_analysis_report.json"

    try:
        # 初始化分析器
        analyzer = NewLabelAccuracyAnalyzer()

        # 加载数据
        new_ground_truth = analyzer.load_new_ground_truth(new_labels_file)
        results = analyzer.load_classification_results(results_file)

        if not new_ground_truth:
            logger.error("❌ 无法加载新标签数据")
            return False

        if not results:
            logger.error("❌ 无法加载分类结果")
            return False

        # 分析准确率
        analysis = analyzer.analyze_accuracy_with_new_labels(results, new_ground_truth)

        # 打印总结
        analyzer.print_analysis_summary(analysis)

        # 保存详细报告
        analyzer.save_detailed_report(analysis, report_file)

        logger.info("✅ 基于新标签的准确率分析完成！")
        return True

    except Exception as e:
        logger.error(f"❌ 分析失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
