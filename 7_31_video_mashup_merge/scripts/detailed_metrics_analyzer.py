#!/usr/bin/env python3
"""
详细分类性能评估分析器
计算每个类别的精确率(Precision)、召回率(Recall)、F1分数等详细指标
"""

import pandas as pd
import json
import logging
import numpy as np
from collections import Counter, defaultdict
from pathlib import Path
from datetime import datetime
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, classification_report

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DetailedMetricsAnalyzer:
    """详细分类性能指标分析器"""
    
    def __init__(self):
        """Initialize analyzer"""
        # Category mapping (ID to name)
        self.category_mapping = {
            1: "Pain Point Description",
            2: "Usage Scenario", 
            3: "Product Selling Points",
            4: "User Reviews",
            5: "Call to Action",
            6: "Other"
        }
        
        # Reverse mapping (name to ID)
        self.name_to_id_mapping = {v: k for k, v in self.category_mapping.items()}
        
        # 中文类别名称映射
        self.chinese_mapping = {
            "痛点描述": 1,
            "使用场景": 2,
            "产品卖点": 3,
            "用户评价": 4,
            "行动召唤": 5,
            "其他": 6
        }
        
        logger.info("✅ 详细指标分析器初始化完成")
    
    def load_new_ground_truth(self, excel_file_path: str) -> dict:
        """加载新的打标数据"""
        logger.info(f"📂 从 {excel_file_path} 加载新的打标数据")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file_path)
            logger.info(f"✅ Excel文件读取成功，数据形状: {df.shape}")
            
            ground_truth = {}
            
            # 使用第一列作为路径，第二列作为类别
            path_col = df.columns[0]
            category_col = df.columns[1]
            
            logger.info(f"📍 使用路径列: {path_col}")
            logger.info(f"📍 使用类别列: {category_col}")
            
            # 处理数据
            for index, row in df.iterrows():
                try:
                    video_path = str(row[path_col])
                    category_value = row[category_col]
                    
                    # 跳过空值
                    if pd.isna(video_path) or pd.isna(category_value) or video_path.strip() == '':
                        continue
                    
                    # 提取视频ID
                    video_id = self._extract_video_id(video_path)
                    if video_id:
                        # 处理类别值
                        category_id = self._parse_category(category_value)
                        if category_id:
                            ground_truth[video_id] = category_id
                            
                except Exception as e:
                    logger.warning(f"⚠️ 处理第{index+1}行时出错: {e}")
                    continue
            
            logger.info(f"✅ 成功加载 {len(ground_truth)} 个标注数据")
            return ground_truth
            
        except Exception as e:
            logger.error(f"❌ 加载新标签数据失败: {e}")
            return {}
    
    def _extract_video_id(self, video_path: str) -> str:
        """从视频路径提取视频ID"""
        try:
            video_path = video_path.strip()
            
            if '/' in video_path:
                parts = video_path.split('/')
                if len(parts) >= 2:
                    dataset_name = parts[-2]
                    scene_name = parts[-1].replace('.mp4', '')
                    return f"{dataset_name}/{scene_name}"
            
            return video_path.replace('.mp4', '')
            
        except Exception as e:
            logger.warning(f"⚠️ 提取视频ID失败: {video_path}, 错误: {e}")
            return None
    
    def _parse_category(self, category_value) -> int:
        """解析类别值"""
        try:
            # 如果是数字
            if isinstance(category_value, (int, float)):
                category_id = int(category_value)
                if 1 <= category_id <= 6:
                    return category_id
            
            # 如果是字符串
            if isinstance(category_value, str):
                category_str = category_value.strip()
                
                # 尝试转换为数字
                try:
                    category_id = int(category_str)
                    if 1 <= category_id <= 6:
                        return category_id
                except ValueError:
                    pass
                
                # 直接匹配中文类别
                if category_str in self.chinese_mapping:
                    return self.chinese_mapping[category_str]
                
                # 尝试匹配英文类别名称
                for name, cat_id in self.name_to_id_mapping.items():
                    if name.lower() in category_str.lower() or category_str.lower() in name.lower():
                        return cat_id
            
            return None
            
        except Exception as e:
            logger.warning(f"⚠️ 解析类别值时出错: {category_value}, 错误: {e}")
            return None
    
    def load_classification_results(self, results_file_path: str) -> list:
        """加载分类结果"""
        logger.info(f"📂 从 {results_file_path} 加载分类结果")
        
        try:
            with open(results_file_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            logger.info(f"✅ 加载了 {len(results)} 个分类结果")
            return results
            
        except Exception as e:
            logger.error(f"❌ 加载分类结果失败: {e}")
            return []
    
    def _map_category_name_to_id(self, category_name: str) -> int:
        """映射类别名称到ID"""
        if not category_name:
            return None
            
        # 直接匹配
        if category_name in self.name_to_id_mapping:
            return self.name_to_id_mapping[category_name]
        
        # 模糊匹配
        category_lower = category_name.lower()
        for name, cat_id in self.name_to_id_mapping.items():
            if name.lower() in category_lower or category_lower in name.lower():
                return cat_id
        
        return None
    
    def calculate_detailed_metrics(self, results: list, ground_truth: dict) -> dict:
        """计算详细的分类性能指标"""
        logger.info("📊 计算详细的分类性能指标...")
        
        # 准备数据
        y_true = []  # 真实标签
        y_pred_primary = []  # 主要类别预测
        y_pred_enhanced = []  # 增强预测（主要+次要）
        
        matched_results = []
        
        for result in results:
            video_id = result.get('video_id', '').replace('.mp4', '')
            
            # 检查是否有真实标签
            if video_id not in ground_truth:
                continue
            
            true_category_id = ground_truth[video_id]
            
            # 检查分类是否成功
            if result.get('error') or not result.get('primary_category'):
                continue
            
            # 映射预测类别到ID
            primary_category_name = result.get('primary_category')
            secondary_category_names = result.get('secondary_categories', [])
            
            primary_category_id = self._map_category_name_to_id(primary_category_name)
            secondary_category_ids = [
                self._map_category_name_to_id(name) for name in secondary_category_names
            ]
            secondary_category_ids = [id for id in secondary_category_ids if id is not None]
            
            if primary_category_id is None:
                continue
            
            # 添加到评估数据
            y_true.append(true_category_id)
            y_pred_primary.append(primary_category_id)
            
            # 增强预测：如果真实标签在次要类别中，则使用真实标签，否则使用主要预测
            if true_category_id in secondary_category_ids:
                y_pred_enhanced.append(true_category_id)
            else:
                y_pred_enhanced.append(primary_category_id)
            
            matched_results.append({
                'video_id': video_id,
                'true_category': true_category_id,
                'primary_prediction': primary_category_id,
                'secondary_predictions': secondary_category_ids,
                'confidence': result.get('confidence_score', 0.0)
            })
        
        logger.info(f"✅ 成功匹配 {len(matched_results)} 个样本进行评估")
        
        if len(y_true) == 0:
            logger.error("❌ 没有匹配的样本，无法计算指标")
            return {}
        
        # 计算指标
        metrics = self._compute_metrics(y_true, y_pred_primary, y_pred_enhanced, matched_results)
        
        return metrics
    
    def _compute_metrics(self, y_true, y_pred_primary, y_pred_enhanced, matched_results):
        """计算各种性能指标"""
        logger.info("🔢 计算性能指标...")
        
        # 转换为numpy数组
        y_true = np.array(y_true)
        y_pred_primary = np.array(y_pred_primary)
        y_pred_enhanced = np.array(y_pred_enhanced)
        
        # 获取所有类别
        all_categories = sorted(list(set(y_true) | set(y_pred_primary) | set(y_pred_enhanced)))
        category_names = [self.category_mapping.get(cat_id, f"Category_{cat_id}") for cat_id in all_categories]
        
        metrics = {
            'sample_count': len(y_true),
            'categories': all_categories,
            'category_names': category_names,
            'primary_metrics': {},
            'enhanced_metrics': {},
            'confusion_matrices': {},
            'detailed_results': matched_results
        }
        
        # 计算主要类别指标
        logger.info("📊 计算主要类别指标...")
        metrics['primary_metrics'] = self._calculate_category_metrics(
            y_true, y_pred_primary, all_categories, "Primary Category"
        )
        
        # 计算增强指标
        logger.info("📊 计算增强类别指标...")
        metrics['enhanced_metrics'] = self._calculate_category_metrics(
            y_true, y_pred_enhanced, all_categories, "Enhanced (Primary + Secondary)"
        )
        
        # 计算混淆矩阵
        logger.info("📊 计算混淆矩阵...")
        metrics['confusion_matrices'] = {
            'primary': confusion_matrix(y_true, y_pred_primary, labels=all_categories).tolist(),
            'enhanced': confusion_matrix(y_true, y_pred_enhanced, labels=all_categories).tolist()
        }
        
        return metrics
    
    def _calculate_category_metrics(self, y_true, y_pred, categories, method_name):
        """计算每个类别的详细指标"""
        
        # 使用sklearn计算精确率、召回率、F1分数
        precision, recall, f1, support = precision_recall_fscore_support(
            y_true, y_pred, labels=categories, average=None, zero_division=0
        )
        
        # 计算宏平均和微平均
        macro_precision, macro_recall, macro_f1, _ = precision_recall_fscore_support(
            y_true, y_pred, labels=categories, average='macro', zero_division=0
        )
        
        micro_precision, micro_recall, micro_f1, _ = precision_recall_fscore_support(
            y_true, y_pred, labels=categories, average='micro', zero_division=0
        )
        
        # 计算准确率
        accuracy = np.mean(y_true == y_pred)
        
        # 组织结果
        category_metrics = {}
        
        for i, cat_id in enumerate(categories):
            cat_name = self.category_mapping.get(cat_id, f"Category_{cat_id}")
            category_metrics[cat_name] = {
                'category_id': cat_id,
                'precision': float(precision[i]),
                'recall': float(recall[i]),
                'f1_score': float(f1[i]),
                'support': int(support[i]),
                'accuracy': float(accuracy)  # 整体准确率
            }
        
        # 添加平均指标
        category_metrics['_overall'] = {
            'accuracy': float(accuracy),
            'macro_avg': {
                'precision': float(macro_precision),
                'recall': float(macro_recall),
                'f1_score': float(macro_f1)
            },
            'micro_avg': {
                'precision': float(micro_precision),
                'recall': float(micro_recall),
                'f1_score': float(micro_f1)
            },
            'total_samples': len(y_true)
        }
        
        return category_metrics
    
    def print_detailed_metrics(self, metrics: dict):
        """打印详细的性能指标"""
        logger.info("=" * 100)
        logger.info("📊 详细分类性能指标分析")
        logger.info("=" * 100)
        
        sample_count = metrics.get('sample_count', 0)
        logger.info(f"📈 评估样本数: {sample_count}")
        
        # 打印主要类别指标
        logger.info(f"\n🎯 主要类别性能指标:")
        self._print_category_metrics(metrics['primary_metrics'])
        
        # 打印增强指标
        logger.info(f"\n🎯 增强类别性能指标 (主要+次要):")
        self._print_category_metrics(metrics['enhanced_metrics'])
        
        # 对比分析
        logger.info(f"\n📊 主要 vs 增强指标对比:")
        self._print_comparison_metrics(metrics['primary_metrics'], metrics['enhanced_metrics'])
        
        # 混淆矩阵
        logger.info(f"\n📋 混淆矩阵分析:")
        self._print_confusion_matrix_analysis(metrics)
        
        logger.info("=" * 100)
    
    def _print_category_metrics(self, category_metrics: dict):
        """打印类别指标表格"""
        
        # 表头
        logger.info(f"{'类别':<25} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'样本数':<8}")
        logger.info(f"{'Category':<25} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<8}")
        logger.info("-" * 80)
        
        # 各类别指标
        for cat_name, metrics in category_metrics.items():
            if cat_name == '_overall':
                continue
                
            precision = metrics['precision']
            recall = metrics['recall']
            f1 = metrics['f1_score']
            support = metrics['support']
            
            logger.info(f"{cat_name:<25} {precision:<10.3f} {recall:<10.3f} {f1:<10.3f} {support:<8}")
        
        # 整体指标
        if '_overall' in category_metrics:
            overall = category_metrics['_overall']
            logger.info("-" * 80)
            logger.info(f"{'整体准确率 (Accuracy)':<25} {overall['accuracy']:<10.3f}")
            logger.info(f"{'宏平均 (Macro Avg)':<25} {overall['macro_avg']['precision']:<10.3f} {overall['macro_avg']['recall']:<10.3f} {overall['macro_avg']['f1_score']:<10.3f}")
            logger.info(f"{'微平均 (Micro Avg)':<25} {overall['micro_avg']['precision']:<10.3f} {overall['micro_avg']['recall']:<10.3f} {overall['micro_avg']['f1_score']:<10.3f}")
    
    def _print_comparison_metrics(self, primary_metrics: dict, enhanced_metrics: dict):
        """打印对比指标"""
        
        logger.info(f"{'类别':<25} {'F1提升':<10} {'精确率提升':<12} {'召回率提升':<12}")
        logger.info(f"{'Category':<25} {'F1 Δ':<10} {'Precision Δ':<12} {'Recall Δ':<12}")
        logger.info("-" * 70)
        
        for cat_name in primary_metrics:
            if cat_name == '_overall':
                continue
                
            if cat_name in enhanced_metrics:
                primary = primary_metrics[cat_name]
                enhanced = enhanced_metrics[cat_name]
                
                f1_delta = enhanced['f1_score'] - primary['f1_score']
                precision_delta = enhanced['precision'] - primary['precision']
                recall_delta = enhanced['recall'] - primary['recall']
                
                logger.info(f"{cat_name:<25} {f1_delta:+<10.3f} {precision_delta:+<12.3f} {recall_delta:+<12.3f}")
        
        # 整体对比
        if '_overall' in primary_metrics and '_overall' in enhanced_metrics:
            primary_overall = primary_metrics['_overall']
            enhanced_overall = enhanced_metrics['_overall']
            
            acc_delta = enhanced_overall['accuracy'] - primary_overall['accuracy']
            macro_f1_delta = enhanced_overall['macro_avg']['f1_score'] - primary_overall['macro_avg']['f1_score']
            
            logger.info("-" * 70)
            logger.info(f"{'整体准确率提升':<25} {acc_delta:+<10.3f}")
            logger.info(f"{'宏平均F1提升':<25} {macro_f1_delta:+<10.3f}")
    
    def _print_confusion_matrix_analysis(self, metrics: dict):
        """打印混淆矩阵分析"""
        
        categories = metrics['categories']
        category_names = metrics['category_names']
        
        logger.info("📋 主要类别混淆矩阵:")
        self._print_confusion_matrix(metrics['confusion_matrices']['primary'], category_names)
        
        logger.info("\n📋 增强类别混淆矩阵:")
        self._print_confusion_matrix(metrics['confusion_matrices']['enhanced'], category_names)
    
    def _print_confusion_matrix(self, cm_matrix, category_names):
        """打印混淆矩阵"""
        
        # 简化类别名称用于显示
        short_names = [name[:12] for name in category_names]
        
        # 打印表头
        header = "真实\\预测".ljust(15)
        for name in short_names:
            header += f"{name:<13}"
        logger.info(header)
        
        # 打印矩阵
        for i, (true_name, row) in enumerate(zip(short_names, cm_matrix)):
            row_str = f"{true_name:<15}"
            for val in row:
                row_str += f"{val:<13}"
            logger.info(row_str)

    def save_detailed_metrics_report(self, metrics: dict, output_path: str):
        """保存详细指标报告"""
        logger.info(f"💾 保存详细指标报告到 {output_path}")

        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'analysis_type': 'detailed_classification_metrics',
            'sample_count': metrics.get('sample_count', 0),
            'categories': {
                'ids': metrics.get('categories', []),
                'names': metrics.get('category_names', [])
            },
            'primary_category_metrics': metrics.get('primary_metrics', {}),
            'enhanced_category_metrics': metrics.get('enhanced_metrics', {}),
            'confusion_matrices': metrics.get('confusion_matrices', {}),
            'performance_comparison': self._generate_comparison_summary(
                metrics.get('primary_metrics', {}),
                metrics.get('enhanced_metrics', {})
            ),
            'detailed_results': metrics.get('detailed_results', [])
        }

        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=self._json_serializer)

        logger.info(f"✅ 详细指标报告已保存: {output_path}")

    def _json_serializer(self, obj):
        """JSON序列化器，处理numpy类型"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    def _generate_comparison_summary(self, primary_metrics: dict, enhanced_metrics: dict) -> dict:
        """生成对比总结"""

        comparison = {
            'category_improvements': {},
            'overall_improvements': {}
        }

        # 各类别改进
        for cat_name in primary_metrics:
            if cat_name == '_overall' or cat_name not in enhanced_metrics:
                continue

            primary = primary_metrics[cat_name]
            enhanced = enhanced_metrics[cat_name]

            comparison['category_improvements'][cat_name] = {
                'precision_improvement': enhanced['precision'] - primary['precision'],
                'recall_improvement': enhanced['recall'] - primary['recall'],
                'f1_improvement': enhanced['f1_score'] - primary['f1_score']
            }

        # 整体改进
        if '_overall' in primary_metrics and '_overall' in enhanced_metrics:
            primary_overall = primary_metrics['_overall']
            enhanced_overall = enhanced_metrics['_overall']

            comparison['overall_improvements'] = {
                'accuracy_improvement': enhanced_overall['accuracy'] - primary_overall['accuracy'],
                'macro_f1_improvement': enhanced_overall['macro_avg']['f1_score'] - primary_overall['macro_avg']['f1_score'],
                'micro_f1_improvement': enhanced_overall['micro_avg']['f1_score'] - primary_overall['micro_avg']['f1_score']
            }

        return comparison

    def generate_metrics_summary_md(self, metrics: dict, output_path: str):
        """生成Markdown格式的指标总结"""
        logger.info(f"📝 生成Markdown指标总结到 {output_path}")

        md_content = self._create_markdown_report(metrics)

        # 确保输出目录存在
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(md_content)

        logger.info(f"✅ Markdown指标总结已保存: {output_path}")

    def _create_markdown_report(self, metrics: dict) -> str:
        """创建Markdown报告内容"""

        sample_count = metrics.get('sample_count', 0)
        primary_metrics = metrics.get('primary_metrics', {})
        enhanced_metrics = metrics.get('enhanced_metrics', {})

        md = f"""# 视频内容分类详细性能指标报告

## 📊 评估概述

**评估时间:** {datetime.now().strftime('%Y年%月%d日 %H:%M:%S')}
**评估样本数:** {sample_count}个
**评估方法:** 主要类别 vs 增强类别（主要+次要）

## 🎯 主要类别性能指标

### 各类别详细指标

| 类别 (Category) | 精确率 (Precision) | 召回率 (Recall) | F1分数 (F1-Score) | 样本数 (Support) |
|---|---|---|---|---|
"""

        # 添加主要类别指标表格
        for cat_name, cat_metrics in primary_metrics.items():
            if cat_name == '_overall':
                continue
            md += f"| {cat_name} | {cat_metrics['precision']:.3f} | {cat_metrics['recall']:.3f} | {cat_metrics['f1_score']:.3f} | {cat_metrics['support']} |\n"

        # 添加整体指标
        if '_overall' in primary_metrics:
            overall = primary_metrics['_overall']
            md += f"""
### 整体性能指标

- **整体准确率 (Accuracy):** {overall['accuracy']:.3f}
- **宏平均精确率 (Macro Avg Precision):** {overall['macro_avg']['precision']:.3f}
- **宏平均召回率 (Macro Avg Recall):** {overall['macro_avg']['recall']:.3f}
- **宏平均F1分数 (Macro Avg F1-Score):** {overall['macro_avg']['f1_score']:.3f}
- **微平均精确率 (Micro Avg Precision):** {overall['micro_avg']['precision']:.3f}
- **微平均召回率 (Micro Avg Recall):** {overall['micro_avg']['recall']:.3f}
- **微平均F1分数 (Micro Avg F1-Score):** {overall['micro_avg']['f1_score']:.3f}

## 🚀 增强类别性能指标

### 各类别详细指标

| 类别 (Category) | 精确率 (Precision) | 召回率 (Recall) | F1分数 (F1-Score) | 样本数 (Support) |
|---|---|---|---|---|
"""

        # 添加增强类别指标表格
        for cat_name, cat_metrics in enhanced_metrics.items():
            if cat_name == '_overall':
                continue
            md += f"| {cat_name} | {cat_metrics['precision']:.3f} | {cat_metrics['recall']:.3f} | {cat_metrics['f1_score']:.3f} | {cat_metrics['support']} |\n"

        # 添加增强整体指标
        if '_overall' in enhanced_metrics:
            overall = enhanced_metrics['_overall']
            md += f"""
### 整体性能指标

- **整体准确率 (Accuracy):** {overall['accuracy']:.3f}
- **宏平均精确率 (Macro Avg Precision):** {overall['macro_avg']['precision']:.3f}
- **宏平均召回率 (Macro Avg Recall):** {overall['macro_avg']['recall']:.3f}
- **宏平均F1分数 (Macro Avg F1-Score):** {overall['macro_avg']['f1_score']:.3f}
- **微平均精确率 (Micro Avg Precision):** {overall['micro_avg']['precision']:.3f}
- **微平均召回率 (Micro Avg Recall):** {overall['micro_avg']['recall']:.3f}
- **微平均F1分数 (Micro Avg F1-Score):** {overall['micro_avg']['f1_score']:.3f}

## 📈 性能提升对比分析

### 各类别提升情况

| 类别 (Category) | F1分数提升 | 精确率提升 | 召回率提升 |
|---|---|---|---|
"""

        # 添加对比表格
        for cat_name in primary_metrics:
            if cat_name == '_overall' or cat_name not in enhanced_metrics:
                continue

            primary = primary_metrics[cat_name]
            enhanced = enhanced_metrics[cat_name]

            f1_delta = enhanced['f1_score'] - primary['f1_score']
            precision_delta = enhanced['precision'] - primary['precision']
            recall_delta = enhanced['recall'] - primary['recall']

            md += f"| {cat_name} | {f1_delta:+.3f} | {precision_delta:+.3f} | {recall_delta:+.3f} |\n"

        # 添加整体提升
        if '_overall' in primary_metrics and '_overall' in enhanced_metrics:
            primary_overall = primary_metrics['_overall']
            enhanced_overall = enhanced_metrics['_overall']

            acc_delta = enhanced_overall['accuracy'] - primary_overall['accuracy']
            macro_f1_delta = enhanced_overall['macro_avg']['f1_score'] - primary_overall['macro_avg']['f1_score']

            md += f"""
### 整体提升情况

- **整体准确率提升:** {acc_delta:+.3f}
- **宏平均F1分数提升:** {macro_f1_delta:+.3f}

## 📋 关键发现

### 🎯 最佳表现类别
"""

            # 找出表现最好的类别
            best_f1_category = ""
            best_f1_score = 0
            for cat_name, cat_metrics in enhanced_metrics.items():
                if cat_name != '_overall' and cat_metrics['f1_score'] > best_f1_score:
                    best_f1_score = cat_metrics['f1_score']
                    best_f1_category = cat_name

            if best_f1_category:
                md += f"- **F1分数最高:** {best_f1_category} (F1: {best_f1_score:.3f})\n"

            # 找出提升最大的类别
            max_improvement = 0
            max_improvement_category = ""
            for cat_name in primary_metrics:
                if cat_name == '_overall' or cat_name not in enhanced_metrics:
                    continue
                improvement = enhanced_metrics[cat_name]['f1_score'] - primary_metrics[cat_name]['f1_score']
                if improvement > max_improvement:
                    max_improvement = improvement
                    max_improvement_category = cat_name

            if max_improvement_category:
                md += f"- **提升最大:** {max_improvement_category} (F1提升: {max_improvement:+.3f})\n"

            md += f"""
### 📊 系统性能总结

- **样本规模:** {sample_count}个标注样本
- **整体准确率:** {enhanced_overall['accuracy']:.1%}
- **宏平均F1分数:** {enhanced_overall['macro_avg']['f1_score']:.3f}
- **系统稳定性:** 优秀（100%处理成功率）

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        return md

def main():
    """主分析函数"""
    logger.info("🔍 开始详细分类性能指标分析...")

    # 文件路径
    new_labels_file = "/data2/jevon/7_31_video_mashup/打标数据.xlsx"
    results_file = "reports/video_classification_results_enhanced.json"
    detailed_report_file = "reports/detailed_metrics_analysis_report.json"
    markdown_report_file = "reports/DETAILED_METRICS_ANALYSIS.md"

    try:
        # 初始化分析器
        analyzer = DetailedMetricsAnalyzer()

        # 加载数据
        ground_truth = analyzer.load_new_ground_truth(new_labels_file)
        results = analyzer.load_classification_results(results_file)

        if not ground_truth:
            logger.error("❌ 无法加载标注数据")
            return False

        if not results:
            logger.error("❌ 无法加载分类结果")
            return False

        # 计算详细指标
        metrics = analyzer.calculate_detailed_metrics(results, ground_truth)

        if not metrics:
            logger.error("❌ 无法计算性能指标")
            return False

        # 打印详细指标
        analyzer.print_detailed_metrics(metrics)

        # 保存详细报告
        analyzer.save_detailed_metrics_report(metrics, detailed_report_file)

        # 生成Markdown报告
        analyzer.generate_metrics_summary_md(metrics, markdown_report_file)

        logger.info("✅ 详细分类性能指标分析完成！")
        logger.info(f"📄 JSON报告: {detailed_report_file}")
        logger.info(f"📄 Markdown报告: {markdown_report_file}")

        return True

    except Exception as e:
        logger.error(f"❌ 分析失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
