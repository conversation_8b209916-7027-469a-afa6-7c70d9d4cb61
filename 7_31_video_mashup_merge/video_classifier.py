#!/usr/bin/env python3
"""
Enhanced Video Content Classification Script
使用原始提示词逻辑 + 增强的输出格式 + 改进的成功率统计
"""

import json
import requests
import time
import logging
import sys
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from datetime import datetime
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('classification_enhanced.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class EnhancedClassificationResult:
    """Enhanced classification result data class"""
    video_id: str
    dataset_name: str
    primary_category: Optional[str]
    secondary_categories: List[str]
    specific_tags: List[str]
    confidence_score: float
    reasoning: str
    error: Optional[str] = None

class VideoContentClassifierEnhanced:
    """Video content classifier using original prompt logic with enhanced output format"""
    
    def __init__(self):
        """Initialize classifier with user's preferred settings"""
        # API settings using user's preferred DeepSeek configuration
        self.api_url = "https://api.siliconflow.cn/v1/chat/completions"
        self.api_token = "sk-iinwlldkziyjemchdrzmwuppvmjcdjemdxhjlompizouxbnl"
        self.model = "Pro/deepseek-ai/DeepSeek-V3"
        self.max_retries = 3
        self.max_workers = 3
        self.timeout = 240
        
        # Load original prompt template with enhanced output format
        self.classification_prompt = self._get_enhanced_prompt()
        self.system_message = "You are an expert in video content classification for footwear marketing."
        
        logger.info(f"✅ Enhanced classifier initialized with original prompt logic")
    
    def _get_enhanced_prompt(self) -> str:
        """Get enhanced prompt with original logic but improved output format"""
        return """## Task Description
You are an expert in classifying e-commerce shoe video content based on visual descriptions, audio transcripts (ASR). Your task is to analyze multi-modal input and categorize it according to the primary focus.

## Input Dictionary Structure
Each input will be a dict with three fields:
- description: [List of visual descriptions from video frames]
- asr: [List of audio transcript segments, may be in foreign languages]

## Category System
### 1. Pain Point Description
Key differentiator: Shows product shortcomings, such as Comfort Issues ,Performance Failures and Purchase Anxiety. 
- Visual: Foot discomfort evidence (blisters, redness), Product failure demonstrations (leaking, sole detachment), Comparative negatives (side-by-side with competitor)
- Audio: Discomfort references ("rubbing", "uncomfortable", "too stiff"), Problem descriptions ("water gets in", "traction issues")

### 2. Usage Scenario
Key differentiator: Neutral environment showcase, such as Office environments, Lifestyle, Activity-Specific.  
- Visual: Environmental shots >50% duration (urban, trail, office), Natural movement without technical focus
- Audio: Setting descriptions ("walking downtown", "at the gym"), Activity narration ("commuting in", "weekend wear")

### 3. Product Selling Points
Key differentiator: feature highlights, such as Technical Features, Design Excellence,Functional Benefits.
- Visual: close-ups (stitching, sole tech), Function tests (water droplets, flex demonstrations)
- Audio: Comfortable craftsmanship ("Soft sole"), Performance claims ("breathable", "shock absorbing")

### 4. User Reviews
Key differentiator: First-person perspective content
- Visual: Handheld POV footage, Unboxing sequences, Mirror selfie angles
- Audio: Personal feedback ("I love these shoes", "They fit perfectly"), User experiences ("I wear them daily")

### 5. Call to Action
Key differentiator: Clear conversion intent*
- Visual:  CTAs covering >20% frame, Time-sensitive graphics ("FLASH SALE")
- Audio: Urgency cues ("limited stock", "today only"), Direct prompts ("click link", "order now")

### 6. Other
Default for non-standard content:
- Visual: Conceptual imagery (floating shoes), Pure branding (logos without product)
- Audio: Non-product narration (brand history)

## Classification Instructions
Please classify the following dict:
Target dict: {dict}

## Output Format
Please output the classification result in the following JSON format:
{{
  "primary_category": "Primary Category Name",
  "secondary_categories": ["Secondary Category Name 1", "Secondary Category Name 2"],
  "specific_tags": ["Specific Tag 1", "Specific Tag 2", "Specific Tag 3"],
  "confidence_score": 0.95,
  "reasoning": "Explanation of classification basis and keyword matching"
}}

## Classification Requirements
- Accuracy first: Classify based on the actual meaning of the text, avoid over-interpretation,Clear category should ≥2 matching indicators
- Keyword matching: Prioritize matching specific keywords and descriptions in the category system
- Context understanding: Consider the overall context and intent of the text
- Open handling: For content not fully matching existing categories, classify as "Other" and explain specific features in reasoning
- Label system evolution: If important new content patterns are found, mark "suggest adding new label type" in reasoning
- Secondary categories: Include up to 2 secondary categories that also match the content
- Specific tags: Provide 3-5 specific descriptive tags based on the content
- Confidence score: Rate your confidence in the primary classification from 0.0 to 1.0"""
    
    def _call_llm_api(self, content: str) -> Optional[Dict]:
        """Call LLM API with robust error handling"""
        headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": self.system_message},
                {"role": "user", "content": content}
            ],
            "temperature": 0.1,
            "max_tokens": 1000
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    self.api_url,
                    headers=headers,
                    json=payload,
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.warning(f"API call failed (status {response.status_code}): {response.text}")
                    
            except Exception as e:
                logger.warning(f"API call exception (attempt {attempt + 1}/{self.max_retries}): {e}")
                
            if attempt < self.max_retries - 1:
                delay = 2 ** attempt  # Exponential backoff
                time.sleep(min(delay, 60))
        
        return None
    
    def _parse_classification_response(self, response_text: str) -> Tuple[Optional[str], List[str], List[str], float, str]:
        """Parse enhanced classification response"""
        try:
            # Extract JSON part
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if not json_match:
                return None, [], [], 0.0, "No JSON found in response"
            
            json_text = json_match.group()
            result = json.loads(json_text)
            
            # Parse main fields according to enhanced format
            primary_category = result.get("primary_category", "")
            secondary_categories = result.get("secondary_categories", [])
            specific_tags = result.get("specific_tags", [])
            confidence_score = float(result.get("confidence_score", 0.0))
            reasoning = result.get("reasoning", "")
            
            # Ensure data types are correct
            if not isinstance(secondary_categories, list):
                secondary_categories = []
            if not isinstance(specific_tags, list):
                specific_tags = []
            
            return primary_category, secondary_categories, specific_tags, confidence_score, reasoning
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse classification response: {e}")
            return None, [], [], 0.0, f"Parse error: {str(e)}"
    
    def classify_single_video(self, video_data: Dict) -> EnhancedClassificationResult:
        """Classify single video using enhanced format"""
        start_time = time.time()
        video_id = f"{video_data['dataset_name']}/{video_data['video_name']}"
        
        try:
            # Extract content for classification - format according to original prompt
            description = video_data.get('video_description', {}).get('description', '')
            asr = video_data.get('asr', [])
            
            # Format input dict according to original prompt requirements
            input_dict = {
                "description": [description] if isinstance(description, str) else description,
                "asr": asr
            }
            
            # Format prompt with the input dict
            formatted_prompt = self.classification_prompt.replace("{dict}", str(input_dict))
            
            # Call API
            response = self._call_llm_api(formatted_prompt)
            
            if not response:
                return EnhancedClassificationResult(
                    video_id=video_id,
                    dataset_name=video_data['dataset_name'],
                    primary_category=None,
                    secondary_categories=[],
                    specific_tags=[],
                    confidence_score=0.0,
                    reasoning="",
                    error="API call failed"
                )
            
            # Parse response
            try:
                content = response["choices"][0]["message"]["content"]
                primary_cat, secondary_cats, tags, confidence, reasoning = self._parse_classification_response(content)
                
                return EnhancedClassificationResult(
                    video_id=video_id,
                    dataset_name=video_data['dataset_name'],
                    primary_category=primary_cat,
                    secondary_categories=secondary_cats,
                    specific_tags=tags,
                    confidence_score=confidence if confidence > 0 else (1.0 if primary_cat else 0.0),
                    reasoning=reasoning,
                    error=None
                )
                
            except (KeyError, IndexError) as e:
                return EnhancedClassificationResult(
                    video_id=video_id,
                    dataset_name=video_data['dataset_name'],
                    primary_category=None,
                    secondary_categories=[],
                    specific_tags=[],
                    confidence_score=0.0,
                    reasoning="",
                    error=f"Response parsing error: {str(e)}"
                )
                
        except Exception as e:
            return EnhancedClassificationResult(
                video_id=video_id,
                dataset_name=video_data['dataset_name'],
                primary_category=None,
                secondary_categories=[],
                specific_tags=[],
                confidence_score=0.0,
                reasoning="",
                error=f"Classification exception: {str(e)}"
            )

    def classify_batch(self, video_data_list: List[Dict]) -> List[EnhancedClassificationResult]:
        """Batch classify videos"""
        logger.info(f"🚀 Starting batch classification of {len(video_data_list)} videos using enhanced format...")

        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit tasks
            future_to_video = {
                executor.submit(self.classify_single_video, video_data): video_data
                for video_data in video_data_list
            }

            # Collect results
            for future in as_completed(future_to_video):
                try:
                    result = future.result()
                    results.append(result)

                    if len(results) % 10 == 0:
                        logger.info(f"Completed {len(results)}/{len(video_data_list)} videos")

                except Exception as e:
                    video_data = future_to_video[future]
                    video_id = f"{video_data['dataset_name']}/{video_data['video_name']}"
                    logger.error(f"Video {video_id} processing failed: {e}")
                    results.append(EnhancedClassificationResult(
                        video_id=video_id,
                        dataset_name=video_data['dataset_name'],
                        primary_category=None,
                        secondary_categories=[],
                        specific_tags=[],
                        confidence_score=0.0,
                        reasoning="",
                        error=f"Processing exception: {str(e)}"
                    ))

        logger.info(f"✅ Batch classification completed: {len(results)} results")
        return results

def load_merged_video_data(file_path: str) -> List[Dict]:
    """Load and flatten merged video data"""
    logger.info(f"📂 Loading merged video data from {file_path}")

    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Flatten the nested structure
    video_list = []
    for dataset_name, videos in data.items():
        for video_name, video_data in videos.items():
            video_data['dataset_name'] = dataset_name
            video_data['video_name'] = video_name
            video_list.append(video_data)

    logger.info(f"✅ Loaded {len(video_list)} videos from {len(data)} datasets")
    return video_list

def save_classification_results(results: List[EnhancedClassificationResult], output_path: str):
    """Save classification results to JSON file"""
    logger.info(f"💾 Saving enhanced classification results to {output_path}")

    # Convert results to dictionary format (enhanced format)
    formatted_results = []
    for result in results:
        formatted_results.append({
            "video_id": result.video_id,
            "dataset_name": result.dataset_name,
            "primary_category": result.primary_category,
            "secondary_categories": result.secondary_categories,
            "specific_tags": result.specific_tags,
            "confidence_score": result.confidence_score,
            "reasoning": result.reasoning,
            "error": result.error
        })

    # Ensure output directory exists
    Path(output_path).parent.mkdir(parents=True, exist_ok=True)

    # Save to file
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(formatted_results, f, ensure_ascii=False, indent=2)

    logger.info(f"✅ Enhanced classification results saved: {output_path}")

def print_enhanced_classification_summary(results: List[EnhancedClassificationResult]):
    """Print enhanced classification summary with primary + secondary success rate"""
    logger.info("📊 Enhanced Classification Summary:")

    total_videos = len(results)
    successful_classifications = sum(1 for r in results if r.primary_category and not r.error)
    failed_classifications = total_videos - successful_classifications

    logger.info(f"  Total videos: {total_videos}")
    logger.info(f"  Successful classifications: {successful_classifications}")
    logger.info(f"  Failed classifications: {failed_classifications}")

    if successful_classifications > 0:
        # Primary category distribution
        primary_category_counts = {}
        for result in results:
            if result.primary_category and not result.error:
                primary_category_counts[result.primary_category] = primary_category_counts.get(result.primary_category, 0) + 1

        logger.info("  Primary category distribution:")
        for category, count in sorted(primary_category_counts.items()):
            percentage = (count / successful_classifications) * 100
            logger.info(f"    {category}: {count} ({percentage:.1f}%)")

        # Secondary category analysis
        secondary_category_counts = {}
        total_secondary = 0
        for result in results:
            if result.secondary_categories and not result.error:
                for sec_cat in result.secondary_categories:
                    secondary_category_counts[sec_cat] = secondary_category_counts.get(sec_cat, 0) + 1
                    total_secondary += 1

        if secondary_category_counts:
            logger.info("  Secondary category distribution:")
            for category, count in sorted(secondary_category_counts.items(), key=lambda x: x[1], reverse=True):
                logger.info(f"    {category}: {count} occurrences")

        # Confidence analysis
        confidence_scores = [r.confidence_score for r in results if r.confidence_score > 0 and not r.error]
        if confidence_scores:
            avg_confidence = sum(confidence_scores) / len(confidence_scores)
            high_confidence = sum(1 for c in confidence_scores if c >= 0.8)
            medium_confidence = sum(1 for c in confidence_scores if 0.5 <= c < 0.8)
            low_confidence = sum(1 for c in confidence_scores if c < 0.5)

            logger.info("  Confidence analysis:")
            logger.info(f"    Average confidence: {avg_confidence:.3f}")
            logger.info(f"    High confidence (≥0.8): {high_confidence} videos")
            logger.info(f"    Medium confidence (0.5-0.8): {medium_confidence} videos")
            logger.info(f"    Low confidence (<0.5): {low_confidence} videos")

    # Error summary
    if failed_classifications > 0:
        error_types = {}
        for result in results:
            if result.error:
                error_types[result.error] = error_types.get(result.error, 0) + 1

        logger.info("  Error types:")
        for error, count in sorted(error_types.items()):
            logger.info(f"    {error}: {count}")

def main():
    """Main function"""
    start_time = time.time()

    logger.info("=" * 80)
    logger.info("🎯 Enhanced Video Content Classification System")
    logger.info("📦 Version: 1.0.0 - Original Logic + Enhanced Format")
    logger.info(f"🕒 Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 80)

    try:
        # File paths - using reports directory
        input_file = "data/merged_video_data.json"
        output_dir = "reports"
        output_file = f"{output_dir}/video_classification_results_enhanced.json"

        # 1. Load merged video data
        video_data_list = load_merged_video_data(input_file)

        # 2. Initialize classifier
        logger.info("🤖 Initializing enhanced video content classifier...")
        classifier = VideoContentClassifierEnhanced()

        # 3. Perform classification
        logger.info("🔍 Starting enhanced video content classification...")
        classification_results = classifier.classify_batch(video_data_list)

        # 4. Save results
        save_classification_results(classification_results, output_file)

        # 5. Print summary
        print_enhanced_classification_summary(classification_results)

        # 6. Calculate total time
        total_time = time.time() - start_time
        logger.info(f"⏱️ Total processing time: {total_time:.2f} seconds")

        logger.info("✅ Enhanced video content classification completed successfully!")
        logger.info(f"📁 Results saved to: {output_dir}/")
        return True

    except Exception as e:
        logger.error(f"❌ System failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ User interrupted the program")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Program exited with exception: {e}")
        sys.exit(1)
