#!/usr/bin/env python3
"""
端到端功能测试 - 测试实际的Prompt优化功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wan22_functionality():
    """测试Wan2.2的实际功能"""
    print("🎬 测试Wan2.2实际功能...")
    
    try:
        from prompt_extend import WanPromptExpander
        
        # 测试T2V模式
        print("\n📹 测试T2V模式实际调用...")
        t2v_expander = WanPromptExpander(
            api_key="sk-test",  # 使用测试密钥，预期会失败但能测试逻辑
            model_name="wan2.2",
            mode="T2V"
        )
        
        test_prompt = "一只可爱的小猫在花园里玩耍"
        print(f"测试输入: {test_prompt}")
        
        # 获取系统提示词
        system_prompt = t2v_expander.decide_system_prompt("zh")
        print(f"✅ 系统提示词获取成功: {len(system_prompt)} 字符")
        
        # 验证系统提示词内容
        key_elements = ["电影导演", "电影元素", "光源", "镜头尺寸"]
        found_elements = [elem for elem in key_elements if elem in system_prompt]
        print(f"✅ 包含关键元素: {found_elements}")
        
        # 测试I2V模式
        print("\n🖼️ 测试I2V模式...")
        i2v_expander = WanPromptExpander(
            api_key="sk-test",
            model_name="wan2.2", 
            mode="I2V"
        )
        
        i2v_system_prompt = i2v_expander.decide_system_prompt("zh")
        print(f"✅ I2V系统提示词获取成功: {len(i2v_system_prompt)} 字符")
        
        # 验证I2V提示词内容
        i2v_elements = ["视频描述", "动态内容", "运动信息"]
        found_i2v = [elem for elem in i2v_elements if elem in i2v_system_prompt]
        print(f"✅ I2V包含关键元素: {found_i2v}")
        
        return True
        
    except Exception as e:
        print(f"❌ Wan2.2功能测试失败: {e}")
        return False

def test_model_selection_logic():
    """测试模型选择逻辑"""
    print("\n🔄 测试模型选择逻辑...")
    
    # 模拟应用中的模型选择逻辑
    model_types = ["SiliconFlow API", "DashScope API", "Wan2.1", "Wan2.2"]
    
    for model_type in model_types:
        print(f"  测试 {model_type}...")
        
        if model_type == "Wan2.2":
            # 测试Wan2.2的模式选择
            modes = ["T2V (Text to Video)", "I2V (Image to Video)"]
            for mode in modes:
                if mode.startswith("T2V"):
                    wan_mode_type = "T2V"
                    is_vl = False
                elif mode.startswith("I2V"):
                    wan_mode_type = "I2V"
                    is_vl = True
                
                print(f"    ✅ {mode} -> 模式:{wan_mode_type}, 视觉:{is_vl}")
        
        elif model_type == "Wan2.1":
            # Wan2.1只有一种模式
            model_name = "wan2.1"
            print(f"    ✅ Wan2.1 -> 模型:{model_name}")
        
        else:
            print(f"    ✅ {model_type} -> 标准API模式")
    
    return True

def test_examples_and_config():
    """测试示例和配置"""
    print("\n📚 测试示例和配置...")
    
    try:
        from config import EXAMPLES, MODEL_CONFIG
        
        # 测试Wan2.1示例
        if "wan2.1" in EXAMPLES:
            wan21_examples = EXAMPLES["wan2.1"]
            print(f"✅ Wan2.1示例数量: {len(wan21_examples)}")
            for i, example in enumerate(wan21_examples):
                print(f"  示例{i+1}: {example['prompt'][:30]}...")
        
        # 测试Wan2.2示例
        if "wan2.2" in EXAMPLES:
            wan22_examples = EXAMPLES["wan2.2"]
            t2v_count = len(wan22_examples.get("T2V", []))
            i2v_count = len(wan22_examples.get("I2V", []))
            print(f"✅ Wan2.2示例 - T2V: {t2v_count}, I2V: {i2v_count}")
        
        # 测试模型配置
        for model in ["wan2.1", "wan2.2"]:
            if model in MODEL_CONFIG:
                config = MODEL_CONFIG[model]
                print(f"✅ {model}配置: {config.get('description', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例和配置测试失败: {e}")
        return False

def test_import_consistency():
    """测试导入一致性"""
    print("\n🔗 测试导入一致性...")
    
    try:
        # 测试app.py中的导入
        from prompt_extend import (
            DashScopePromptExpander,
            SiliconFlowPromptExpander,
            WanPromptExpander,
            WAN21_SYS_PROMPT
        )
        print("✅ app.py所需的所有类都可导入")
        
        # 测试config.py的一致性
        from config import get_env_config, MODEL_CONFIG, EXAMPLES
        env_config = get_env_config()
        
        # 验证API密钥配置
        if "siliconflow_api_key" in env_config:
            print("✅ SiliconFlow API密钥配置可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入一致性测试失败: {e}")
        return False

def run_functional_tests():
    """运行功能测试"""
    print("🧪 端到端功能测试")
    print("=" * 60)
    
    tests = [
        ("Wan2.2功能测试", test_wan22_functionality),
        ("模型选择逻辑", test_model_selection_logic),
        ("示例和配置测试", test_examples_and_config),
        ("导入一致性测试", test_import_consistency)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"🔬 {test_name}")
        print('='*40)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
                failed += 1
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
            failed += 1
    
    print(f"\n{'='*60}")
    print(f"📊 功能测试结果")
    print(f"{'='*60}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 成功率: {passed/(passed+failed)*100:.1f}%")
    
    return failed == 0

if __name__ == "__main__":
    success = run_functional_tests()
    print(f"\n{'🎉 功能测试完成！' if success else '⚠️ 发现问题，请检查'}")