"""
配置文件 - 智能Prompt优化器
"""

import os
from pathlib import Path

# 应用配置
APP_CONFIG = {
    "title": "🎨 智能Prompt优化器",
    "description": "支持文本、图片及多模态输入的智能Prompt扩展与优化工具",
    "version": "1.0.0",
    "author": "Prompt Optimizer Team"
}

# Streamlit配置
STREAMLIT_CONFIG = {
    "page_title": "智能Prompt优化器",
    "page_icon": "🎨",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# 模型配置
MODEL_CONFIG = {
    "dashscope": {
        "text_models": ["qwen-plus", "qwen-turbo"],
        "vision_models": ["qwen-vl-max", "qwen-vl-plus"],
        "default_text": "qwen-plus",
        "default_vision": "qwen-vl-max"
    },
    "siliconflow": {
        "text_models": [
            "deepseek-ai/DeepSeek-V3",
            "Qwen/Qwen2.5-72B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct"
        ],
        "vision_models": [
            "Qwen/Qwen2.5-VL-72B-Instruct",
            "Qwen/Qwen2.5-VL-32B-Instruct",
            "Qwen/Qwen2.5-VL-7B-Instruct"
        ],
        "default_text": "deepseek-ai/DeepSeek-V3",
        "default_vision": "Qwen/Qwen2.5-VL-72B-Instruct"
    },
    "wan2.1": {
        "description": "Wan2.1视频生成模型，生成正向和负向Prompt对",
        "model": "deepseek-ai/DeepSeek-V3"
    },
    "wan2.2": {
        "modes": ["T2V", "I2V"],
        "description": "Wan2.2视频生成模型，T2V模式处理纯文本，I2V模式处理图像+文本",
        "t2v_model": "deepseek-ai/DeepSeek-V3",
        "i2v_model": "Qwen/Qwen2.5-VL-72B-Instruct"
    }
}

# 文件上传配置
UPLOAD_CONFIG = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "allowed_extensions": ["png", "jpg", "jpeg", "bmp", "gif"],
    "max_images": 5,
    "image_resize_threshold": 512 * 512
}

# API配置
API_CONFIG = {
    "dashscope": {
        "base_url": "https://dashscope.aliyuncs.com/api/v1",
        "retry_times": 3,
        "timeout": 60
    },
    "siliconflow": {
        "base_url": "https://api.siliconflow.cn/v1/chat/completions",
        "retry_times": 3,
        "timeout": 60,
        "default_api_key": "sk-usbplpzkegbzkiiasrffeuatvrowxqeyihoboqxpsobpbccy",
        "text_models": [
            "deepseek-ai/DeepSeek-V3",
            "Qwen/Qwen2.5-72B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct"
        ],
        "vision_models": [
            "Qwen/Qwen2.5-VL-72B-Instruct",
            "Qwen/Qwen2.5-VL-32B-Instruct",
            "Qwen/Qwen2.5-VL-7B-Instruct"
        ]
    }
}

# 示例配置 - 用户输入示例，非系统提示词
EXAMPLES = {
    # 说明：以下是用户输入示例，帮助用户了解如何使用不同模式
    # 这些不是系统内部的提示词，而是建议用户尝试的输入内容
    "纯文本": [
        {
            "prompt": "夏日海滩度假风格，一只戴着墨镜的白色猫咪坐在冲浪板上",
            "description": "简单的文本描述，系统会自动扩展细节"
        },
        {
            "prompt": "雨夜城市街头，霓虹灯倒映在湿润的地面上",
            "description": "电影级的氛围描述，适合制作视频内容"
        },
        {
            "prompt": "古典园林，春风吹过桃花满树",
            "description": "中国古典风格的诗意描述"
        }
    ],
    "单图片+文本": [
        {
            "prompt": "日系小清新胶片写真，根据图片中的人物和场景，展现自然温馨的生活画面",
            "description": "结合上传的图片内容进行专业级Prompt优化"
        },
        {
            "prompt": "电影级构图，参考图片风格和元素，创作具有电影感的视觉叙事",
            "description": "自动识别图片风格并生成匹配的高质量Prompt"
        }
    ],
    "多图片+文本": [
        {
            "prompt": "无人机拍摄，镜头快速推进然后拉远至全景俯瞰",
            "description": "结合多张图片（如视频首尾帧）进行动态描述"
        },
        {
            "prompt": "镜头从近景慢慢拉远，展现完整的场景变化",
            "description": "描述镜头运动和场景转换的动态过程"
        }
    ],
    "wan2.1": [
        {
            "prompt": "一个宁静的海港，停满了游艇，水面清澈透蓝",
            "description": "Wan2.1模式：生成正向和负向Prompt对，专为视频生成优化"
        },
        {
            "prompt": "日落时分的城市天际线，灯火开始点亮",
            "description": "Wan2.1模式：场景描述，系统会生成详细的正负向提示词"
        }
    ],
    "wan2.2": {
        "T2V": [
            {
                "prompt": "一只可爱的小猫在阳光明媚的花园里玩耍",
                "description": "T2V模式：纯文本输入，添加电影级美学元素"
            },
            {
                "prompt": "日落时分，海浪轻拍沙滩，远山如黛",
                "description": "T2V模式：自然风景描述，增强视觉效果"
            }
        ],
        "I2V": [
            {
                "prompt": "根据图片内容，描述人物的动作和表情变化",
                "description": "I2V模式：基于图像内容优化动态描述"
            },
            {
                "prompt": "镜头运动，展现画面中的动态元素",
                "description": "I2V模式：结合图像生成视频描述"
            }
        ]
    }
}

# 获取环境变量配置
def get_env_config():
    """获取环境变量配置"""
    return {
        "dash_api_key": os.environ.get("DASH_API_KEY"),
        "dash_api_url": os.environ.get("DASH_API_URL", API_CONFIG["dashscope"]["base_url"]),
        "siliconflow_api_key": os.environ.get("SILICONFLOW_API_KEY", API_CONFIG["siliconflow"]["default_api_key"]),
        "cuda_visible_devices": os.environ.get("CUDA_VISIBLE_DEVICES", "0")
    }

# 获取当前目录下的模型路径
def get_local_models():
    """扫描本地模型目录"""
    models_dir = Path("./models")
    if not models_dir.exists():
        return []
    
    local_models = []
    for item in models_dir.iterdir():
        if item.is_dir() and any(file.name == "config.json" for file in item.iterdir()):
            local_models.append(str(item))
    
    return local_models 