#!/usr/bin/env python3
"""
全面测试智能Prompt优化器的所有功能
包括：SiliconFlow API, DashScope API, Wan2.1, Wan2.2
"""

import sys
import os
import time
import traceback
from datetime import datetime

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    try:
        # 基础导入
        import streamlit as st
        print("✅ Streamlit导入成功")
        
        # 应用导入
        import app
        print("✅ app.py导入成功")
        
        import config
        print("✅ config.py导入成功")
        
        # 核心功能导入
        from prompt_extend import (
            DashScopePromptExpander,
            SiliconFlowPromptExpander, 
            WanPromptExpander,
            WAN21_SYS_PROMPT,
            T2V_A14B_ZH_SYS_PROMPT,
            T2V_A14B_EN_SYS_PROMPT,
            I2V_A14B_ZH_SYS_PROMPT,
            I2V_A14B_EN_SYS_PROMPT
        )
        print("✅ 所有prompt_extend类和提示词导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_system_prompts():
    """测试所有系统提示词"""
    print("\n📝 测试系统提示词...")
    
    try:
        from prompt_extend import (
            WAN21_SYS_PROMPT,
            T2V_A14B_ZH_SYS_PROMPT,
            T2V_A14B_EN_SYS_PROMPT,
            I2V_A14B_ZH_SYS_PROMPT,
            I2V_A14B_EN_SYS_PROMPT,
            SYSTEM_PROMPT_TYPES
        )
        
        # 验证所有提示词长度
        prompts = {
            "WAN21_SYS_PROMPT": WAN21_SYS_PROMPT,
            "T2V_A14B_ZH_SYS_PROMPT": T2V_A14B_ZH_SYS_PROMPT,
            "T2V_A14B_EN_SYS_PROMPT": T2V_A14B_EN_SYS_PROMPT,
            "I2V_A14B_ZH_SYS_PROMPT": I2V_A14B_ZH_SYS_PROMPT,
            "I2V_A14B_EN_SYS_PROMPT": I2V_A14B_EN_SYS_PROMPT
        }
        
        for name, prompt in prompts.items():
            if len(prompt) > 100:
                print(f"✅ {name}: {len(prompt)} 字符")
            else:
                print(f"❌ {name}: 长度过短 ({len(prompt)} 字符)")
        
        # 验证SYSTEM_PROMPT_TYPES映射
        print(f"✅ SYSTEM_PROMPT_TYPES包含 {len(SYSTEM_PROMPT_TYPES)} 个映射")
        
        return True
    except Exception as e:
        print(f"❌ 系统提示词测试失败: {e}")
        return False

def test_wan21_expander():
    """测试Wan2.1扩展器"""
    print("\n🎬 测试Wan2.1扩展器...")
    
    try:
        from prompt_extend import SiliconFlowPromptExpander, WAN21_SYS_PROMPT
        
        # 创建Wan2.1风格的扩展器（使用SiliconFlow + 特殊提示词）
        expander = SiliconFlowPromptExpander(
            api_key="sk-test-key",
            model_name="deepseek-ai/DeepSeek-V3",
            is_vl=False
        )
        
        # 验证能否获取系统提示词
        print(f"✅ WAN21_SYS_PROMPT可访问，长度: {len(WAN21_SYS_PROMPT)}")
        
        # 验证关键词
        keywords = ["Wan2.1", "Positive Prompt", "Negative Prompt"]
        for keyword in keywords:
            if keyword in WAN21_SYS_PROMPT:
                print(f"✅ 包含关键词: {keyword}")
            else:
                print(f"⚠️  关键词可能有变体: {keyword}")
        
        return True
    except Exception as e:
        print(f"❌ Wan2.1扩展器测试失败: {e}")
        return False

def test_wan22_expander():
    """测试Wan2.2扩展器"""
    print("\n🎥 测试Wan2.2扩展器...")
    
    try:
        from prompt_extend import WanPromptExpander
        
        # 测试T2V模式
        print("  📹 测试T2V模式...")
        t2v_expander = WanPromptExpander(
            api_key="sk-test-key",
            model_name="wan2.2",
            mode="T2V",
            is_vl=False
        )
        
        t2v_prompt = t2v_expander.decide_system_prompt(tar_lang="zh")
        print(f"  ✅ T2V中文提示词: {len(t2v_prompt)} 字符")
        
        t2v_prompt_en = t2v_expander.decide_system_prompt(tar_lang="en")
        print(f"  ✅ T2V英文提示词: {len(t2v_prompt_en)} 字符")
        
        # 测试I2V模式
        print("  🖼️ 测试I2V模式...")
        i2v_expander = WanPromptExpander(
            api_key="sk-test-key",
            model_name="wan2.2",
            mode="I2V",
            is_vl=True
        )
        
        i2v_prompt = i2v_expander.decide_system_prompt(tar_lang="zh")
        print(f"  ✅ I2V中文提示词: {len(i2v_prompt)} 字符")
        
        i2v_prompt_en = i2v_expander.decide_system_prompt(tar_lang="en")
        print(f"  ✅ I2V英文提示词: {len(i2v_prompt_en)} 字符")
        
        return True
    except Exception as e:
        print(f"❌ Wan2.2扩展器测试失败: {e}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置文件"""
    print("\n⚙️ 测试配置文件...")
    
    try:
        from config import MODEL_CONFIG, EXAMPLES, get_env_config
        
        # 验证模型配置
        expected_models = ["dashscope", "siliconflow", "wan2.1", "wan2.2"]
        for model in expected_models:
            if model in MODEL_CONFIG:
                print(f"✅ {model} 配置存在")
            else:
                print(f"❌ 缺少 {model} 配置")
        
        # 验证示例配置
        expected_examples = ["纯文本", "单图片+文本", "多图片+文本", "wan2.1", "wan2.2"]
        for example in expected_examples:
            if example in EXAMPLES:
                print(f"✅ {example} 示例存在")
            else:
                print(f"❌ 缺少 {example} 示例")
        
        # 验证环境配置
        env_config = get_env_config()
        print(f"✅ 环境配置获取成功: {len(env_config)} 个配置项")
        
        return True
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_app_logic():
    """测试应用逻辑（不启动Streamlit）"""
    print("\n🖥️ 测试应用逻辑...")
    
    try:
        # 模拟不同的模型类型选择
        model_types = ["SiliconFlow API", "DashScope API", "Wan2.1", "Wan2.2"]
        
        for model_type in model_types:
            print(f"  🔄 测试 {model_type} 逻辑...")
            
            # 这里我们只测试导入和基本逻辑，不实际调用API
            if model_type == "Wan2.2":
                # 测试Wan2.2模式逻辑
                modes = ["T2V (Text to Video)", "I2V (Image to Video)"]
                for mode in modes:
                    mode_type = "T2V" if mode.startswith("T2V") else "I2V"
                    print(f"    ✅ {mode_type}模式解析正确")
            
            elif model_type == "Wan2.1":
                print(f"    ✅ Wan2.1模式逻辑正确")
        
        return True
    except Exception as e:
        print(f"❌ 应用逻辑测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖项"""
    print("\n📦 测试依赖项...")
    
    dependencies = [
        ("streamlit", "Streamlit Web框架"),
        ("PIL", "图像处理"),
        ("requests", "HTTP请求"),
        ("json", "JSON处理"),
        ("os", "系统操作"),
        ("sys", "系统接口"),
        ("tempfile", "临时文件"),
        ("base64", "Base64编码"),
        ("datetime", "日期时间")
    ]
    
    success_count = 0
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module}: {description}")
            success_count += 1
        except ImportError:
            print(f"❌ {module}: {description} - 缺失")
    
    return success_count == len(dependencies)

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 智能Prompt优化器 - 全面测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    tests = [
        ("依赖项检查", test_dependencies),
        ("导入测试", test_imports),
        ("系统提示词测试", test_system_prompts),
        ("Wan2.1扩展器测试", test_wan21_expander),
        ("Wan2.2扩展器测试", test_wan22_expander),
        ("配置文件测试", test_config),
        ("应用逻辑测试", test_app_logic)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)
        
        try:
            if test_func():
                print(f"✅ {test_name} - 通过")
                passed += 1
            else:
                print(f"❌ {test_name} - 失败")
                failed += 1
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
            failed += 1
        
        time.sleep(0.5)  # 短暂停顿，便于阅读
    
    # 测试结果汇总
    print(f"\n{'='*80}")
    print(f"📊 测试结果汇总")
    print(f"{'='*80}")
    print(f"✅ 通过: {passed}")
    print(f"❌ 失败: {failed}")
    print(f"📈 通过率: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print(f"\n🎉 所有测试通过！系统准备就绪！")
        print(f"🚀 可以安全启动应用：")
        print(f"   python run.py --port 8080")
        print(f"   或者")
        print(f"   streamlit run app.py --server.port 8080")
    else:
        print(f"\n⚠️  发现 {failed} 个问题，建议修复后再启动应用")
    
    return failed == 0

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)